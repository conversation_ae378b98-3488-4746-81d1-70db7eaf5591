# 基于 sunfa.md 文档的指标计算实现

## 概述

根据 `sunfa.md` 文档的要求，我们重新设计了报告生成器中的分类指标计算方法，将"none"类别视为负例，其他类别视为正例，专注于评估正向意图识别能力。

## 核心变更

### 1. 指标计算方法的重新定义

#### 原有方法问题
- 将所有类别（包括"none"）平等对待
- 使用传统的多分类指标计算方式
- 没有区分"正向意图"和"none"类别的特殊性

#### 新方法优势
- 将"none"视为负例，其他类别视为正例
- 重新定义 TP、FP、FN、TN 的计算方式
- 专注于正向意图识别能力的评估

### 2. 具体计算公式

根据 sunfa.md 文档，重新定义了关键指标：

#### TP (True Positive)
```
总 TP = Σ C(i, i) for i = 1 to K
```
所有正向类别的正确预测之和

#### FP (False Positive)  
```
总 FP = Σ [Σ C(j, i) for j = 1 to K, j ≠ i] + Σ C(none, i) for i = 1 to K
```
包括：
- 真实是none但被预测为正向类
- 真实是正向类A但被预测为正向类B

#### FN (False Negative)
```
总 FN = Σ C(i, none) for i = 1 to K
```
真实正向意图被误判为none的样本总数

#### TN (True Negative)
```
总 TN = C(none, none)
```
正确预测为none的数量

### 3. 核心指标公式

#### 召回率 (Recall)
```
Recall = 总 TP / (总 TP + 总 FN)
```
在所有真实存在的正向意图样本中，模型成功识别出了多少

#### 精确率 (Precision)
```
Precision = 总 TP / (总 TP + 总 FP)
```
在模型预测为某个正向意图的所有样本中，有多少预测是正确的

#### F1分数
```
F1 = 2 * (Precision * Recall) / (Precision + Recall)
```
精确率和召回率的调和平均数，是评估正向意图识别能力的最佳综合指标

#### 准确率 (Accuracy)
```
Accuracy = (总 TP + 总 TN) / 总样本数
```
⚠️ **谨慎使用**：可能受类别不平衡影响，无法准确反映正向意图识别能力

## 修改的文件

### 1. `src/ai_model_evaluation/services/evaluation_report_generator.py`
- 修改了 `_calculate_detailed_classification_metrics` 方法
- 重新实现了 TP、FP、FN、TN 的计算逻辑
- 添加了正向类别识别和分离逻辑
- 增加了警告信息和方法说明

### 2. `src/ai_model_evaluation/services/analysis.py`
- 修改了 `_calculate_detailed_classification_metrics` 方法
- 保持与报告生成器一致的计算逻辑
- 确保接口兼容性

## 新增功能

### 1. 自动识别正向类别和none类别
```python
# 识别正向类别（非"none"的类别）
positive_classes = [label for label in all_labels if label.lower() not in ['none', '', 'null', 'na']]
none_class = 'none' if 'none' in all_labels else None

# 如果没有找到明确的"none"类，尝试其他可能的表示
if none_class is None:
    for label in all_labels:
        if label.lower() in ['', 'null', 'na', 'unknown', '无', '空']:
            none_class = label
            positive_classes = [l for l in all_labels if l != label]
            break
```

### 2. 增强的返回信息
新的返回结果包含：
- 核心指标（precision, recall, f1_score）
- 正向类别列表和none类别标识
- 详细的TP/FP/FN/TN统计
- 每个类别的is_positive_class标识
- 准确率警告信息
- 方法说明

### 3. 宏平均和加权平均（仅针对正向类别）
```python
# 计算宏平均（仅针对正向类别）
if positive_classes:
    macro_precision = sum(per_class_metrics[cls]["precision"] for cls in positive_classes) / len(positive_classes)
    macro_recall = sum(per_class_metrics[cls]["recall"] for cls in positive_classes) / len(positive_classes)
    macro_f1 = sum(per_class_metrics[cls]["f1_score"] for cls in positive_classes) / len(positive_classes)
```

## 验证结果

使用 sunfa.md 文档中的示例数据进行验证：

### 期望结果
- 召回率: 0.800
- 精确率: 0.809
- F1分数: 0.805
- 准确率: 0.773

### 实际结果
- ✅ 召回率: 0.800
- ✅ 精确率: 0.809
- ✅ F1分数: 0.805
- ✅ 准确率: 0.773

所有指标完全符合文档期望！

## 使用建议

### 1. 重点关注的指标
1. **F1分数** - 最推荐的综合指标
2. **召回率** - 评估避免漏报正向意图的能力
3. **精确率** - 评估预测正向意图的可靠性

### 2. 谨慎使用的指标
- **准确率** - 可能受类别不平衡误导，无法准确反映正向意图识别能力

### 3. 分析建议
- 优先分析宏平均指标（平等对待每个正向类别）
- 关注每个正向类别的具体表现
- 分析"none"导致的误报情况

## 示例代码

参见 `example_usage.py` 文件，展示了如何使用修改后的报告生成器进行模型评估和对比。

## 测试

运行 `test_sunfa_metrics.py` 可以验证计算逻辑的正确性：

```bash
poetry run python test_sunfa_metrics.py
```

该测试使用 sunfa.md 文档中的示例数据，验证所有指标计算是否符合预期。
