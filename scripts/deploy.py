#!/usr/bin/env python3
"""部署脚本
用于自动化部署AI模型评估系统的优化版本
"""

import os
import sys
import subprocess
import shutil
import json
import yaml
import time
from pathlib import Path
from typing import Dict, List, Optional, Any
import argparse
from loguru import logger
from dataclasses import dataclass


@dataclass
class DeploymentConfig:
    """部署配置"""
    environment: str  # dev, staging, prod
    config_file: str
    optimization_level: str  # basic, intermediate, advanced, extreme
    enable_monitoring: bool = True
    enable_caching: bool = True
    enable_compression: bool = True
    backup_existing: bool = True
    run_tests: bool = True
    

class DeploymentManager:
    """部署管理器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.backup_dir = self.project_root / "backups"
        self.logs_dir = self.project_root / "logs"
        self.config_dir = self.project_root / "config"
        
        # 创建必要的目录
        self.backup_dir.mkdir(exist_ok=True)
        self.logs_dir.mkdir(exist_ok=True)
        self.config_dir.mkdir(exist_ok=True)
        
        # 部署环境配置
        self.environments = {
            'dev': {
                'config_file': 'config.yaml',
                'optimization_level': 'basic',
                'max_concurrent': 20,
                'enable_debug': True,
                'log_level': 'DEBUG'
            },
            'staging': {
                'config_file': 'config_optimized.yaml',
                'optimization_level': 'intermediate',
                'max_concurrent': 100,
                'enable_debug': False,
                'log_level': 'INFO'
            },
            'prod': {
                'config_file': 'config_optimized.yaml',
                'optimization_level': 'extreme',
                'max_concurrent': 300,
                'enable_debug': False,
                'log_level': 'WARNING'
            }
        }
    
    def check_prerequisites(self) -> bool:
        """检查部署前置条件"""
        logger.info("检查部署前置条件...")
        
        # 检查Python版本
        python_version = sys.version_info
        if python_version < (3, 8):
            logger.error(f"Python版本过低: {python_version}, 需要3.8+")
            return False
        
        logger.info(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
        
        # 检查必要的包
        required_packages = [
            'asyncio', 'aiohttp', 'pydantic', 'loguru', 'yaml', 'pandas'
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                __import__(package)
                logger.debug(f"✓ {package}")
            except ImportError:
                missing_packages.append(package)
                logger.warning(f"✗ {package}")
        
        if missing_packages:
            logger.error(f"缺少必要的包: {missing_packages}")
            logger.info("请运行: pip install -r requirements.txt")
            return False
        
        # 检查项目文件结构
        required_files = [
            'src/ai_model_evaluation/optimized_main.py',
            'src/ai_model_evaluation/services/enhanced_api_client.py',
            'src/ai_model_evaluation/services/smart_batch_processor.py',
            'src/ai_model_evaluation/services/async_file_handler.py',
            'src/ai_model_evaluation/services/performance_monitor.py'
        ]
        
        missing_files = []
        for file_path in required_files:
            full_path = self.project_root / file_path
            if not full_path.exists():
                missing_files.append(file_path)
                logger.warning(f"✗ {file_path}")
            else:
                logger.debug(f"✓ {file_path}")
        
        if missing_files:
            logger.error(f"缺少必要的文件: {missing_files}")
            return False
        
        # 检查配置文件
        config_files = ['config.yaml', 'config_optimized.yaml']
        for config_file in config_files:
            config_path = self.project_root / config_file
            if not config_path.exists():
                logger.warning(f"配置文件不存在: {config_file}")
            else:
                try:
                    with open(config_path, 'r', encoding='utf-8') as f:
                        yaml.safe_load(f)
                    logger.debug(f"✓ {config_file}")
                except Exception as e:
                    logger.error(f"配置文件格式错误 {config_file}: {e}")
                    return False
        
        logger.info("前置条件检查完成")
        return True
    
    def backup_existing_deployment(self, deployment_config: DeploymentConfig) -> Optional[str]:
        """备份现有部署"""
        if not deployment_config.backup_existing:
            return None
        
        logger.info("备份现有部署...")
        
        timestamp = int(time.time())
        backup_name = f"backup_{deployment_config.environment}_{timestamp}"
        backup_path = self.backup_dir / backup_name
        
        try:
            # 创建备份目录
            backup_path.mkdir(exist_ok=True)
            
            # 备份配置文件
            config_backup = backup_path / "config"
            config_backup.mkdir(exist_ok=True)
            
            for config_file in ['config.yaml', 'config_optimized.yaml']:
                src_path = self.project_root / config_file
                if src_path.exists():
                    shutil.copy2(src_path, config_backup / config_file)
                    logger.debug(f"备份配置文件: {config_file}")
            
            # 备份日志文件
            if self.logs_dir.exists():
                logs_backup = backup_path / "logs"
                shutil.copytree(self.logs_dir, logs_backup, dirs_exist_ok=True)
                logger.debug("备份日志文件")
            
            # 备份运行时数据
            runtime_dirs = ['cache', 'metrics', 'temp']
            for dir_name in runtime_dirs:
                src_dir = self.project_root / dir_name
                if src_dir.exists():
                    dst_dir = backup_path / dir_name
                    shutil.copytree(src_dir, dst_dir, dirs_exist_ok=True)
                    logger.debug(f"备份运行时目录: {dir_name}")
            
            # 创建备份信息文件
            backup_info = {
                'timestamp': timestamp,
                'environment': deployment_config.environment,
                'backup_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                'python_version': f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
                'project_root': str(self.project_root)
            }
            
            with open(backup_path / "backup_info.json", 'w', encoding='utf-8') as f:
                json.dump(backup_info, f, indent=2, ensure_ascii=False)
            
            logger.info(f"备份完成: {backup_path}")
            return str(backup_path)
            
        except Exception as e:
            logger.error(f"备份失败: {e}")
            return None
    
    def install_dependencies(self) -> bool:
        """安装依赖"""
        logger.info("安装项目依赖...")
        
        try:
            # 检查是否有requirements.txt
            requirements_file = self.project_root / "requirements.txt"
            if not requirements_file.exists():
                logger.info("创建requirements.txt文件")
                self._create_requirements_file()
            
            # 使用uv安装依赖（如果可用）
            try:
                result = subprocess.run(
                    ['uv', 'pip', 'install', '-r', str(requirements_file)],
                    cwd=self.project_root,
                    capture_output=True,
                    text=True,
                    timeout=300
                )
                
                if result.returncode == 0:
                    logger.info("使用uv安装依赖成功")
                    return True
                else:
                    logger.warning(f"uv安装失败: {result.stderr}")
                    
            except (subprocess.TimeoutExpired, FileNotFoundError):
                logger.info("uv不可用，使用pip安装")
            
            # 使用pip安装依赖
            result = subprocess.run(
                [sys.executable, '-m', 'pip', 'install', '-r', str(requirements_file)],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=300
            )
            
            if result.returncode == 0:
                logger.info("依赖安装成功")
                return True
            else:
                logger.error(f"依赖安装失败: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"安装依赖时出错: {e}")
            return False
    
    def _create_requirements_file(self):
        """创建requirements.txt文件"""
        requirements = [
            "aiohttp>=3.8.0",
            "pydantic>=2.0.0",
            "loguru>=0.7.0",
            "PyYAML>=6.0",
            "pandas>=2.0.0",
            "numpy>=1.24.0",
            "asyncio-throttle>=1.0.0",
            "aiofiles>=23.0.0",
            "psutil>=5.9.0",
            "matplotlib>=3.7.0",
            "seaborn>=0.12.0",
            "fastapi>=0.100.0",
            "uvicorn>=0.23.0",
            "python-multipart>=0.0.6",
            "httpx>=0.24.0",
            "tenacity>=8.2.0",
            "cachetools>=5.3.0",
            "prometheus-client>=0.17.0",
            "structlog>=23.1.0"
        ]
        
        requirements_file = self.project_root / "requirements.txt"
        with open(requirements_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(requirements))
        
        logger.info(f"创建requirements.txt: {len(requirements)}个依赖")
    
    def configure_environment(self, deployment_config: DeploymentConfig) -> bool:
        """配置部署环境"""
        logger.info(f"配置{deployment_config.environment}环境...")
        
        try:
            env_config = self.environments.get(deployment_config.environment)
            if not env_config:
                logger.error(f"未知的环境: {deployment_config.environment}")
                return False
            
            # 更新配置文件
            config_path = self.project_root / deployment_config.config_file
            if not config_path.exists():
                logger.error(f"配置文件不存在: {deployment_config.config_file}")
                return False
            
            # 读取现有配置
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 更新环境特定配置
            if 'performance' not in config:
                config['performance'] = {}
            
            config['performance'].update({
                'global_max_concurrent': env_config['max_concurrent'],
                'enable_debug': env_config['enable_debug'],
                'log_level': env_config['log_level'],
                'optimization_level': deployment_config.optimization_level,
                'enable_monitoring': deployment_config.enable_monitoring,
                'enable_caching': deployment_config.enable_caching,
                'enable_compression': deployment_config.enable_compression
            })
            
            # 根据优化级别调整配置
            self._apply_optimization_settings(config, deployment_config.optimization_level)
            
            # 保存更新后的配置
            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
            
            logger.info(f"环境配置完成: {deployment_config.environment}")
            return True
            
        except Exception as e:
            logger.error(f"配置环境失败: {e}")
            return False
    
    def _apply_optimization_settings(self, config: Dict[str, Any], optimization_level: str):
        """应用优化设置"""
        optimization_settings = {
            'basic': {
                'global_max_concurrent': 20,
                'batch_strategy': 'fixed_size',
                'min_batch_size': 5,
                'max_batch_size': 10,
                'enable_adaptive_concurrency': True,
                'enable_memory_optimization': True,
                'memory_threshold_mb': 500
            },
            'intermediate': {
                'global_max_concurrent': 100,
                'batch_strategy': 'adaptive_size',
                'min_batch_size': 10,
                'max_batch_size': 25,
                'enable_adaptive_concurrency': True,
                'enable_memory_optimization': True,
                'memory_threshold_mb': 1000,
                'enable_request_cache': True,
                'cache_ttl_seconds': 1800
            },
            'advanced': {
                'global_max_concurrent': 200,
                'batch_strategy': 'hybrid',
                'min_batch_size': 20,
                'max_batch_size': 50,
                'enable_adaptive_concurrency': True,
                'enable_memory_optimization': True,
                'memory_threshold_mb': 2000,
                'enable_request_cache': True,
                'cache_ttl_seconds': 3600,
                'enable_streaming': True,
                'enable_compression': True
            },
            'extreme': {
                'global_max_concurrent': 300,
                'batch_strategy': 'memory_aware',
                'min_batch_size': 30,
                'max_batch_size': 100,
                'enable_adaptive_concurrency': True,
                'enable_memory_optimization': True,
                'memory_threshold_mb': 4000,
                'enable_request_cache': True,
                'cache_ttl_seconds': 7200,
                'enable_streaming': True,
                'enable_compression': True,
                'enable_detailed_metrics': True,
                'enable_predictive_caching': True
            }
        }
        
        settings = optimization_settings.get(optimization_level, optimization_settings['basic'])
        
        if 'performance' not in config:
            config['performance'] = {}
        
        config['performance'].update(settings)
        
        logger.info(f"应用{optimization_level}级别优化设置")
    
    def run_tests(self, deployment_config: DeploymentConfig) -> bool:
        """运行测试"""
        if not deployment_config.run_tests:
            return True
        
        logger.info("运行部署测试...")
        
        try:
            # 运行单元测试
            test_files = [
                'tests/test_optimized_main.py',
                'tests/test_enhanced_api_client.py',
                'tests/test_smart_batch_processor.py',
                'tests/test_async_file_handler.py'
            ]
            
            for test_file in test_files:
                test_path = self.project_root / test_file
                if test_path.exists():
                    logger.info(f"运行测试: {test_file}")
                    result = subprocess.run(
                        [sys.executable, '-m', 'pytest', str(test_path), '-v'],
                        cwd=self.project_root,
                        capture_output=True,
                        text=True,
                        timeout=120
                    )
                    
                    if result.returncode != 0:
                        logger.error(f"测试失败 {test_file}: {result.stderr}")
                        return False
                    else:
                        logger.debug(f"测试通过: {test_file}")
                else:
                    logger.warning(f"测试文件不存在: {test_file}")
            
            # 运行集成测试
            logger.info("运行集成测试")
            integration_test = self.project_root / "tests" / "test_integration.py"
            if integration_test.exists():
                result = subprocess.run(
                    [sys.executable, str(integration_test)],
                    cwd=self.project_root,
                    capture_output=True,
                    text=True,
                    timeout=300
                )
                
                if result.returncode != 0:
                    logger.error(f"集成测试失败: {result.stderr}")
                    return False
                else:
                    logger.info("集成测试通过")
            
            logger.info("所有测试通过")
            return True
            
        except Exception as e:
            logger.error(f"运行测试时出错: {e}")
            return False
    
    def setup_monitoring(self, deployment_config: DeploymentConfig) -> bool:
        """设置监控"""
        if not deployment_config.enable_monitoring:
            return True
        
        logger.info("设置监控系统...")
        
        try:
            # 创建监控目录
            monitoring_dir = self.project_root / "monitoring"
            monitoring_dir.mkdir(exist_ok=True)
            
            # 创建监控配置
            monitoring_config = {
                'metrics': {
                    'enabled': True,
                    'port': 9090,
                    'path': '/metrics',
                    'retention_hours': 24
                },
                'logging': {
                    'level': self.environments[deployment_config.environment]['log_level'],
                    'file': str(self.logs_dir / f"{deployment_config.environment}.log"),
                    'max_size': '100MB',
                    'backup_count': 5
                },
                'alerts': {
                    'enabled': True,
                    'thresholds': {
                        'error_rate': 5.0,  # %
                        'response_time': 2.0,  # seconds
                        'cpu_usage': 80.0,  # %
                        'memory_usage': 85.0  # %
                    }
                }
            }
            
            monitoring_config_file = monitoring_dir / "monitoring.yaml"
            with open(monitoring_config_file, 'w', encoding='utf-8') as f:
                yaml.dump(monitoring_config, f, default_flow_style=False, allow_unicode=True, indent=2)
            
            # 创建监控启动脚本
            monitoring_script = monitoring_dir / "start_monitoring.py"
            with open(monitoring_script, 'w', encoding='utf-8') as f:
                f.write(self._get_monitoring_script_content())
            
            monitoring_script.chmod(0o755)
            
            logger.info("监控系统设置完成")
            return True
            
        except Exception as e:
            logger.error(f"设置监控失败: {e}")
            return False
    
    def _get_monitoring_script_content(self) -> str:
        """获取监控脚本内容"""
        return '''#!/usr/bin/env python3
"""监控系统启动脚本"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.ai_model_evaluation.services.performance_monitor import (
    create_performance_monitor
)

async def main():
    """启动监控系统"""
    monitor = create_performance_monitor(
        enable_system_monitoring=True,
        system_monitor_interval=1.0,
        metrics_retention_hours=24
    )
    
    try:
        await monitor.start()
        print("监控系统已启动")
        
        # 保持运行
        while True:
            await asyncio.sleep(60)
            
    except KeyboardInterrupt:
        print("停止监控系统")
    finally:
        await monitor.stop()

if __name__ == "__main__":
    asyncio.run(main())
'''
    
    def create_startup_scripts(self, deployment_config: DeploymentConfig) -> bool:
        """创建启动脚本"""
        logger.info("创建启动脚本...")
        
        try:
            scripts_dir = self.project_root / "scripts"
            scripts_dir.mkdir(exist_ok=True)
            
            # 创建主启动脚本
            startup_script = scripts_dir / f"start_{deployment_config.environment}.py"
            with open(startup_script, 'w', encoding='utf-8') as f:
                f.write(self._get_startup_script_content(deployment_config))
            
            startup_script.chmod(0o755)
            
            # 创建停止脚本
            stop_script = scripts_dir / f"stop_{deployment_config.environment}.py"
            with open(stop_script, 'w', encoding='utf-8') as f:
                f.write(self._get_stop_script_content(deployment_config))
            
            stop_script.chmod(0o755)
            
            # 创建健康检查脚本
            health_script = scripts_dir / f"health_check_{deployment_config.environment}.py"
            with open(health_script, 'w', encoding='utf-8') as f:
                f.write(self._get_health_check_script_content(deployment_config))
            
            health_script.chmod(0o755)
            
            logger.info("启动脚本创建完成")
            return True
            
        except Exception as e:
            logger.error(f"创建启动脚本失败: {e}")
            return False
    
    def _get_startup_script_content(self, deployment_config: DeploymentConfig) -> str:
        """获取启动脚本内容"""
        return f'''#!/usr/bin/env python3
"""AI模型评估系统启动脚本 - {deployment_config.environment}环境"""

import asyncio
import sys
import signal
from pathlib import Path
from loguru import logger

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.ai_model_evaluation.optimized_main import (
    OptimizedAIModelEvaluator,
    OptimizedEvaluationConfig,
    BatchStrategy
)

# 全局变量
evaluator = None

def signal_handler(signum, frame):
    """信号处理器"""
    logger.info(f"接收到信号 {{signum}}，正在关闭系统...")
    if evaluator:
        asyncio.create_task(evaluator.shutdown())
    sys.exit(0)

async def main():
    """主函数"""
    global evaluator
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    logger.info("启动AI模型评估系统 - {deployment_config.environment}环境")
    
    try:
        # 创建优化配置
        optimization_config = OptimizedEvaluationConfig(
            global_max_concurrent={self.environments[deployment_config.environment]['max_concurrent']},
            enable_adaptive_concurrency=True,
            batch_strategy=BatchStrategy.HYBRID,
            enable_memory_optimization=True,
            enable_request_cache={str(deployment_config.enable_caching).lower()},
            enable_streaming=True,
            enable_compression={str(deployment_config.enable_compression).lower()},
            enable_detailed_metrics={str(deployment_config.enable_monitoring).lower()}
        )
        
        # 创建评估器
        async with OptimizedAIModelEvaluator(
            config_path="{deployment_config.config_file}",
            optimization_config=optimization_config,
            enable_monitoring={str(deployment_config.enable_monitoring).lower()},
            enable_system_monitoring=True
        ) as evaluator:
            
            logger.info("系统启动完成，等待请求...")
            
            # 保持运行
            while True:
                await asyncio.sleep(1)
                
    except Exception as e:
        logger.error(f"系统启动失败: {{e}}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
'''
    
    def _get_stop_script_content(self, deployment_config: DeploymentConfig) -> str:
        """获取停止脚本内容"""
        return f'''#!/usr/bin/env python3
"""AI模型评估系统停止脚本 - {deployment_config.environment}环境"""

import os
import signal
import psutil
from loguru import logger

def stop_system():
    """停止系统"""
    logger.info("正在停止AI模型评估系统...")
    
    # 查找相关进程
    processes = []
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            cmdline = ' '.join(proc.info['cmdline'] or [])
            if 'start_{deployment_config.environment}.py' in cmdline:
                processes.append(proc)
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    if not processes:
        logger.info("没有找到运行中的系统进程")
        return
    
    # 优雅停止
    for proc in processes:
        try:
            logger.info(f"发送SIGTERM信号到进程 {{proc.pid}}")
            proc.send_signal(signal.SIGTERM)
        except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
            logger.warning(f"无法停止进程 {{proc.pid}}: {{e}}")
    
    # 等待进程结束
    import time
    time.sleep(5)
    
    # 强制停止仍在运行的进程
    for proc in processes:
        try:
            if proc.is_running():
                logger.warning(f"强制停止进程 {{proc.pid}}")
                proc.kill()
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            pass
    
    logger.info("系统已停止")

if __name__ == "__main__":
    stop_system()
'''
    
    def _get_health_check_script_content(self, deployment_config: DeploymentConfig) -> str:
        """获取健康检查脚本内容"""
        return f'''#!/usr/bin/env python3
"""AI模型评估系统健康检查脚本 - {deployment_config.environment}环境"""

import asyncio
import sys
import time
from pathlib import Path
from loguru import logger

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.ai_model_evaluation.optimized_main import OptimizedAIModelEvaluator

async def health_check():
    """健康检查"""
    logger.info("开始健康检查...")
    
    try:
        # 创建评估器实例
        evaluator = OptimizedAIModelEvaluator(
            config_path="{deployment_config.config_file}"
        )
        
        # 检查系统状态
        health_status = await evaluator.get_health_status()
        
        if health_status.get('status') == 'healthy':
            logger.info("✓ 系统健康")
            print("HEALTHY")
            return 0
        else:
            logger.warning(f"✗ 系统异常: {{health_status}}")
            print("UNHEALTHY")
            return 1
            
    except Exception as e:
        logger.error(f"健康检查失败: {{e}}")
        print("ERROR")
        return 2

if __name__ == "__main__":
    exit_code = asyncio.run(health_check())
    sys.exit(exit_code)
'''
    
    def deploy(self, deployment_config: DeploymentConfig) -> bool:
        """执行部署"""
        logger.info(f"开始部署到{deployment_config.environment}环境")
        
        # 1. 检查前置条件
        if not self.check_prerequisites():
            logger.error("前置条件检查失败")
            return False
        
        # 2. 备份现有部署
        backup_path = self.backup_existing_deployment(deployment_config)
        if deployment_config.backup_existing and not backup_path:
            logger.error("备份失败")
            return False
        
        try:
            # 3. 安装依赖
            if not self.install_dependencies():
                logger.error("依赖安装失败")
                return False
            
            # 4. 配置环境
            if not self.configure_environment(deployment_config):
                logger.error("环境配置失败")
                return False
            
            # 5. 运行测试
            if not self.run_tests(deployment_config):
                logger.error("测试失败")
                return False
            
            # 6. 设置监控
            if not self.setup_monitoring(deployment_config):
                logger.error("监控设置失败")
                return False
            
            # 7. 创建启动脚本
            if not self.create_startup_scripts(deployment_config):
                logger.error("启动脚本创建失败")
                return False
            
            # 8. 创建部署信息文件
            self._create_deployment_info(deployment_config, backup_path)
            
            logger.info(f"部署到{deployment_config.environment}环境成功！")
            
            # 显示部署后的操作指南
            self._show_post_deployment_guide(deployment_config)
            
            return True
            
        except Exception as e:
            logger.error(f"部署过程中出错: {e}")
            
            # 如果有备份，询问是否回滚
            if backup_path:
                logger.info(f"可以使用备份回滚: {backup_path}")
            
            return False
    
    def _create_deployment_info(self, deployment_config: DeploymentConfig, backup_path: Optional[str]):
        """创建部署信息文件"""
        deployment_info = {
            'deployment_time': time.strftime('%Y-%m-%d %H:%M:%S'),
            'environment': deployment_config.environment,
            'config_file': deployment_config.config_file,
            'optimization_level': deployment_config.optimization_level,
            'backup_path': backup_path,
            'python_version': f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
            'project_root': str(self.project_root),
            'features': {
                'monitoring': deployment_config.enable_monitoring,
                'caching': deployment_config.enable_caching,
                'compression': deployment_config.enable_compression
            }
        }
        
        deployment_info_file = self.project_root / f"deployment_info_{deployment_config.environment}.json"
        with open(deployment_info_file, 'w', encoding='utf-8') as f:
            json.dump(deployment_info, f, indent=2, ensure_ascii=False)
        
        logger.info(f"部署信息已保存: {deployment_info_file}")
    
    def _show_post_deployment_guide(self, deployment_config: DeploymentConfig):
        """显示部署后操作指南"""
        print("\n" + "="*80)
        print(f"🎉 部署到{deployment_config.environment}环境成功！")
        print("="*80)
        
        print("\n📋 后续操作:")
        print(f"1. 启动系统: python scripts/start_{deployment_config.environment}.py")
        print(f"2. 健康检查: python scripts/health_check_{deployment_config.environment}.py")
        print(f"3. 停止系统: python scripts/stop_{deployment_config.environment}.py")
        
        if deployment_config.enable_monitoring:
            print(f"4. 启动监控: python monitoring/start_monitoring.py")
            print(f"5. 查看指标: http://localhost:9090/metrics")
        
        print(f"\n📁 重要文件:")
        print(f"- 配置文件: {deployment_config.config_file}")
        print(f"- 日志目录: logs/")
        print(f"- 部署信息: deployment_info_{deployment_config.environment}.json")
        
        print(f"\n🔧 优化级别: {deployment_config.optimization_level}")
        env_config = self.environments[deployment_config.environment]
        print(f"- 最大并发数: {env_config['max_concurrent']}")
        print(f"- 日志级别: {env_config['log_level']}")
        print(f"- 调试模式: {env_config['enable_debug']}")
        
        print("\n" + "="*80)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="AI模型评估系统部署脚本")
    parser.add_argument("environment", choices=['dev', 'staging', 'prod'], help="部署环境")
    parser.add_argument("--config", help="配置文件路径")
    parser.add_argument("--optimization", choices=['basic', 'intermediate', 'advanced', 'extreme'], 
                       default='intermediate', help="优化级别")
    parser.add_argument("--no-monitoring", action="store_true", help="禁用监控")
    parser.add_argument("--no-caching", action="store_true", help="禁用缓存")
    parser.add_argument("--no-compression", action="store_true", help="禁用压缩")
    parser.add_argument("--no-backup", action="store_true", help="跳过备份")
    parser.add_argument("--no-tests", action="store_true", help="跳过测试")
    parser.add_argument("--project-root", default=".", help="项目根目录")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细日志")
    
    args = parser.parse_args()
    
    # 配置日志
    if args.verbose:
        logger.remove()
        logger.add(sys.stderr, level="DEBUG")
    
    # 确定配置文件
    if args.config:
        config_file = args.config
    elif args.environment == 'dev':
        config_file = 'config.yaml'
    else:
        config_file = 'config_optimized.yaml'
    
    # 创建部署配置
    deployment_config = DeploymentConfig(
        environment=args.environment,
        config_file=config_file,
        optimization_level=args.optimization,
        enable_monitoring=not args.no_monitoring,
        enable_caching=not args.no_caching,
        enable_compression=not args.no_compression,
        backup_existing=not args.no_backup,
        run_tests=not args.no_tests
    )
    
    # 创建部署管理器
    deployment_manager = DeploymentManager(args.project_root)
    
    # 执行部署
    try:
        success = deployment_manager.deploy(deployment_config)
        if success:
            logger.info("部署成功完成")
            sys.exit(0)
        else:
            logger.error("部署失败")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("部署被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"部署过程中发生异常: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()