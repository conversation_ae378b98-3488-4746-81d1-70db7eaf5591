#!/usr/bin/env python3
"""性能测试脚本
用于验证AI模型评估系统的优化效果
"""

import asyncio
import time
import sys
import os
import json
import csv
import statistics
from typing import List, Dict, Any, Optional
from pathlib import Path
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor
import argparse
from loguru import logger

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.ai_model_evaluation.optimized_main import (
    OptimizedAIModelEvaluator,
    OptimizedEvaluationConfig,
    BatchStrategy,
    Priority
)
from src.ai_model_evaluation.services.performance_monitor import (
    PerformanceMonitor,
    create_performance_monitor
)


@dataclass
class TestConfig:
    """测试配置"""
    name: str
    description: str
    config_path: str
    optimization_config: OptimizedEvaluationConfig
    test_duration: int = 60  # 秒
    warmup_duration: int = 10  # 秒
    

@dataclass
class TestResult:
    """测试结果"""
    config_name: str
    total_requests: int
    successful_requests: int
    failed_requests: int
    requests_per_second: float
    avg_response_time: float
    p95_response_time: float
    p99_response_time: float
    max_concurrent: int
    avg_concurrent: float
    cpu_usage: float
    memory_usage_mb: float
    cache_hit_rate: float
    error_rate: float
    total_time: float
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'config_name': self.config_name,
            'total_requests': self.total_requests,
            'successful_requests': self.successful_requests,
            'failed_requests': self.failed_requests,
            'requests_per_second': self.requests_per_second,
            'avg_response_time': self.avg_response_time,
            'p95_response_time': self.p95_response_time,
            'p99_response_time': self.p99_response_time,
            'max_concurrent': self.max_concurrent,
            'avg_concurrent': self.avg_concurrent,
            'cpu_usage': self.cpu_usage,
            'memory_usage_mb': self.memory_usage_mb,
            'cache_hit_rate': self.cache_hit_rate,
            'error_rate': self.error_rate,
            'total_time': self.total_time
        }


class PerformanceTester:
    """性能测试器"""
    
    def __init__(self, test_data_file: str, output_dir: str = "performance_test_results"):
        self.test_data_file = test_data_file
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建测试数据
        self._create_test_data()
        
        # 测试配置
        self.test_configs = self._create_test_configs()
    
    def _create_test_data(self):
        """创建测试数据文件"""
        if not os.path.exists(self.test_data_file):
            logger.info(f"创建测试数据文件: {self.test_data_file}")
            
            # 生成测试数据
            test_data = []
            questions = [
                "你好，我想了解一下你们的汽车产品",
                "请问有什么优惠活动吗？",
                "我想预约试驾",
                "你们的售后服务怎么样？",
                "这款车的油耗如何？",
                "有现车吗？",
                "可以分期付款吗？",
                "保修期是多长时间？",
                "你们店的地址在哪里？",
                "营业时间是什么时候？"
            ]
            
            # 生成1000条测试数据
            for i in range(1000):
                question = questions[i % len(questions)]
                test_data.append({
                    'id': i + 1,
                    'question': f"{question} (测试数据 {i+1})",
                    'context': f"这是第{i+1}条测试数据的上下文信息"
                })
            
            # 保存为CSV文件
            with open(self.test_data_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=['id', 'question', 'context'])
                writer.writeheader()
                writer.writerows(test_data)
            
            logger.info(f"测试数据文件创建完成: {len(test_data)} 条记录")
    
    def _create_test_configs(self) -> List[TestConfig]:
        """创建测试配置"""
        configs = []
        
        # 基准配置 - 无优化
        configs.append(TestConfig(
            name="baseline",
            description="基准配置 - 无优化",
            config_path="config.yaml",
            optimization_config=OptimizedEvaluationConfig(
                global_max_concurrent=10,
                enable_adaptive_concurrency=False,
                batch_strategy=BatchStrategy.FIXED_SIZE,
                min_batch_size=1,
                max_batch_size=1,
                enable_memory_optimization=False,
                enable_request_cache=False,
                enable_streaming=False
            )
        ))
        
        # 低并发优化
        configs.append(TestConfig(
            name="low_concurrency",
            description="低并发优化配置",
            config_path="config_optimized.yaml",
            optimization_config=OptimizedEvaluationConfig(
                global_max_concurrent=50,
                enable_adaptive_concurrency=True,
                batch_strategy=BatchStrategy.FIXED_SIZE,
                min_batch_size=5,
                max_batch_size=10,
                enable_memory_optimization=True,
                enable_request_cache=True,
                enable_streaming=True
            )
        ))
        
        # 中等并发优化
        configs.append(TestConfig(
            name="medium_concurrency",
            description="中等并发优化配置",
            config_path="config_optimized.yaml",
            optimization_config=OptimizedEvaluationConfig(
                global_max_concurrent=100,
                enable_adaptive_concurrency=True,
                batch_strategy=BatchStrategy.ADAPTIVE_SIZE,
                min_batch_size=10,
                max_batch_size=20,
                enable_memory_optimization=True,
                enable_request_cache=True,
                enable_streaming=True
            )
        ))
        
        # 高并发优化
        configs.append(TestConfig(
            name="high_concurrency",
            description="高并发优化配置",
            config_path="config_optimized.yaml",
            optimization_config=OptimizedEvaluationConfig(
                global_max_concurrent=200,
                enable_adaptive_concurrency=True,
                batch_strategy=BatchStrategy.HYBRID,
                min_batch_size=20,
                max_batch_size=50,
                enable_memory_optimization=True,
                enable_request_cache=True,
                enable_streaming=True
            )
        ))
        
        # 内存优化配置
        configs.append(TestConfig(
            name="memory_optimized",
            description="内存优化配置",
            config_path="config_optimized.yaml",
            optimization_config=OptimizedEvaluationConfig(
                global_max_concurrent=150,
                enable_adaptive_concurrency=True,
                batch_strategy=BatchStrategy.MEMORY_AWARE,
                min_batch_size=15,
                max_batch_size=30,
                memory_threshold_mb=1000,
                enable_memory_optimization=True,
                enable_request_cache=True,
                enable_streaming=True,
                enable_compression=True
            )
        ))
        
        # 极限性能配置
        configs.append(TestConfig(
            name="extreme_performance",
            description="极限性能配置",
            config_path="config_optimized.yaml",
            optimization_config=OptimizedEvaluationConfig(
                global_max_concurrent=300,
                enable_adaptive_concurrency=True,
                batch_strategy=BatchStrategy.HYBRID,
                min_batch_size=30,
                max_batch_size=100,
                memory_threshold_mb=4000,
                enable_memory_optimization=True,
                enable_request_cache=True,
                enable_streaming=True,
                enable_compression=True,
                enable_detailed_metrics=True
            )
        ))
        
        return configs
    
    async def run_single_test(self, config: TestConfig) -> TestResult:
        """运行单个测试"""
        logger.info(f"开始测试配置: {config.name} - {config.description}")
        
        # 创建性能监控器
        monitor = create_performance_monitor(
            enable_system_monitoring=True,
            system_monitor_interval=0.5,
            metrics_retention_hours=1
        )
        
        try:
            # 启动监控
            await monitor.start()
            
            # 创建评估器
            async with OptimizedAIModelEvaluator(
                config_path=config.config_path,
                optimization_config=config.optimization_config,
                enable_monitoring=True,
                enable_system_monitoring=True
            ) as evaluator:
                
                # 设置监控器
                evaluator.performance_monitor = monitor
                
                # 预热阶段
                logger.info(f"预热阶段开始 ({config.warmup_duration}秒)")
                warmup_start = time.time()
                
                while time.time() - warmup_start < config.warmup_duration:
                    output_file = self.output_dir / f"warmup_{config.name}_{int(time.time())}.csv"
                    try:
                        await evaluator.evaluate_models(
                            input_file=self.test_data_file,
                            output_file=str(output_file),
                            model_ids=["gpt-3.5-turbo"],  # 使用快速模型预热
                            priority=Priority.LOW
                        )
                    except Exception as e:
                        logger.warning(f"预热请求失败: {e}")
                    
                    # 清理预热文件
                    if output_file.exists():
                        output_file.unlink()
                
                logger.info("预热阶段完成")
                
                # 清除预热期间的指标
                monitor.collector.clear()
                
                # 正式测试阶段
                logger.info(f"正式测试开始 ({config.test_duration}秒)")
                test_start = time.time()
                test_tasks = []
                
                # 创建并发测试任务
                task_id = 0
                while time.time() - test_start < config.test_duration:
                    output_file = self.output_dir / f"test_{config.name}_{task_id}.csv"
                    
                    # 创建评估任务
                    task = asyncio.create_task(
                        evaluator.evaluate_models(
                            input_file=self.test_data_file,
                            output_file=str(output_file),
                            model_ids=["gpt-3.5-turbo", "gpt-4o-mini"],  # 使用多个模型
                            priority=Priority.NORMAL
                        )
                    )
                    test_tasks.append((task, output_file))
                    task_id += 1
                    
                    # 控制任务创建频率
                    await asyncio.sleep(0.1)
                
                logger.info(f"等待 {len(test_tasks)} 个测试任务完成")
                
                # 等待所有任务完成
                results = await asyncio.gather(*[task for task, _ in test_tasks], return_exceptions=True)
                
                # 清理测试文件
                for _, output_file in test_tasks:
                    if output_file.exists():
                        output_file.unlink()
                
                # 计算测试结果
                test_end = time.time()
                total_time = test_end - test_start
                
                # 获取性能指标
                metrics = monitor.get_current_metrics()
                
                # 统计成功和失败的任务
                successful_tasks = sum(1 for r in results if isinstance(r, dict) and r.get('success', False))
                failed_tasks = len(results) - successful_tasks
                
                # 计算响应时间统计
                response_times = []
                for r in results:
                    if isinstance(r, dict) and 'evaluation_metrics' in r:
                        response_times.append(r['evaluation_metrics']['average_response_time'])
                
                avg_response_time = statistics.mean(response_times) if response_times else 0
                p95_response_time = statistics.quantiles(response_times, n=20)[18] if len(response_times) >= 20 else 0
                p99_response_time = statistics.quantiles(response_times, n=100)[98] if len(response_times) >= 100 else 0
                
                # 创建测试结果
                result = TestResult(
                    config_name=config.name,
                    total_requests=metrics.total_requests,
                    successful_requests=metrics.successful_requests,
                    failed_requests=metrics.failed_requests,
                    requests_per_second=metrics.requests_per_second,
                    avg_response_time=avg_response_time,
                    p95_response_time=p95_response_time,
                    p99_response_time=p99_response_time,
                    max_concurrent=metrics.max_concurrent,
                    avg_concurrent=metrics.avg_concurrent,
                    cpu_usage=metrics.cpu_usage,
                    memory_usage_mb=metrics.memory_usage_mb,
                    cache_hit_rate=metrics.cache_hit_rate,
                    error_rate=metrics.error_rate,
                    total_time=total_time
                )
                
                logger.info(f"测试配置 {config.name} 完成:")
                logger.info(f"  总请求数: {result.total_requests}")
                logger.info(f"  成功率: {(result.successful_requests/max(1,result.total_requests))*100:.1f}%")
                logger.info(f"  RPS: {result.requests_per_second:.2f}")
                logger.info(f"  平均响应时间: {result.avg_response_time:.2f}秒")
                logger.info(f"  CPU使用率: {result.cpu_usage:.1f}%")
                logger.info(f"  内存使用: {result.memory_usage_mb:.1f}MB")
                
                return result
        
        finally:
            # 停止监控
            await monitor.stop()
    
    async def run_all_tests(self) -> List[TestResult]:
        """运行所有测试"""
        logger.info(f"开始运行 {len(self.test_configs)} 个性能测试")
        
        results = []
        for i, config in enumerate(self.test_configs, 1):
            logger.info(f"\n=== 测试 {i}/{len(self.test_configs)}: {config.name} ===")
            
            try:
                result = await self.run_single_test(config)
                results.append(result)
                
                # 测试间隔，让系统恢复
                if i < len(self.test_configs):
                    logger.info("等待系统恢复...")
                    await asyncio.sleep(10)
                    
            except Exception as e:
                logger.error(f"测试 {config.name} 失败: {e}")
                # 创建失败结果
                failed_result = TestResult(
                    config_name=config.name,
                    total_requests=0,
                    successful_requests=0,
                    failed_requests=0,
                    requests_per_second=0,
                    avg_response_time=0,
                    p95_response_time=0,
                    p99_response_time=0,
                    max_concurrent=0,
                    avg_concurrent=0,
                    cpu_usage=0,
                    memory_usage_mb=0,
                    cache_hit_rate=0,
                    error_rate=100,
                    total_time=0
                )
                results.append(failed_result)
        
        return results
    
    def save_results(self, results: List[TestResult]):
        """保存测试结果"""
        timestamp = int(time.time())
        
        # 保存为JSON
        json_file = self.output_dir / f"performance_test_results_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump([r.to_dict() for r in results], f, indent=2, ensure_ascii=False)
        
        # 保存为CSV
        csv_file = self.output_dir / f"performance_test_results_{timestamp}.csv"
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            if results:
                writer = csv.DictWriter(f, fieldnames=results[0].to_dict().keys())
                writer.writeheader()
                for result in results:
                    writer.writerow(result.to_dict())
        
        logger.info(f"测试结果已保存:")
        logger.info(f"  JSON: {json_file}")
        logger.info(f"  CSV: {csv_file}")
        
        return json_file, csv_file
    
    def generate_report(self, results: List[TestResult]) -> str:
        """生成性能测试报告"""
        if not results:
            return "没有测试结果"
        
        # 找到基准配置
        baseline = next((r for r in results if r.config_name == "baseline"), None)
        
        report = []
        report.append("# AI模型评估系统性能测试报告\n")
        report.append(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        report.append(f"测试配置数量: {len(results)}\n")
        
        # 总体统计
        report.append("## 总体统计\n")
        avg_rps = statistics.mean([r.requests_per_second for r in results if r.requests_per_second > 0])
        max_rps = max([r.requests_per_second for r in results])
        best_config = max(results, key=lambda x: x.requests_per_second)
        
        report.append(f"- 平均RPS: {avg_rps:.2f}")
        report.append(f"- 最高RPS: {max_rps:.2f} (配置: {best_config.config_name})")
        report.append(f"- 最佳配置: {best_config.config_name}\n")
        
        # 详细结果
        report.append("## 详细测试结果\n")
        report.append("| 配置名称 | RPS | 平均响应时间(s) | P95响应时间(s) | 成功率(%) | CPU使用率(%) | 内存使用(MB) | 缓存命中率(%) |")
        report.append("|----------|-----|----------------|----------------|-----------|-------------|-------------|--------------|")
        
        for result in results:
            success_rate = (result.successful_requests / max(1, result.total_requests)) * 100
            report.append(
                f"| {result.config_name} | {result.requests_per_second:.2f} | "
                f"{result.avg_response_time:.2f} | {result.p95_response_time:.2f} | "
                f"{success_rate:.1f} | {result.cpu_usage:.1f} | "
                f"{result.memory_usage_mb:.1f} | {result.cache_hit_rate:.1f} |"
            )
        
        # 性能提升分析
        if baseline:
            report.append("\n## 性能提升分析\n")
            for result in results:
                if result.config_name != "baseline" and result.requests_per_second > 0:
                    improvement = ((result.requests_per_second - baseline.requests_per_second) / 
                                 max(1, baseline.requests_per_second)) * 100
                    response_time_improvement = ((baseline.avg_response_time - result.avg_response_time) / 
                                               max(0.001, baseline.avg_response_time)) * 100
                    
                    report.append(f"### {result.config_name}")
                    report.append(f"- RPS提升: {improvement:+.1f}%")
                    report.append(f"- 响应时间改善: {response_time_improvement:+.1f}%")
                    report.append("")
        
        # 推荐配置
        report.append("## 推荐配置\n")
        
        # 按不同指标排序
        best_rps = max(results, key=lambda x: x.requests_per_second)
        best_response_time = min(results, key=lambda x: x.avg_response_time if x.avg_response_time > 0 else float('inf'))
        best_efficiency = min(results, key=lambda x: x.memory_usage_mb / max(1, x.requests_per_second))
        
        report.append(f"- **最高吞吐量**: {best_rps.config_name} (RPS: {best_rps.requests_per_second:.2f})")
        report.append(f"- **最低延迟**: {best_response_time.config_name} (响应时间: {best_response_time.avg_response_time:.2f}s)")
        report.append(f"- **最高效率**: {best_efficiency.config_name} (内存效率最佳)")
        
        return "\n".join(report)


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="AI模型评估系统性能测试")
    parser.add_argument("--test-data", default="test_data.csv", help="测试数据文件路径")
    parser.add_argument("--output-dir", default="performance_test_results", help="输出目录")
    parser.add_argument("--config", help="指定单个测试配置名称")
    parser.add_argument("--duration", type=int, default=60, help="每个测试的持续时间(秒)")
    parser.add_argument("--warmup", type=int, default=10, help="预热时间(秒)")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细日志")
    
    args = parser.parse_args()
    
    # 配置日志
    if args.verbose:
        logger.remove()
        logger.add(sys.stderr, level="DEBUG")
    
    # 创建测试器
    tester = PerformanceTester(args.test_data, args.output_dir)
    
    # 更新测试持续时间
    for config in tester.test_configs:
        config.test_duration = args.duration
        config.warmup_duration = args.warmup
    
    # 如果指定了特定配置，只运行该配置
    if args.config:
        tester.test_configs = [c for c in tester.test_configs if c.name == args.config]
        if not tester.test_configs:
            logger.error(f"未找到配置: {args.config}")
            return
    
    try:
        # 运行测试
        logger.info("开始性能测试")
        start_time = time.time()
        
        results = await tester.run_all_tests()
        
        total_time = time.time() - start_time
        logger.info(f"所有测试完成，总耗时: {total_time:.2f}秒")
        
        # 保存结果
        json_file, csv_file = tester.save_results(results)
        
        # 生成报告
        report = tester.generate_report(results)
        report_file = tester.output_dir / f"performance_report_{int(time.time())}.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info(f"性能报告已生成: {report_file}")
        
        # 打印简要报告
        print("\n" + "="*80)
        print("性能测试完成")
        print("="*80)
        print(report)
        
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
    except Exception as e:
        logger.error(f"测试失败: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())