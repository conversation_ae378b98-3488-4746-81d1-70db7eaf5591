#!/usr/bin/env python3
"""基准测试脚本
用于对比AI模型评估系统优化前后的性能差异
"""

import asyncio
import time
import sys
import os
import json
import csv
import statistics
import psutil
import gc
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
from dataclasses import dataclass, asdict
from concurrent.futures import ThreadPoolExecutor
import argparse
from loguru import logger
import matplotlib.pyplot as plt
import pandas as pd
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.ai_model_evaluation.optimized_main import (
    OptimizedAIModelEvaluator,
    OptimizedEvaluationConfig,
    BatchStrategy,
    Priority
)
from src.ai_model_evaluation.services.performance_monitor import (
    PerformanceMonitor,
    create_performance_monitor
)


@dataclass
class BenchmarkConfig:
    """基准测试配置"""
    name: str
    description: str
    config_path: str
    optimization_config: OptimizedEvaluationConfig
    expected_rps: float = 0  # 期望的RPS
    expected_response_time: float = 0  # 期望的响应时间(秒)
    

@dataclass
class BenchmarkMetrics:
    """基准测试指标"""
    timestamp: str
    config_name: str
    
    # 吞吐量指标
    requests_per_second: float
    total_requests: int
    successful_requests: int
    failed_requests: int
    
    # 延迟指标
    avg_response_time: float
    p50_response_time: float
    p95_response_time: float
    p99_response_time: float
    max_response_time: float
    
    # 并发指标
    max_concurrent: int
    avg_concurrent: float
    
    # 资源使用指标
    cpu_usage_percent: float
    memory_usage_mb: float
    memory_peak_mb: float
    disk_io_read_mb: float
    disk_io_write_mb: float
    network_sent_mb: float
    network_recv_mb: float
    
    # 错误指标
    error_rate: float
    timeout_rate: float
    retry_rate: float
    
    # 缓存指标
    cache_hit_rate: float
    cache_miss_rate: float
    
    # 效率指标
    requests_per_cpu_percent: float
    requests_per_mb_memory: float
    cost_per_request: float  # 资源成本
    
    # 稳定性指标
    response_time_std: float
    rps_std: float
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


class SystemResourceMonitor:
    """系统资源监控器"""
    
    def __init__(self, interval: float = 1.0):
        self.interval = interval
        self.monitoring = False
        self.metrics = []
        self.process = psutil.Process()
        
    async def start_monitoring(self):
        """开始监控"""
        self.monitoring = True
        self.metrics = []
        
        while self.monitoring:
            try:
                # CPU使用率
                cpu_percent = self.process.cpu_percent()
                
                # 内存使用
                memory_info = self.process.memory_info()
                memory_mb = memory_info.rss / 1024 / 1024
                
                # 磁盘IO
                io_counters = self.process.io_counters()
                
                # 网络IO (系统级别)
                net_io = psutil.net_io_counters()
                
                metric = {
                    'timestamp': time.time(),
                    'cpu_percent': cpu_percent,
                    'memory_mb': memory_mb,
                    'disk_read_bytes': io_counters.read_bytes,
                    'disk_write_bytes': io_counters.write_bytes,
                    'network_sent_bytes': net_io.bytes_sent,
                    'network_recv_bytes': net_io.bytes_recv
                }
                
                self.metrics.append(metric)
                
            except Exception as e:
                logger.warning(f"资源监控错误: {e}")
            
            await asyncio.sleep(self.interval)
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
    
    def get_summary(self) -> Dict[str, float]:
        """获取监控摘要"""
        if not self.metrics:
            return {}
        
        # 计算平均值和峰值
        cpu_values = [m['cpu_percent'] for m in self.metrics]
        memory_values = [m['memory_mb'] for m in self.metrics]
        
        # 计算IO差值
        if len(self.metrics) > 1:
            first = self.metrics[0]
            last = self.metrics[-1]
            duration = last['timestamp'] - first['timestamp']
            
            disk_read_mb = (last['disk_read_bytes'] - first['disk_read_bytes']) / 1024 / 1024
            disk_write_mb = (last['disk_write_bytes'] - first['disk_write_bytes']) / 1024 / 1024
            network_sent_mb = (last['network_sent_bytes'] - first['network_sent_bytes']) / 1024 / 1024
            network_recv_mb = (last['network_recv_bytes'] - first['network_recv_bytes']) / 1024 / 1024
        else:
            disk_read_mb = disk_write_mb = network_sent_mb = network_recv_mb = 0
        
        return {
            'avg_cpu_percent': statistics.mean(cpu_values) if cpu_values else 0,
            'max_cpu_percent': max(cpu_values) if cpu_values else 0,
            'avg_memory_mb': statistics.mean(memory_values) if memory_values else 0,
            'peak_memory_mb': max(memory_values) if memory_values else 0,
            'disk_read_mb': disk_read_mb,
            'disk_write_mb': disk_write_mb,
            'network_sent_mb': network_sent_mb,
            'network_recv_mb': network_recv_mb
        }


class BenchmarkRunner:
    """基准测试运行器"""
    
    def __init__(self, test_data_file: str, output_dir: str = "benchmark_results"):
        self.test_data_file = test_data_file
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建测试数据
        self._create_test_data()
        
        # 基准测试配置
        self.benchmark_configs = self._create_benchmark_configs()
    
    def _create_test_data(self):
        """创建测试数据文件"""
        if not os.path.exists(self.test_data_file):
            logger.info(f"创建基准测试数据文件: {self.test_data_file}")
            
            # 生成多样化的测试数据
            test_data = []
            
            # 不同类型的问题
            question_templates = [
                # 简单问答
                "什么是{topic}？",
                "请介绍一下{topic}",
                "我想了解{topic}的相关信息",
                
                # 复杂查询
                "请详细说明{topic}的优缺点，并给出具体的使用建议",
                "比较{topic}和其他类似产品的差异，分析各自的适用场景",
                "从技术角度分析{topic}的实现原理和发展趋势",
                
                # 多轮对话
                "你好，我想咨询关于{topic}的问题",
                "基于之前的讨论，请进一步说明{topic}的细节",
                "总结一下我们关于{topic}的对话要点"
            ]
            
            topics = [
                "人工智能", "机器学习", "深度学习", "自然语言处理", "计算机视觉",
                "云计算", "大数据", "区块链", "物联网", "边缘计算",
                "汽车销售", "房地产", "金融服务", "医疗健康", "教育培训",
                "电商平台", "社交媒体", "游戏娱乐", "旅游服务", "餐饮美食"
            ]
            
            # 生成2000条测试数据
            for i in range(2000):
                template = question_templates[i % len(question_templates)]
                topic = topics[i % len(topics)]
                question = template.format(topic=topic)
                
                # 添加一些变化
                if i % 3 == 0:
                    question += f" (测试数据 {i+1})"
                elif i % 3 == 1:
                    question = f"[{i+1}] " + question
                
                test_data.append({
                    'id': i + 1,
                    'question': question,
                    'context': f"这是第{i+1}条基准测试数据，主题：{topic}",
                    'category': topics.index(topic) % 5,  # 分类标签
                    'priority': (i % 3) + 1,  # 优先级 1-3
                    'complexity': len(question.split()) // 5 + 1  # 复杂度
                })
            
            # 保存为CSV文件
            with open(self.test_data_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=['id', 'question', 'context', 'category', 'priority', 'complexity'])
                writer.writeheader()
                writer.writerows(test_data)
            
            logger.info(f"基准测试数据文件创建完成: {len(test_data)} 条记录")
    
    def _create_benchmark_configs(self) -> List[BenchmarkConfig]:
        """创建基准测试配置"""
        configs = []
        
        # 1. 基准配置 - 无优化
        configs.append(BenchmarkConfig(
            name="baseline",
            description="基准配置 - 无优化",
            config_path="config.yaml",
            optimization_config=OptimizedEvaluationConfig(
                global_max_concurrent=5,
                enable_adaptive_concurrency=False,
                batch_strategy=BatchStrategy.FIXED_SIZE,
                min_batch_size=1,
                max_batch_size=1,
                enable_memory_optimization=False,
                enable_request_cache=False,
                enable_streaming=False,
                enable_compression=False
            ),
            expected_rps=2.0,
            expected_response_time=2.0
        ))
        
        # 2. 基础优化配置
        configs.append(BenchmarkConfig(
            name="basic_optimized",
            description="基础优化配置",
            config_path="config_optimized.yaml",
            optimization_config=OptimizedEvaluationConfig(
                global_max_concurrent=20,
                enable_adaptive_concurrency=True,
                batch_strategy=BatchStrategy.FIXED_SIZE,
                min_batch_size=5,
                max_batch_size=10,
                enable_memory_optimization=True,
                enable_request_cache=True,
                enable_streaming=True,
                enable_compression=True
            ),
            expected_rps=8.0,
            expected_response_time=1.5
        ))
        
        # 3. 中级优化配置
        configs.append(BenchmarkConfig(
            name="intermediate_optimized",
            description="中级优化配置",
            config_path="config_optimized.yaml",
            optimization_config=OptimizedEvaluationConfig(
                global_max_concurrent=50,
                enable_adaptive_concurrency=True,
                batch_strategy=BatchStrategy.ADAPTIVE_SIZE,
                min_batch_size=10,
                max_batch_size=25,
                enable_memory_optimization=True,
                enable_request_cache=True,
                enable_streaming=True,
                enable_compression=True,
                memory_threshold_mb=1000
            ),
            expected_rps=20.0,
            expected_response_time=1.0
        ))
        
        # 4. 高级优化配置
        configs.append(BenchmarkConfig(
            name="advanced_optimized",
            description="高级优化配置",
            config_path="config_optimized.yaml",
            optimization_config=OptimizedEvaluationConfig(
                global_max_concurrent=100,
                enable_adaptive_concurrency=True,
                batch_strategy=BatchStrategy.HYBRID,
                min_batch_size=20,
                max_batch_size=50,
                enable_memory_optimization=True,
                enable_request_cache=True,
                enable_streaming=True,
                enable_compression=True,
                memory_threshold_mb=2000,
                enable_detailed_metrics=True
            ),
            expected_rps=40.0,
            expected_response_time=0.8
        ))
        
        # 5. 极限优化配置
        configs.append(BenchmarkConfig(
            name="extreme_optimized",
            description="极限优化配置",
            config_path="config_optimized.yaml",
            optimization_config=OptimizedEvaluationConfig(
                global_max_concurrent=200,
                enable_adaptive_concurrency=True,
                batch_strategy=BatchStrategy.MEMORY_AWARE,
                min_batch_size=30,
                max_batch_size=100,
                enable_memory_optimization=True,
                enable_request_cache=True,
                enable_streaming=True,
                enable_compression=True,
                memory_threshold_mb=4000,
                enable_detailed_metrics=True,
                cache_ttl_seconds=3600
            ),
            expected_rps=80.0,
            expected_response_time=0.5
        ))
        
        return configs
    
    async def run_benchmark(self, config: BenchmarkConfig, duration: int = 120) -> BenchmarkMetrics:
        """运行单个基准测试"""
        logger.info(f"开始基准测试: {config.name} - {config.description}")
        
        # 创建资源监控器
        resource_monitor = SystemResourceMonitor(interval=0.5)
        
        # 创建性能监控器
        performance_monitor = create_performance_monitor(
            enable_system_monitoring=True,
            system_monitor_interval=0.5,
            metrics_retention_hours=1
        )
        
        # 响应时间记录
        response_times = []
        request_timestamps = []
        
        try:
            # 启动监控
            await performance_monitor.start()
            monitor_task = asyncio.create_task(resource_monitor.start_monitoring())
            
            # 创建评估器
            async with OptimizedAIModelEvaluator(
                config_path=config.config_path,
                optimization_config=config.optimization_config,
                enable_monitoring=True,
                enable_system_monitoring=True
            ) as evaluator:
                
                # 设置监控器
                evaluator.performance_monitor = performance_monitor
                
                # 预热阶段 (30秒)
                logger.info("预热阶段开始")
                warmup_start = time.time()
                
                while time.time() - warmup_start < 30:
                    try:
                        output_file = self.output_dir / f"warmup_{config.name}_{int(time.time())}.csv"
                        await evaluator.evaluate_models(
                            input_file=self.test_data_file,
                            output_file=str(output_file),
                            model_ids=["gpt-3.5-turbo"],
                            priority=Priority.LOW
                        )
                        
                        # 清理预热文件
                        if output_file.exists():
                            output_file.unlink()
                            
                    except Exception as e:
                        logger.warning(f"预热请求失败: {e}")
                    
                    await asyncio.sleep(1)
                
                logger.info("预热阶段完成")
                
                # 清除预热期间的指标
                performance_monitor.collector.clear()
                resource_monitor.metrics.clear()
                
                # 正式基准测试阶段
                logger.info(f"基准测试开始 ({duration}秒)")
                test_start = time.time()
                test_tasks = []
                task_id = 0
                
                # 创建持续的测试负载
                while time.time() - test_start < duration:
                    output_file = self.output_dir / f"benchmark_{config.name}_{task_id}.csv"
                    
                    # 记录请求开始时间
                    request_start = time.time()
                    request_timestamps.append(request_start)
                    
                    # 创建评估任务
                    task = asyncio.create_task(
                        self._timed_evaluation(
                            evaluator,
                            output_file,
                            request_start,
                            response_times
                        )
                    )
                    test_tasks.append((task, output_file))
                    task_id += 1
                    
                    # 动态调整请求频率
                    current_concurrent = len([t for t, _ in test_tasks if not t.done()])
                    if current_concurrent < config.optimization_config.global_max_concurrent:
                        await asyncio.sleep(0.05)  # 高频率
                    else:
                        await asyncio.sleep(0.2)   # 低频率
                
                logger.info(f"等待 {len(test_tasks)} 个基准测试任务完成")
                
                # 等待所有任务完成
                results = await asyncio.gather(*[task for task, _ in test_tasks], return_exceptions=True)
                
                # 清理测试文件
                for _, output_file in test_tasks:
                    if output_file.exists():
                        output_file.unlink()
                
                # 停止监控
                resource_monitor.stop_monitoring()
                await monitor_task
                
                # 计算测试结果
                test_end = time.time()
                total_time = test_end - test_start
                
                # 获取性能指标
                perf_metrics = performance_monitor.get_current_metrics()
                resource_summary = resource_monitor.get_summary()
                
                # 统计成功和失败的任务
                successful_tasks = sum(1 for r in results if isinstance(r, dict) and r.get('success', False))
                failed_tasks = len(results) - successful_tasks
                timeout_tasks = sum(1 for r in results if isinstance(r, Exception) and 'timeout' in str(r).lower())
                
                # 计算响应时间统计
                if response_times:
                    response_times.sort()
                    n = len(response_times)
                    avg_response_time = statistics.mean(response_times)
                    p50_response_time = response_times[n//2]
                    p95_response_time = response_times[int(n*0.95)]
                    p99_response_time = response_times[int(n*0.99)]
                    max_response_time = max(response_times)
                    response_time_std = statistics.stdev(response_times) if n > 1 else 0
                else:
                    avg_response_time = p50_response_time = p95_response_time = p99_response_time = max_response_time = response_time_std = 0
                
                # 计算RPS统计
                rps = len(results) / total_time
                
                # 计算效率指标
                cpu_usage = resource_summary.get('avg_cpu_percent', 0)
                memory_usage = resource_summary.get('avg_memory_mb', 0)
                
                requests_per_cpu = rps / max(cpu_usage, 0.1)
                requests_per_mb = rps / max(memory_usage, 1)
                cost_per_request = (cpu_usage + memory_usage/100) / max(rps, 0.1)
                
                # 创建基准测试指标
                metrics = BenchmarkMetrics(
                    timestamp=datetime.now().isoformat(),
                    config_name=config.name,
                    
                    # 吞吐量指标
                    requests_per_second=rps,
                    total_requests=len(results),
                    successful_requests=successful_tasks,
                    failed_requests=failed_tasks,
                    
                    # 延迟指标
                    avg_response_time=avg_response_time,
                    p50_response_time=p50_response_time,
                    p95_response_time=p95_response_time,
                    p99_response_time=p99_response_time,
                    max_response_time=max_response_time,
                    
                    # 并发指标
                    max_concurrent=perf_metrics.max_concurrent,
                    avg_concurrent=perf_metrics.avg_concurrent,
                    
                    # 资源使用指标
                    cpu_usage_percent=cpu_usage,
                    memory_usage_mb=memory_usage,
                    memory_peak_mb=resource_summary.get('peak_memory_mb', 0),
                    disk_io_read_mb=resource_summary.get('disk_read_mb', 0),
                    disk_io_write_mb=resource_summary.get('disk_write_mb', 0),
                    network_sent_mb=resource_summary.get('network_sent_mb', 0),
                    network_recv_mb=resource_summary.get('network_recv_mb', 0),
                    
                    # 错误指标
                    error_rate=(failed_tasks / max(len(results), 1)) * 100,
                    timeout_rate=(timeout_tasks / max(len(results), 1)) * 100,
                    retry_rate=0,  # TODO: 从监控器获取
                    
                    # 缓存指标
                    cache_hit_rate=perf_metrics.cache_hit_rate,
                    cache_miss_rate=100 - perf_metrics.cache_hit_rate,
                    
                    # 效率指标
                    requests_per_cpu_percent=requests_per_cpu,
                    requests_per_mb_memory=requests_per_mb,
                    cost_per_request=cost_per_request,
                    
                    # 稳定性指标
                    response_time_std=response_time_std,
                    rps_std=0  # TODO: 计算RPS标准差
                )
                
                logger.info(f"基准测试 {config.name} 完成:")
                logger.info(f"  RPS: {metrics.requests_per_second:.2f} (期望: {config.expected_rps})")
                logger.info(f"  平均响应时间: {metrics.avg_response_time:.2f}s (期望: {config.expected_response_time}s)")
                logger.info(f"  成功率: {(metrics.successful_requests/max(1,metrics.total_requests))*100:.1f}%")
                logger.info(f"  CPU使用率: {metrics.cpu_usage_percent:.1f}%")
                logger.info(f"  内存使用: {metrics.memory_usage_mb:.1f}MB")
                
                return metrics
        
        finally:
            # 确保监控停止
            resource_monitor.stop_monitoring()
            await performance_monitor.stop()
    
    async def _timed_evaluation(self, evaluator, output_file, start_time, response_times):
        """带时间记录的评估"""
        try:
            result = await evaluator.evaluate_models(
                input_file=self.test_data_file,
                output_file=str(output_file),
                model_ids=["gpt-3.5-turbo", "gpt-4o-mini"],
                priority=Priority.NORMAL
            )
            
            # 记录响应时间
            end_time = time.time()
            response_time = end_time - start_time
            response_times.append(response_time)
            
            return {'success': True, 'response_time': response_time, 'result': result}
            
        except Exception as e:
            end_time = time.time()
            response_time = end_time - start_time
            response_times.append(response_time)
            
            return {'success': False, 'response_time': response_time, 'error': str(e)}
    
    async def run_all_benchmarks(self, duration: int = 120) -> List[BenchmarkMetrics]:
        """运行所有基准测试"""
        logger.info(f"开始运行 {len(self.benchmark_configs)} 个基准测试")
        
        results = []
        for i, config in enumerate(self.benchmark_configs, 1):
            logger.info(f"\n=== 基准测试 {i}/{len(self.benchmark_configs)}: {config.name} ===")
            
            try:
                # 运行基准测试
                metrics = await self.run_benchmark(config, duration)
                results.append(metrics)
                
                # 测试间隔，让系统恢复
                if i < len(self.benchmark_configs):
                    logger.info("等待系统恢复...")
                    gc.collect()  # 强制垃圾回收
                    await asyncio.sleep(30)
                    
            except Exception as e:
                logger.error(f"基准测试 {config.name} 失败: {e}")
                # 创建失败的指标记录
                failed_metrics = BenchmarkMetrics(
                    timestamp=datetime.now().isoformat(),
                    config_name=config.name,
                    requests_per_second=0,
                    total_requests=0,
                    successful_requests=0,
                    failed_requests=0,
                    avg_response_time=0,
                    p50_response_time=0,
                    p95_response_time=0,
                    p99_response_time=0,
                    max_response_time=0,
                    max_concurrent=0,
                    avg_concurrent=0,
                    cpu_usage_percent=0,
                    memory_usage_mb=0,
                    memory_peak_mb=0,
                    disk_io_read_mb=0,
                    disk_io_write_mb=0,
                    network_sent_mb=0,
                    network_recv_mb=0,
                    error_rate=100,
                    timeout_rate=0,
                    retry_rate=0,
                    cache_hit_rate=0,
                    cache_miss_rate=100,
                    requests_per_cpu_percent=0,
                    requests_per_mb_memory=0,
                    cost_per_request=float('inf'),
                    response_time_std=0,
                    rps_std=0
                )
                results.append(failed_metrics)
        
        return results
    
    def save_benchmark_results(self, results: List[BenchmarkMetrics]) -> Tuple[str, str]:
        """保存基准测试结果"""
        timestamp = int(time.time())
        
        # 保存为JSON
        json_file = self.output_dir / f"benchmark_results_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump([r.to_dict() for r in results], f, indent=2, ensure_ascii=False)
        
        # 保存为CSV
        csv_file = self.output_dir / f"benchmark_results_{timestamp}.csv"
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            if results:
                writer = csv.DictWriter(f, fieldnames=results[0].to_dict().keys())
                writer.writeheader()
                for result in results:
                    writer.writerow(result.to_dict())
        
        logger.info(f"基准测试结果已保存:")
        logger.info(f"  JSON: {json_file}")
        logger.info(f"  CSV: {csv_file}")
        
        return str(json_file), str(csv_file)
    
    def generate_benchmark_report(self, results: List[BenchmarkMetrics]) -> str:
        """生成基准测试报告"""
        if not results:
            return "没有基准测试结果"
        
        # 找到基准配置
        baseline = next((r for r in results if r.config_name == "baseline"), None)
        
        report = []
        report.append("# AI模型评估系统基准测试报告\n")
        report.append(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        report.append(f"测试配置数量: {len(results)}\n")
        
        # 执行摘要
        report.append("## 执行摘要\n")
        
        best_rps = max(results, key=lambda x: x.requests_per_second)
        best_latency = min(results, key=lambda x: x.avg_response_time if x.avg_response_time > 0 else float('inf'))
        best_efficiency = max(results, key=lambda x: x.requests_per_cpu_percent)
        
        report.append(f"- **最佳吞吐量**: {best_rps.config_name} ({best_rps.requests_per_second:.2f} RPS)")
        report.append(f"- **最佳延迟**: {best_latency.config_name} ({best_latency.avg_response_time:.2f}s)")
        report.append(f"- **最佳效率**: {best_efficiency.config_name} ({best_efficiency.requests_per_cpu_percent:.2f} RPS/CPU%)")
        
        if baseline:
            max_improvement = max(results, key=lambda x: x.requests_per_second)
            improvement_ratio = max_improvement.requests_per_second / max(baseline.requests_per_second, 0.1)
            report.append(f"- **最大性能提升**: {improvement_ratio:.1f}x (相比基准配置)\n")
        
        # 详细性能对比表
        report.append("## 详细性能对比\n")
        report.append("| 配置 | RPS | 平均延迟(s) | P95延迟(s) | 成功率(%) | CPU(%) | 内存(MB) | 缓存命中率(%) | 效率(RPS/CPU%) |")
        report.append("|------|-----|-------------|------------|-----------|--------|----------|---------------|----------------|")
        
        for result in results:
            success_rate = (result.successful_requests / max(1, result.total_requests)) * 100
            report.append(
                f"| {result.config_name} | {result.requests_per_second:.2f} | "
                f"{result.avg_response_time:.2f} | {result.p95_response_time:.2f} | "
                f"{success_rate:.1f} | {result.cpu_usage_percent:.1f} | "
                f"{result.memory_usage_mb:.1f} | {result.cache_hit_rate:.1f} | "
                f"{result.requests_per_cpu_percent:.2f} |"
            )
        
        # 性能提升分析
        if baseline:
            report.append("\n## 性能提升分析\n")
            
            for result in results:
                if result.config_name != "baseline" and result.requests_per_second > 0:
                    rps_improvement = ((result.requests_per_second - baseline.requests_per_second) / 
                                     max(baseline.requests_per_second, 0.1)) * 100
                    
                    latency_improvement = ((baseline.avg_response_time - result.avg_response_time) / 
                                         max(baseline.avg_response_time, 0.001)) * 100
                    
                    efficiency_improvement = ((result.requests_per_cpu_percent - baseline.requests_per_cpu_percent) / 
                                            max(baseline.requests_per_cpu_percent, 0.1)) * 100
                    
                    report.append(f"### {result.config_name}")
                    report.append(f"- **吞吐量提升**: {rps_improvement:+.1f}%")
                    report.append(f"- **延迟改善**: {latency_improvement:+.1f}%")
                    report.append(f"- **效率提升**: {efficiency_improvement:+.1f}%")
                    
                    # 期望vs实际对比
                    config = next((c for c in self.benchmark_configs if c.name == result.config_name), None)
                    if config:
                        rps_vs_expected = (result.requests_per_second / max(config.expected_rps, 0.1)) * 100
                        latency_vs_expected = (config.expected_response_time / max(result.avg_response_time, 0.001)) * 100
                        
                        report.append(f"- **RPS达成率**: {rps_vs_expected:.1f}% (实际: {result.requests_per_second:.2f}, 期望: {config.expected_rps:.2f})")
                        report.append(f"- **延迟达成率**: {latency_vs_expected:.1f}% (实际: {result.avg_response_time:.2f}s, 期望: {config.expected_response_time:.2f}s)")
                    
                    report.append("")
        
        # 资源使用分析
        report.append("## 资源使用分析\n")
        
        avg_cpu = statistics.mean([r.cpu_usage_percent for r in results if r.cpu_usage_percent > 0])
        avg_memory = statistics.mean([r.memory_usage_mb for r in results if r.memory_usage_mb > 0])
        
        report.append(f"- **平均CPU使用率**: {avg_cpu:.1f}%")
        report.append(f"- **平均内存使用**: {avg_memory:.1f}MB")
        
        # 找出资源使用最优配置
        best_cpu_efficiency = max(results, key=lambda x: x.requests_per_cpu_percent if x.requests_per_cpu_percent > 0 else 0)
        best_memory_efficiency = max(results, key=lambda x: x.requests_per_mb_memory if x.requests_per_mb_memory > 0 else 0)
        
        report.append(f"- **CPU效率最佳**: {best_cpu_efficiency.config_name} ({best_cpu_efficiency.requests_per_cpu_percent:.2f} RPS/CPU%)")
        report.append(f"- **内存效率最佳**: {best_memory_efficiency.config_name} ({best_memory_efficiency.requests_per_mb_memory:.2f} RPS/MB)\n")
        
        # 稳定性分析
        report.append("## 稳定性分析\n")
        
        for result in results:
            if result.total_requests > 0:
                report.append(f"### {result.config_name}")
                report.append(f"- **错误率**: {result.error_rate:.2f}%")
                report.append(f"- **超时率**: {result.timeout_rate:.2f}%")
                report.append(f"- **响应时间标准差**: {result.response_time_std:.3f}s")
                report.append(f"- **P99延迟**: {result.p99_response_time:.2f}s")
                report.append("")
        
        # 推荐配置
        report.append("## 推荐配置\n")
        
        # 根据不同场景推荐
        report.append("### 高吞吐量场景")
        high_throughput = max(results, key=lambda x: x.requests_per_second)
        report.append(f"推荐使用 **{high_throughput.config_name}** 配置")
        report.append(f"- RPS: {high_throughput.requests_per_second:.2f}")
        report.append(f"- 平均延迟: {high_throughput.avg_response_time:.2f}s")
        report.append(f"- 资源使用: CPU {high_throughput.cpu_usage_percent:.1f}%, 内存 {high_throughput.memory_usage_mb:.1f}MB\n")
        
        report.append("### 低延迟场景")
        low_latency = min(results, key=lambda x: x.avg_response_time if x.avg_response_time > 0 else float('inf'))
        report.append(f"推荐使用 **{low_latency.config_name}** 配置")
        report.append(f"- 平均延迟: {low_latency.avg_response_time:.2f}s")
        report.append(f"- P95延迟: {low_latency.p95_response_time:.2f}s")
        report.append(f"- RPS: {low_latency.requests_per_second:.2f}\n")
        
        report.append("### 资源受限场景")
        resource_efficient = max(results, key=lambda x: (x.requests_per_cpu_percent + x.requests_per_mb_memory) / 2)
        report.append(f"推荐使用 **{resource_efficient.config_name}** 配置")
        report.append(f"- CPU效率: {resource_efficient.requests_per_cpu_percent:.2f} RPS/CPU%")
        report.append(f"- 内存效率: {resource_efficient.requests_per_mb_memory:.2f} RPS/MB")
        report.append(f"- 综合性能: RPS {resource_efficient.requests_per_second:.2f}\n")
        
        # 优化建议
        report.append("## 优化建议\n")
        
        if baseline:
            best_overall = max(results, key=lambda x: x.requests_per_second)
            if best_overall.config_name != "baseline":
                improvement = (best_overall.requests_per_second / max(baseline.requests_per_second, 0.1))
                report.append(f"1. **采用优化配置**: 使用 {best_overall.config_name} 可获得 {improvement:.1f}x 性能提升")
        
        # 分析缓存效果
        cache_configs = [r for r in results if r.cache_hit_rate > 0]
        if cache_configs:
            avg_cache_hit = statistics.mean([r.cache_hit_rate for r in cache_configs])
            report.append(f"2. **缓存优化**: 平均缓存命中率 {avg_cache_hit:.1f}%，建议进一步优化缓存策略")
        
        # 分析并发效果
        high_concurrent = [r for r in results if r.max_concurrent > 50]
        if high_concurrent:
            report.append(f"3. **并发优化**: 高并发配置表现良好，建议根据硬件资源调整并发数")
        
        report.append(f"4. **监控告警**: 建议设置 RPS < {best_rps.requests_per_second * 0.8:.1f} 或延迟 > {best_latency.avg_response_time * 1.5:.2f}s 的告警")
        
        return "\n".join(report)
    
    def create_performance_charts(self, results: List[BenchmarkMetrics]):
        """创建性能图表"""
        if not results:
            return
        
        # 创建图表目录
        charts_dir = self.output_dir / "charts"
        charts_dir.mkdir(exist_ok=True)
        
        # 准备数据
        configs = [r.config_name for r in results]
        rps_values = [r.requests_per_second for r in results]
        latency_values = [r.avg_response_time for r in results]
        cpu_values = [r.cpu_usage_percent for r in results]
        memory_values = [r.memory_usage_mb for r in results]
        
        # 1. RPS对比图
        plt.figure(figsize=(12, 6))
        bars = plt.bar(configs, rps_values, color=['red' if c == 'baseline' else 'blue' for c in configs])
        plt.title('吞吐量对比 (RPS)', fontsize=14, fontweight='bold')
        plt.xlabel('配置')
        plt.ylabel('每秒请求数 (RPS)')
        plt.xticks(rotation=45)
        
        # 添加数值标签
        for bar, value in zip(bars, rps_values):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(rps_values)*0.01,
                    f'{value:.1f}', ha='center', va='bottom')
        
        plt.tight_layout()
        plt.savefig(charts_dir / 'rps_comparison.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        # 2. 延迟对比图
        plt.figure(figsize=(12, 6))
        bars = plt.bar(configs, latency_values, color=['red' if c == 'baseline' else 'green' for c in configs])
        plt.title('响应延迟对比', fontsize=14, fontweight='bold')
        plt.xlabel('配置')
        plt.ylabel('平均响应时间 (秒)')
        plt.xticks(rotation=45)
        
        # 添加数值标签
        for bar, value in zip(bars, latency_values):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(latency_values)*0.01,
                    f'{value:.2f}s', ha='center', va='bottom')
        
        plt.tight_layout()
        plt.savefig(charts_dir / 'latency_comparison.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        # 3. 资源使用对比图
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # CPU使用率
        bars1 = ax1.bar(configs, cpu_values, color='orange')
        ax1.set_title('CPU使用率对比')
        ax1.set_xlabel('配置')
        ax1.set_ylabel('CPU使用率 (%)')
        ax1.tick_params(axis='x', rotation=45)
        
        for bar, value in zip(bars1, cpu_values):
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(cpu_values)*0.01,
                    f'{value:.1f}%', ha='center', va='bottom')
        
        # 内存使用
        bars2 = ax2.bar(configs, memory_values, color='purple')
        ax2.set_title('内存使用对比')
        ax2.set_xlabel('配置')
        ax2.set_ylabel('内存使用 (MB)')
        ax2.tick_params(axis='x', rotation=45)
        
        for bar, value in zip(bars2, memory_values):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(memory_values)*0.01,
                    f'{value:.0f}MB', ha='center', va='bottom')
        
        plt.tight_layout()
        plt.savefig(charts_dir / 'resource_comparison.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        # 4. 综合性能雷达图
        if len(results) > 1:
            # 标准化数据 (0-1)
            max_rps = max(rps_values)
            min_latency = min([v for v in latency_values if v > 0])
            max_cpu = max(cpu_values)
            max_memory = max(memory_values)
            
            fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))
            
            # 定义指标
            metrics = ['吞吐量', '低延迟', 'CPU效率', '内存效率']
            angles = [n / float(len(metrics)) * 2 * 3.14159 for n in range(len(metrics))]
            angles += angles[:1]  # 闭合
            
            # 为每个配置绘制雷达图
            colors = ['red', 'blue', 'green', 'orange', 'purple']
            for i, result in enumerate(results[:5]):  # 最多显示5个配置
                values = [
                    result.requests_per_second / max_rps,  # 吞吐量 (标准化)
                    min_latency / max(result.avg_response_time, 0.001),  # 低延迟 (倒数标准化)
                    1 - (result.cpu_usage_percent / max_cpu),  # CPU效率 (倒数)
                    1 - (result.memory_usage_mb / max_memory)  # 内存效率 (倒数)
                ]
                values += values[:1]  # 闭合
                
                ax.plot(angles, values, 'o-', linewidth=2, label=result.config_name, color=colors[i % len(colors)])
                ax.fill(angles, values, alpha=0.25, color=colors[i % len(colors)])
            
            ax.set_xticks(angles[:-1])
            ax.set_xticklabels(metrics)
            ax.set_ylim(0, 1)
            ax.set_title('综合性能对比雷达图', size=16, fontweight='bold', pad=20)
            ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
            
            plt.tight_layout()
            plt.savefig(charts_dir / 'performance_radar.png', dpi=300, bbox_inches='tight')
            plt.close()
        
        logger.info(f"性能图表已生成到: {charts_dir}")


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="AI模型评估系统基准测试")
    parser.add_argument("--test-data", default="benchmark_data.csv", help="基准测试数据文件路径")
    parser.add_argument("--output-dir", default="benchmark_results", help="输出目录")
    parser.add_argument("--duration", type=int, default=120, help="每个基准测试的持续时间(秒)")
    parser.add_argument("--config", help="指定单个基准测试配置名称")
    parser.add_argument("--charts", action="store_true", help="生成性能图表")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细日志")
    
    args = parser.parse_args()
    
    # 配置日志
    if args.verbose:
        logger.remove()
        logger.add(sys.stderr, level="DEBUG")
    
    # 创建基准测试运行器
    runner = BenchmarkRunner(args.test_data, args.output_dir)
    
    # 如果指定了特定配置，只运行该配置
    if args.config:
        runner.benchmark_configs = [c for c in runner.benchmark_configs if c.name == args.config]
        if not runner.benchmark_configs:
            logger.error(f"未找到基准测试配置: {args.config}")
            return
    
    try:
        # 运行基准测试
        logger.info("开始基准测试")
        start_time = time.time()
        
        results = await runner.run_all_benchmarks(args.duration)
        
        total_time = time.time() - start_time
        logger.info(f"所有基准测试完成，总耗时: {total_time:.2f}秒")
        
        # 保存结果
        json_file, csv_file = runner.save_benchmark_results(results)
        
        # 生成报告
        report = runner.generate_benchmark_report(results)
        report_file = runner.output_dir / f"benchmark_report_{int(time.time())}.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info(f"基准测试报告已生成: {report_file}")
        
        # 生成图表
        if args.charts:
            try:
                runner.create_performance_charts(results)
            except Exception as e:
                logger.warning(f"图表生成失败: {e}")
        
        # 打印简要报告
        print("\n" + "="*80)
        print("基准测试完成")
        print("="*80)
        print(report)
        
    except KeyboardInterrupt:
        logger.info("基准测试被用户中断")
    except Exception as e:
        logger.error(f"基准测试失败: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())