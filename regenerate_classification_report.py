#!/usr/bin/env python3
"""
重新生成分类指标报告

使用修改后的代码重新生成 yitu_10_20250805_160103 的分类指标报告，
确保包含正确的召回率、精确率、准确率和F1分数。
"""

import pandas as pd
import sys
import os
from pathlib import Path
import json

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from ai_model_evaluation.services.evaluation_report_generator import EvaluationReportGenerator

def regenerate_classification_report():
    """重新生成分类指标报告"""
    
    # 输入和输出路径
    input_dir = Path("evaluation_results/yitu_10_20250805_160103")
    csv_file = input_dir / "yitu_10_20250805_160103_results.csv"
    
    if not csv_file.exists():
        print(f"❌ 输入文件不存在: {csv_file}")
        return
    
    print(f"📁 读取数据文件: {csv_file}")
    
    # 读取CSV数据
    df = pd.read_csv(csv_file)
    print(f"📊 数据概览:")
    print(f"   总样本数: {len(df)}")
    print(f"   列数: {len(df.columns)}")
    print(f"   列名: {list(df.columns)}")
    print()
    
    # 显示期望结果分布
    if 'expected_result' in df.columns:
        print("📈 期望结果分布:")
        print(df['expected_result'].value_counts())
        print()
    
    # 创建报告生成器
    generator = EvaluationReportGenerator()
    
    # 识别模型列
    model_columns = [col for col in df.columns if col.endswith('_result') and col != 'expected_result']
    print(f"🤖 识别到的模型列: {model_columns}")
    print()
    
    # 计算每个模型的分类指标
    metrics_data = {}
    
    for model_col in model_columns:
        model_name = model_col.replace('_result', '')
        print(f"🔍 计算 {model_name} 的分类指标...")
        
        metrics = generator._calculate_classification_metrics(df, model_col)
        metrics_data[model_name] = metrics
        
        if 'error' in metrics:
            print(f"❌ {model_name} 计算出错: {metrics['error']}")
        else:
            print(f"✅ {model_name} 计算成功")
            print(f"   准确率: {metrics.get('accuracy', 0):.2%}")
            print(f"   精确率: {metrics.get('precision', 0):.2%}")
            print(f"   召回率: {metrics.get('recall', 0):.2%}")
            print(f"   F1分数: {metrics.get('f1_score', 0):.2%}")
            
            # 显示宏平均指标
            macro_avg = metrics.get('macro_avg', {})
            print(f"   宏平均 - 精确率: {macro_avg.get('precision', 0):.2%}")
            print(f"   宏平均 - 召回率: {macro_avg.get('recall', 0):.2%}")
            print(f"   宏平均 - F1分数: {macro_avg.get('f1_score', 0):.2%}")
            
            # 显示总体指标
            overall_metrics = metrics.get('overall_metrics', {})
            print(f"   TP: {overall_metrics.get('tp', 0)}, FP: {overall_metrics.get('fp', 0)}")
            print(f"   FN: {overall_metrics.get('fn', 0)}, TN: {overall_metrics.get('tn', 0)}")
            
            print(f"   正向类别: {metrics.get('positive_classes', [])}")
            print(f"   None类别: {metrics.get('none_class', 'N/A')}")
        print()
    
    # 生成HTML报告
    html_file = input_dir / "classification_metrics_report_new.html"
    print(f"📝 生成HTML报告: {html_file}")
    
    generator._generate_classification_html_report(metrics_data, html_file)
    
    if html_file.exists():
        print(f"✅ HTML报告生成成功")
        print(f"   文件大小: {html_file.stat().st_size} 字节")
        
        # 读取并检查内容
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
            print(f"   内容长度: {len(content)} 字符")
    else:
        print(f"❌ HTML报告生成失败")
    
    # 生成JSON报告
    json_file = input_dir / "classification_metrics_report_new.json"
    print(f"📝 生成JSON报告: {json_file}")
    
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(metrics_data, f, ensure_ascii=False, indent=2)
    
    print(f"✅ JSON报告生成成功")
    
    # 生成文本摘要
    summary_file = input_dir / "classification_metrics_summary_new.txt"
    print(f"📝 生成文本摘要: {summary_file}")
    
    generator._generate_classification_summary(metrics_data, summary_file)
    
    print(f"✅ 文本摘要生成成功")
    
    return html_file, json_file, summary_file

def compare_reports():
    """对比新旧报告"""
    print("=" * 60)
    print("对比新旧报告")
    print("=" * 60)
    
    input_dir = Path("evaluation_results/yitu_10_20250805_160103")
    
    old_html = input_dir / "classification_metrics_report.html"
    new_html = input_dir / "classification_metrics_report_new.html"
    
    if old_html.exists() and new_html.exists():
        # 读取文件大小
        old_size = old_html.stat().st_size
        new_size = new_html.stat().st_size
        
        print(f"旧报告大小: {old_size} 字节")
        print(f"新报告大小: {new_size} 字节")
        print(f"大小变化: {new_size - old_size:+d} 字节")
        
        # 读取内容并检查关键元素
        with open(old_html, 'r', encoding='utf-8') as f:
            old_content = f.read()
        
        with open(new_html, 'r', encoding='utf-8') as f:
            new_content = f.read()
        
        key_elements = [
            "精确率",
            "召回率", 
            "F1分数",
            "混淆矩阵",
            "各类别详细指标",
            "宏平均",
            "加权平均"
        ]
        
        print("\n关键元素对比:")
        for element in key_elements:
            old_has = element in old_content
            new_has = element in new_content
            
            if old_has and new_has:
                status = "✅ 都有"
            elif not old_has and new_has:
                status = "🆕 新增"
            elif old_has and not new_has:
                status = "❌ 缺失"
            else:
                status = "❌ 都无"
            
            print(f"   {element}: {status}")
    
    else:
        print("❌ 无法找到对比文件")

if __name__ == "__main__":
    print("重新生成分类指标报告")
    print("=" * 60)
    
    # 重新生成报告
    html_file, json_file, summary_file = regenerate_classification_report()
    
    # 对比新旧报告
    compare_reports()
    
    print("\n🎉 报告重新生成完成！")
    print(f"新HTML报告: {html_file}")
    print(f"新JSON报告: {json_file}")
    print(f"新文本摘要: {summary_file}")
    print("\n请在浏览器中打开新的HTML报告查看效果。")
