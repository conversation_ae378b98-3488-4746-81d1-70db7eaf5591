# AI模型评估系统配置

providers:
  - id: "tongyi"
    name: "通义千问"
    base_url: "https://dashscope.aliyuncs.com/compatible-mode/v1/"
    api_key: "sk-118e3693f3e84b029e85b9c0d4412a84"
  - id: "hunyuan"
    name: "混元AI"
    base_url: "https://api.hunyuan.cloud.tencent.com/v1/"
    api_key: "sk-FMpOdgAhRkCwfaRFVQgWAlTYFhrEaZLUNsYZXOPgk6ouffCk"
  - id: "baidu"
    name: "百度AI"
    base_url: "https://qianfan.baidubce.com/v2/"
    api_key: "bce-v3/ALTAK-tFI7EaDNOz96ifvHt1Vn0/9c0f28d9b7c343cee89f2eaf3f1a5f52dc0dc405"
  - id: "huoshan"
    name: "火山AI"
    base_url: "https://ark.cn-beijing.volces.com/api/v3/"
    api_key: "a44bd908-ca9f-4c94-a208-7e9717a8b626"

models:
  - id: "qwen-plus"
    provider_id: "tongyi"
    model_name: "qwen-plus-latest"
    display_name: "通义千问Plus"
    temperature: 0.01
    thinking_enabled: false
    max_tokens: 8000
  - id: "通义千问3-235B-A22B"
    provider_id: "tongyi"
    model_name: "qwen3-235b-a22b-instruct-2507"
    display_name: "通义千问3-235B-A22B"
    temperature: 0.01
    thinking_enabled: false
    max_tokens: 8000
  - id: "ERNIE 4.5 Turbo"
    provider_id: "baidu"
    model_name: "ernie-4.5-turbo-32k"
    display_name: "文心一言4.5"
    temperature: 0.01
    thinking_enabled: false
    max_tokens: 8000
  - id: "deepseek-v3"
    provider_id: "baidu"
    model_name: "deepseek-v3"
    display_name: "DeepSeek V3"
    temperature: 0.01
    thinking_enabled: false
    max_tokens: 8000

  - id: "doubao-1.6-seed"
    provider_id: "huoshan"
    model_name: "doubao-seed-1-6-250615"
    display_name: "豆包1.6-seed"
    temperature: 0.01
    thinking_enabled: false
    max_tokens: 8000
  - id: "doubao1.5 pro"
    provider_id: "huoshan"
    model_name: "doubao-1-5-pro-32k-250115"
    display_name: "doubao1.5 pro"
    temperature: 0.01
    thinking_enabled: false
    max_tokens: 8000
    
  - id: "hunyuan-Turbo"
    provider_id: "hunyuan"
    model_name: "hunyuan-turbos-latest"
    display_name: "hunyuan-Turbo"
    temperature: 0.01
    thinking_enabled: false
    max_tokens: 8000

prompt_templates:
  - id: "default"
    name: "默认模板"
    template: "{original_prompt}"
    variables: ["original_prompt"]
    description: "基础的提示词模板"
  - id: "comparison"
    name: "对比模板"
    template: "请对比分析以下内容：{original_prompt}\n\n请从以下角度进行分析：\n1. {aspect_1}\n2. {aspect_2}\n3. {aspect_3}"
    variables: ["original_prompt", "aspect_1", "aspect_2", "aspect_3"]
    description: "用于对比分析的模板"
  - id: "simple"
    name: "简单模板"
    template: "{original_prompt} - A: {variable_a}, B: {variable_b}"
    variables: ["original_prompt", "variable_a", "variable_b"]
    description: "简化的提示词模板"
  - id: "yitu"
    name: "意图识别模板"
    template_file: "intent_templates.yaml"  # 引用外部模板文件
    template_key: "intent_recognition.yitu.template"
    variables: ["live_comment"]
    description: "用于识别意图的模板（从外部文件加载）"

# 性能优化配置
performance:
  # 全局性能设置
  global_performance:
    # 全局最大并发请求数（优化提升）
    max_concurrent_requests: 1200
    
    # 连接池配置（优化提升）
    connection_pool_size: 500
    
    # 请求超时配置（秒）
    request_timeout: 45
    connect_timeout: 10
    
    # 启用HTTP/2
    enable_http2: true
    
    # 启用连接复用
    enable_connection_reuse: true
    
    # DNS缓存TTL（秒）
    dns_cache_ttl: 600
    
    # 启用连接预热
    enable_connection_warmup: true
    warmup_connections: 50

  # 提供商特定的性能配置
  provider_performance:
    openai:
      max_concurrent: 150
      retry_config:
        max_retries: 3
        base_delay: 0.1
        max_delay: 2.0
        backoff_factor: 1.5
      rate_limit:
        requests_per_minute: 5000
        tokens_per_minute: 200000
    
    zhipu:
      max_concurrent: 120
      retry_config:
        max_retries: 3
        base_delay: 0.2
        max_delay: 3.0
        backoff_factor: 1.8
      rate_limit:
        requests_per_minute: 3000
        tokens_per_minute: 150000
    
    tongyi:
      max_concurrent: 150
      retry_config:
        max_retries: 4
        base_delay: 0.1
        max_delay: 2.5
        backoff_factor: 1.6
      rate_limit:
        requests_per_minute: 4000
        tokens_per_minute: 180000
    
    hunyuan:
      max_concurrent: 100
      retry_config:
        max_retries: 3
        base_delay: 0.15
        max_delay: 2.0
        backoff_factor: 1.7
      rate_limit:
        requests_per_minute: 2500
        tokens_per_minute: 120000
    
    baidu:
      max_concurrent: 120
      retry_config:
        max_retries: 3
        base_delay: 0.2
        max_delay: 3.0
        backoff_factor: 1.8
      rate_limit:
        requests_per_minute: 3000
        tokens_per_minute: 140000
    
    huoshan:
      max_concurrent: 100
      retry_config:
        max_retries: 3
        base_delay: 0.15
        max_delay: 2.5
        backoff_factor: 1.6
      rate_limit:
        requests_per_minute: 2800
        tokens_per_minute: 130000

  # 批处理优化配置
  batch_optimization:
    # 启用智能批处理
    enable_smart_batching: true
    
    # 批处理大小配置
    batch_size:
      min: 30  # 减少最小批次大小
      max: 500  # 增加最大批次大小
      default: 200  # 增加默认批次大小
    
    # 启用流式处理
    enable_streaming: false
    streaming_threshold: 300  # 降低流式处理阈值
    
    # 自适应批处理大小
    adaptive_batch_size: true
    
    # 批处理超时（秒）
    batch_timeout: 30

  # 内存优化配置
  memory_optimization:
    # 启用内存优化
    enable: true
    
    # 垃圾回收间隔（秒）
    gc_interval: 180  # 减少GC间隔
    
    # 最大内存使用（GB）
    max_memory_usage: 6  # 增加内存限制
    
    # 启用内存监控
    enable_monitoring: true
    
    # 内存清理阈值（%）
    cleanup_threshold: 75

  # 缓存配置
  cache:
    # 响应缓存
    response_cache:
      enable: true
      ttl: 1800  # 增加缓存时间
      max_size: 2000  # 增加缓存大小
    
    # 提示词缓存
    prompt_cache:
      enable: true
      ttl: 3600
      max_size: 1000
    
    # 连接缓存
    connection_cache:
      enable: true
      ttl: 1800
      max_size: 500

  # 监控和日志配置
  monitoring:
    # 性能监控
    enable_performance_monitoring: true
    
    # 指标收集间隔（秒）
    metrics_collection_interval: 30
    
    # 日志级别
    log_level: "INFO"
    
    # 性能报告
    performance_report:
      enable: true
      interval: 300  # 5分钟
      detailed: true

  # 错误处理和重试配置
  error_handling:
    # 全局重试配置
    global_retry:
      enable: true
      max_retries: 4  # 增加重试次数
      base_delay: 0.1
      max_delay: 5.0
      backoff_factor: 2.0
    
    # 可重试的HTTP状态码
    retryable_status_codes: [429, 500, 502, 503, 504, 520, 521, 522, 524]
    
    # 错误分类
    error_classification:
      network_errors: ["ConnectionError", "TimeoutError", "DNSError"]
      rate_limit_errors: ["RateLimitError", "QuotaExceededError"]
      server_errors: ["InternalServerError", "BadGatewayError"]
    
    # 熔断器配置
    circuit_breaker:
      enable: true
      failure_threshold: 10  # 降低失败阈值
      recovery_timeout: 30
      half_open_max_calls: 5

  # 资源限制
  resource_limits:
    # 内存限制（MB）
    max_memory_mb: 6144  # 增加内存限制
    
    # CPU限制（核心数）
    max_cpu_cores: 8  # 增加CPU限制
    
    # 磁盘空间限制（GB）
    max_disk_gb: 20
    
    # 网络带宽限制（Mbps）
    max_bandwidth_mbps: 1000

  # 优化策略
  optimization_strategies:
    # 请求合并
    request_merging:
      enable: true
      merge_window_ms: 50  # 减少合并窗口
      max_merge_size: 20  # 增加合并大小
    
    # 连接预热
    connection_warmup:
      enable: true
      warmup_connections: 100  # 增加预热连接数
      warmup_timeout: 10
    
    # 负载均衡
    load_balancing:
      strategy: "round_robin"
      health_check_interval: 30
    
    # 请求优先级
    request_priority:
      enable: true
      high_priority_models: ["qwen-plus", "通义千问3-235B-A22B", "ERNIE 4.5 Turbo"]
      priority_queue_size: 200  # 增加优先级队列大小

# 场景化配置
scenarios:
  small_scale:
    max_concurrent_requests: 200
    batch_size: 50
    enable_streaming: false
    enable_aggressive_retry: false
    memory_optimization: false
    enable_monitoring: false
    enable_cache: true
  
  medium_scale:
    max_concurrent_requests: 600
    batch_size: 150
    enable_streaming: true
    enable_aggressive_retry: true
    memory_optimization: true
    enable_monitoring: true
    enable_cache: true
  
  large_scale_test:
    max_concurrent_requests: 1200
    batch_size: 300
    enable_streaming: true
    enable_aggressive_retry: true
    memory_optimization: true
    enable_monitoring: true
    enable_cache: true
  
  production:
    max_concurrent_requests: 1500  # 增加生产环境并发
    batch_size: 400  # 增加批处理大小
    enable_streaming: true
    enable_aggressive_retry: true
    memory_optimization: true
    enable_monitoring: true
    enable_cache: true
    enable_circuit_breaker: true
    enable_load_balancing: true

# 实验性功能
experimental:
  # HTTP/3支持
  enable_http3: false
  
  # 请求管道化
  enable_pipelining: true  # 启用管道化
  
  # 智能重试（基于错误类型）
  smart_retry: true
  
  # 动态并发调整
  dynamic_concurrency: true
  
  # 预测性缓存
  predictive_caching: true  # 启用预测性缓存
  
  # 异步IO优化
  async_io_optimization: true
  
  # 内存池
  memory_pool: true