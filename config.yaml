# AI模型评估系统配置

providers:
  # - id: "openai"
  #   name: "OpenAI"
  #   base_url: "https://api.openai.com/v1/"
  #   api_key: "${OPENAI_API_KEY}"
  - id: "zhipu"
    name: "智谱AI"
    base_url: "https://open.bigmodel.cn/api/paas/v4/"
    api_key: "4f457c3b758946bd87a17f2c2c6ce4ce.XSZ7lasxr8KcKPvj"
  - id: "tongyi"
    name: "通义千问"
    base_url: "https://dashscope.aliyuncs.com/compatible-mode/v1/"
    api_key: "sk-118e3693f3e84b029e85b9c0d4412a84"
  - id: "hunyuan"
    name: "混元AI"
    base_url: "https://api.hunyuan.cloud.tencent.com/v1/"
    api_key: "sk-FMpOdgAhRkCwfaRFVQgWAlTYFhrEaZLUNsYZXOPgk6ouffCk"
  - id: "baidu"
    name: "百度AI"
    base_url: "https://qianfan.baidubce.com/v2/"
    api_key: "bce-v3/ALTAK-tFI7EaDNOz96ifvHt1Vn0/9c0f28d9b7c343cee89f2eaf3f1a5f52dc0dc405"
  - id: "huoshan"
    name: "火山AI"
    base_url: "https://ark.cn-beijing.volces.com/api/v3/"
    api_key: "a44bd908-ca9f-4c94-a208-7e9717a8b626"

models:
  - id: "qwen-plus"
    provider_id: "tongyi"
    model_name: "qwen-plus-latest"
    display_name: "通义千问Plus"
    temperature: 0.01
    thinking_enabled: false
    max_tokens: 8000
  - id: "通义千问3-235B-A22B"
    provider_id: "tongyi"
    model_name: "qwen3-235b-a22b-instruct-2507"
    display_name: "通义千问3-235B-A22B"
    temperature: 0.01
    thinking_enabled: false
    max_tokens: 8000

  - id: "ERNIE 4.5 Turbo"
    provider_id: "baidu"
    model_name: "ernie-4.5-turbo-32k"
    display_name: "文心一言4.5"
    temperature: 0.01
    thinking_enabled: false
    max_tokens: 8000
  - id: "deepseek-v3"
    provider_id: "baidu"
    model_name: "deepseek-v3"
    display_name: "DeepSeek V3"
    temperature: 0.01
    thinking_enabled: false
    max_tokens: 8000

  - id: "doubao-1.6-seed"
    provider_id: "huoshan"
    model_name: "doubao-seed-1-6-250615"
    display_name: "豆包1.6-seed"
    temperature: 0.01
    thinking_enabled: false
    max_tokens: 8000
  - id: "doubao1.5 pro"
    provider_id: "huoshan"
    model_name: "doubao-1-5-pro-32k-250115"
    display_name: "doubao1.5 pro"
    temperature: 0.01
    thinking_enabled: false
    max_tokens: 8000
    
  - id: "hunyuan-Turbo"
    provider_id: "hunyuan"
    model_name: "hunyuan-turbos-latest"
    display_name: "hunyuan-Turbo"
    temperature: 0.01
    thinking_enabled: false
    max_tokens: 8000

prompt_templates:
  - id: "default"
    name: "默认模板"
    template: "{original_prompt}\n\n变量A: {variable_a}\n变量B: {variable_b}"
    variables: ["original_prompt", "variable_a", "variable_b"]
    description: "默认的提示词模板，包含原始提示词和两个变量"
  - id: "comparison"
    name: "对比分析模板"
    template: "请对比分析以下两个选项：\n\n问题：{original_prompt}\n\n选项A：{variable_a}\n选项B：{variable_b}\n\n请从多个维度进行详细分析，并给出建议。"
    variables: ["original_prompt", "variable_a", "variable_b"]
    description: "用于对比分析的详细模板"
  - id: "simple"
    name: "简单模板"
    template: "{original_prompt} - A: {variable_a}, B: {variable_b}"
    variables: ["original_prompt", "variable_a", "variable_b"]
    description: "简化的提示词模板"
  - id: "yitu"
    name: "意图识别模板"
    template: "【角色设定】
你是一个汽车行业4S店直播领域的专家，善于分析客户发送的弹幕文本，理解背后的意图，识别潜在购车用户。

【核心任务】
识别用户弹幕消息是否包含以下意图，只能选择一个最符合的意图：

<意图列表>
意图名称：问优惠
意图定义：用户询问购车优惠、补贴、促销活动或折扣信息
相似问法：
反例：

意图名称：要买车
意图定义：用户明确表达购车意向，咨询购车流程、提车时间或希望获得车型推荐
相似问法：代下步、刚才在懂车帝看了
反例：

意图名称：问地址
意图定义：用户询问4S店的具体地理位置、门店地址、导航路线、说明自己所在城市或区域等
相似问法：[具体城市]、
反例：

意图名称：金融政策
意图定义：用户咨询贷款政策，包括全款、分期、贷款等
相似问法：
反例：

意图名称：问颜色
意图定义：用户询问可选车身颜色、内饰颜色或个性化配色方案
相似问法：
反例：

意图名称：看车型
意图定义：用户询问具体车型的型号、版本或希望了解不同车型的特点
相似问法：介绍下、
反例：

意图名称：问价格
意图定义：用户询问价格、裸车价、落地价、分期价格等
相似问法：高配的加4000、[车系]也要快[数字]了、[城市]便宜啊
反例：

意图名称：想卖车
意图定义：用户表达卖车意向，咨询收车、车辆回收、评估、检测、收购价格或卖车流程
相似问法：收车吗、能上门检测吗、
反例：

意图名称：问置换
意图定义：用户询问旧车置换新车的政策、本品置换、非本品置换、流程、补贴或估值计算方式
相似问法：没有置换、无置换、[xxx]的旧车
反例：

意图名称：问配置
意图定义：用户询问具体车型的参数、配置、座椅、功能亮点或不同配置版本差异
相似问法：安全性怎样、续航多少、原车音响、
反例：

意图名称：问试乘试驾
意图定义：用户询问试驾相关问题，包括是否可以上门试驾
相似问法：可以来接吗、
反例：

意图名称：问库存车
意图定义：用户询问车辆是否库存车
相似问法：
反例：

意图名称：问政策
意图定义：用户咨询购车相关政策，如本地牌照政策、补贴政策、保养政策、限行政策等
相似问法：免费保养吗、包牌，包税，包保险、有售后服务吗、
反例：

意图名称：问联系方式
意图定义：用户询问4S店的电话、微信、在线客服等联系方式以便进一步沟通
相似问法：
反例：

意图名称：撩妹
意图定义：用户发送与汽车销售无关的调侃、搭讪等内容，无明确购车咨询意图
相似问法：
反例：

</意图列表>

【意图分类规则】
1. 仔细分析弹幕文本的核心意图
2. 优先匹配最直接相关的意图
3. 如果一条弹幕包含多个意图，选择最主要的一个
4. 未命中任何意图时返回：none
5. 只返回意图名称，不要解释，不要修改意图枚举值的名称


【输出格式】
问优惠

弹幕内容如下：
<input>
{live_comment}
</input>

"
    variables: ["live_comment"]
    description: "用于识别意图的模板"

# 性能优化配置
performance:
  # 全局性能设置
  global_performance:
    # 全局最大并发请求数（大幅提升）
    max_concurrent_requests: 800
    
    # 连接池配置
    connection_pool_size: 300
    
    # 请求超时配置（秒）
    request_timeout: 60
    connect_timeout: 15
    
    # 启用HTTP/2
    enable_http2: true
    
    # 启用连接复用
    enable_connection_reuse: true
    
    # DNS缓存TTL（秒）
    dns_cache_ttl: 300

  # 提供商特定的性能配置
  provider_performance:
    openai:
      max_concurrent: 120
      retry_config:
        max_retries: 3
        base_delay: 0.1
        max_delay: 2.0
        backoff_factor: 1.5
      rate_limit:
        requests_per_minute: 5000
        tokens_per_minute: 200000
    
    zhipu:
      max_concurrent: 100
      retry_config:
        max_retries: 3
        base_delay: 0.2
        max_delay: 3.0
        backoff_factor: 1.8
      rate_limit:
        requests_per_minute: 3000
        tokens_per_minute: 150000
    
    tongyi:
      max_concurrent: 120
      retry_config:
        max_retries: 3
        base_delay: 0.15
        max_delay: 2.5
        backoff_factor: 1.6
      rate_limit:
        requests_per_minute: 4000
        tokens_per_minute: 180000
    
    hunyuan:
      max_concurrent: 80
      retry_config:
        max_retries: 3
        base_delay: 0.3
        max_delay: 4.0
        backoff_factor: 2.0
      rate_limit:
        requests_per_minute: 2500
        tokens_per_minute: 120000
    
    baidu:
      max_concurrent: 80
      retry_config:
        max_retries: 3
        base_delay: 0.25
        max_delay: 3.0
        backoff_factor: 1.7
      rate_limit:
        requests_per_minute: 2000
        tokens_per_minute: 100000
    
    huoshan:
      max_concurrent: 100
      retry_config:
        max_retries: 3
        base_delay: 0.2
        max_delay: 2.5
        backoff_factor: 1.5
      rate_limit:
        requests_per_minute: 3500
        tokens_per_minute: 150000

  # 批处理优化配置
  batch_processing:
    # 启用智能批处理
    enable_smart_batching: true
    
    # 批处理大小配置
    batch_size:
      min: 50
      max: 300
      default: 150
    
    # 启用流式处理（适用于大数据集）
    enable_streaming: true
    streaming_threshold: 500
    
    # 启用自适应批处理大小
    adaptive_batch_size: true
    
    # 内存优化
    memory_optimization:
      enable: true
      gc_interval: 300
      max_memory_usage: "4GB"

  # 缓存配置
  caching:
    # 启用响应缓存
    enable_response_cache: true
    
    # 缓存TTL（秒）
    cache_ttl: 3600
    
    # 最大缓存大小（MB）
    max_cache_size: 1000
    
    # 启用提示词缓存
    enable_prompt_cache: true

  # 监控和日志配置
  monitoring:
    # 启用性能监控
    enable_performance_monitoring: true
    
    # 监控指标收集间隔（秒）
    metrics_interval: 10
    
    # 启用详细日志
    enable_detailed_logging: false
    
    # 日志级别
    log_level: "INFO"
    
    # 性能报告
    performance_report:
      enable: true
      include_provider_breakdown: true
      include_timing_analysis: true

  # 错误处理和重试配置
  error_handling:
    # 全局重试配置
    global_retry:
      max_retries: 3
      base_delay: 0.1
      max_delay: 5.0
      backoff_factor: 1.5
    
    # 可重试的HTTP状态码
    retryable_status_codes: [429, 500, 502, 503, 504]
    
    # 错误分类处理
    error_classification:
      rate_limit_errors: [429]
      server_errors: [500, 502, 503, 504]
      client_errors: [400, 401, 403, 404]
    
    # 熔断器配置
    circuit_breaker:
      enable: true
      failure_threshold: 15
      recovery_timeout: 60

  # 资源限制配置
  resource_limits:
    # 内存限制（MB）
    max_memory_usage: 4096
    
    # CPU使用率限制（百分比）
    max_cpu_usage: 85
    
    # 磁盘空间限制（MB）
    max_disk_usage: 2048
    
    # 网络带宽限制（Mbps）
    max_bandwidth: 200

  # 优化策略配置
  optimization_strategies:
    # 请求合并
    request_batching:
      enable: true
      max_batch_size: 100
      batch_timeout: 50
    
    # 连接预热
    connection_warmup:
      enable: true
      warmup_requests: 10
    
    # 负载均衡
    load_balancing:
      enable: true
      strategy: "round_robin"
    
    # 请求优先级
    request_priority:
      enable: true
      high_priority_models: ["qwen-plus", "通义千问3-235B-A22B"]
      low_priority_models: []

  # 场景特定配置
  scenarios:
    # 小规模测试（< 100个样本）
    small_scale:
      max_concurrent: 100
      batch_size: 50
      enable_streaming: false
      aggressive_retry: false
    
    # 中等规模测试（100-1000个样本）
    medium_scale:
      max_concurrent: 400
      batch_size: 150
      enable_streaming: true
      aggressive_retry: true
    
    # 大规模测试（> 1000个样本）
    large_scale:
      max_concurrent: 800
      batch_size: 300
      enable_streaming: true
      aggressive_retry: true
      memory_optimization: true
    
    # 生产环境
    production:
      max_concurrent: 600
      batch_size: 200
      enable_streaming: true
      enable_monitoring: true
      enable_caching: true
      conservative_retry: true

  # 实验性功能
  experimental:
    # HTTP/3支持
    enable_http3: false
    
    # 请求管道化
    enable_pipelining: false
    
    # 智能重试（基于错误类型）
    smart_retry: true
    
    # 动态并发调整
    dynamic_concurrency: true
    
    # 预测性缓存
    predictive_caching: false