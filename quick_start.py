#!/usr/bin/env python3
"""AI模型评估系统快速启动脚本

这个脚本提供了一键式部署和启动优化后的AI模型评估系统的功能。
适用于快速演示、开发测试和生产部署。
"""

import os
import sys
import asyncio
import subprocess
import time
import json
import yaml
from pathlib import Path
from typing import Dict, List, Optional, Any
import argparse
from loguru import logger
from dataclasses import dataclass


@dataclass
class QuickStartConfig:
    """快速启动配置"""
    mode: str  # demo, dev, prod
    skip_tests: bool = False
    skip_dependencies: bool = False
    auto_open_browser: bool = True
    port: int = 8000
    host: str = "localhost"
    log_level: str = "INFO"
    enable_monitoring: bool = True
    optimization_level: str = "intermediate"


class QuickStartManager:
    """快速启动管理器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root).resolve()
        self.venv_path = self.project_root / ".venv"
        self.config_file = None
        self.process_ids = []
        
        # 预定义配置
        self.mode_configs = {
            'demo': {
                'config_file': 'config.yaml',
                'optimization_level': 'basic',
                'max_concurrent': 10,
                'enable_debug': True,
                'log_level': 'INFO',
                'description': '演示模式 - 基础优化，适合快速演示'
            },
            'dev': {
                'config_file': 'config_optimized.yaml',
                'optimization_level': 'intermediate',
                'max_concurrent': 50,
                'enable_debug': True,
                'log_level': 'DEBUG',
                'description': '开发模式 - 中级优化，适合开发测试'
            },
            'prod': {
                'config_file': 'config_optimized.yaml',
                'optimization_level': 'extreme',
                'max_concurrent': 200,
                'enable_debug': False,
                'log_level': 'WARNING',
                'description': '生产模式 - 极限优化，适合生产环境'
            }
        }
    
    def print_banner(self):
        """打印启动横幅"""
        banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                    🚀 AI模型评估系统 - 快速启动工具                          ║
║                                                                              ║
║  ⚡ 高性能 | 🔧 易配置 | 📊 智能监控 | 🎯 批量处理 | 💾 内存优化           ║
╚══════════════════════════════════════════════════════════════════════════════╝
"""
        print(banner)
    
    def check_environment(self) -> bool:
        """检查运行环境"""
        logger.info("🔍 检查运行环境...")
        
        # 检查Python版本
        python_version = sys.version_info
        if python_version < (3, 8):
            logger.error(f"❌ Python版本过低: {python_version}, 需要3.8+")
            return False
        
        logger.info(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
        
        # 检查项目结构
        required_files = [
            'src/ai_model_evaluation/optimized_main.py',
            'src/ai_model_evaluation/services/enhanced_api_client.py',
            'src/ai_model_evaluation/services/smart_batch_processor.py'
        ]
        
        missing_files = []
        for file_path in required_files:
            if not (self.project_root / file_path).exists():
                missing_files.append(file_path)
        
        if missing_files:
            logger.error(f"❌ 缺少必要文件: {missing_files}")
            return False
        
        logger.info("✅ 项目结构检查通过")
        return True
    
    def setup_virtual_environment(self) -> bool:
        """设置虚拟环境"""
        logger.info("🐍 设置Python虚拟环境...")
        
        try:
            # 检查是否已存在虚拟环境
            if self.venv_path.exists():
                logger.info("✅ 虚拟环境已存在")
                return True
            
            # 创建虚拟环境
            logger.info("创建虚拟环境...")
            result = subprocess.run(
                [sys.executable, '-m', 'venv', str(self.venv_path)],
                cwd=self.project_root,
                capture_output=True,
                text=True
            )
            
            if result.returncode != 0:
                logger.error(f"❌ 创建虚拟环境失败: {result.stderr}")
                return False
            
            logger.info("✅ 虚拟环境创建成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 设置虚拟环境失败: {e}")
            return False
    
    def install_dependencies(self, quick_start_config: QuickStartConfig) -> bool:
        """安装依赖"""
        if quick_start_config.skip_dependencies:
            logger.info("⏭️ 跳过依赖安装")
            return True
        
        logger.info("📦 安装项目依赖...")
        
        try:
            # 确定pip路径
            if os.name == 'nt':  # Windows
                pip_path = self.venv_path / "Scripts" / "pip"
            else:  # Unix/Linux/macOS
                pip_path = self.venv_path / "bin" / "pip"
            
            # 创建requirements.txt（如果不存在）
            requirements_file = self.project_root / "requirements.txt"
            if not requirements_file.exists():
                self._create_requirements_file()
            
            # 安装依赖
            logger.info("正在安装依赖包...")
            result = subprocess.run(
                [str(pip_path), 'install', '-r', str(requirements_file)],
                cwd=self.project_root,
                capture_output=True,
                text=True
            )
            
            if result.returncode != 0:
                logger.error(f"❌ 依赖安装失败: {result.stderr}")
                return False
            
            logger.info("✅ 依赖安装成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 安装依赖时出错: {e}")
            return False
    
    def _create_requirements_file(self):
        """创建requirements.txt文件"""
        requirements = [
            "aiohttp>=3.8.0",
            "pydantic>=2.0.0",
            "loguru>=0.7.0",
            "PyYAML>=6.0",
            "pandas>=2.0.0",
            "numpy>=1.24.0",
            "asyncio-throttle>=1.0.0",
            "aiofiles>=23.0.0",
            "psutil>=5.9.0",
            "fastapi>=0.100.0",
            "uvicorn>=0.23.0",
            "httpx>=0.24.0",
            "tenacity>=8.2.0",
            "cachetools>=5.3.0"
        ]
        
        requirements_file = self.project_root / "requirements.txt"
        with open(requirements_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(requirements))
        
        logger.info(f"📝 创建requirements.txt: {len(requirements)}个依赖")
    
    def create_config_files(self, quick_start_config: QuickStartConfig) -> bool:
        """创建配置文件"""
        logger.info("⚙️ 创建配置文件...")
        
        try:
            mode_config = self.mode_configs[quick_start_config.mode]
            config_file = self.project_root / mode_config['config_file']
            
            # 如果配置文件已存在，备份
            if config_file.exists():
                backup_file = config_file.with_suffix(f".backup.{int(time.time())}.yaml")
                config_file.rename(backup_file)
                logger.info(f"📋 备份现有配置: {backup_file.name}")
            
            # 创建新配置
            config = self._generate_config(quick_start_config, mode_config)
            
            with open(config_file, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
            
            self.config_file = config_file
            logger.info(f"✅ 配置文件创建成功: {config_file.name}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 创建配置文件失败: {e}")
            return False
    
    def _generate_config(self, quick_start_config: QuickStartConfig, mode_config: Dict[str, Any]) -> Dict[str, Any]:
        """生成配置"""
        config = {
            'providers': {
                'openai': {
                    'api_key': '${OPENAI_API_KEY}',
                    'base_url': 'https://api.openai.com/v1',
                    'timeout': 30,
                    'max_retries': 3,
                    'max_concurrent': min(mode_config['max_concurrent'], 20)
                },
                'zhipu': {
                    'api_key': '${ZHIPU_API_KEY}',
                    'base_url': 'https://open.bigmodel.cn/api/paas/v4',
                    'timeout': 30,
                    'max_retries': 3,
                    'max_concurrent': min(mode_config['max_concurrent'], 10)
                }
            },
            'models': {
                'gpt-3.5-turbo': {
                    'provider': 'openai',
                    'max_concurrent': min(mode_config['max_concurrent'] // 2, 10),
                    'priority_weight': 1.0
                },
                'gpt-4o-mini': {
                    'provider': 'openai',
                    'max_concurrent': min(mode_config['max_concurrent'] // 4, 5),
                    'priority_weight': 1.2
                },
                'glm-4-flash': {
                    'provider': 'zhipu',
                    'max_concurrent': min(mode_config['max_concurrent'] // 3, 8),
                    'priority_weight': 0.9
                }
            },
            'prompts': {
                'simple_qa': {
                    'template': '问题: {question}\n请简洁地回答这个问题。',
                    'variables': ['question']
                },
                'text_classification': {
                    'template': '请对以下文本进行分类：\n\n文本: {text}\n\n分类选项: {categories}\n\n分类结果:',
                    'variables': ['text', 'categories']
                },
                'sentiment_analysis': {
                    'template': '请分析以下文本的情感倾向：\n\n文本: {text}\n\n情感分析结果（正面/负面/中性）:',
                    'variables': ['text']
                }
            },
            'performance': {
                'global_max_concurrent': mode_config['max_concurrent'],
                'enable_adaptive_concurrency': True,
                'batch_strategy': 'adaptive_size',
                'min_batch_size': 3 if quick_start_config.mode == 'demo' else 5,
                'max_batch_size': 8 if quick_start_config.mode == 'demo' else 15,
                'enable_memory_optimization': True,
                'memory_threshold_mb': 200 if quick_start_config.mode == 'demo' else 500,
                'enable_request_cache': True,
                'cache_ttl_seconds': 300,
                'enable_streaming': quick_start_config.mode != 'demo',
                'enable_compression': quick_start_config.mode != 'demo',
                'enable_detailed_metrics': quick_start_config.enable_monitoring,
                'log_level': mode_config['log_level']
            },
            'server': {
                'host': quick_start_config.host,
                'port': quick_start_config.port,
                'enable_cors': True,
                'enable_docs': mode_config['enable_debug']
            }
        }
        
        return config
    
    def run_tests(self, quick_start_config: QuickStartConfig) -> bool:
        """运行测试"""
        if quick_start_config.skip_tests:
            logger.info("⏭️ 跳过测试")
            return True
        
        logger.info("🧪 运行快速测试...")
        
        try:
            # 确定python路径
            if os.name == 'nt':  # Windows
                python_path = self.venv_path / "Scripts" / "python"
            else:  # Unix/Linux/macOS
                python_path = self.venv_path / "bin" / "python"
            
            # 运行集成测试
            test_file = self.project_root / "tests" / "test_integration.py"
            if test_file.exists():
                logger.info("运行集成测试...")
                result = subprocess.run(
                    [str(python_path), str(test_file)],
                    cwd=self.project_root,
                    capture_output=True,
                    text=True,
                    timeout=120
                )
                
                if result.returncode != 0:
                    logger.warning(f"⚠️ 集成测试失败: {result.stderr}")
                    logger.info("继续启动系统（测试失败不影响演示）")
                else:
                    logger.info("✅ 集成测试通过")
            else:
                logger.info("📝 集成测试文件不存在，跳过测试")
            
            return True
            
        except Exception as e:
            logger.warning(f"⚠️ 运行测试时出错: {e}")
            logger.info("继续启动系统")
            return True
    
    def start_system(self, quick_start_config: QuickStartConfig) -> bool:
        """启动系统"""
        logger.info("🚀 启动AI模型评估系统...")
        
        try:
            # 确定python路径
            if os.name == 'nt':  # Windows
                python_path = self.venv_path / "Scripts" / "python"
            else:  # Unix/Linux/macOS
                python_path = self.venv_path / "bin" / "python"
            
            # 创建启动脚本
            startup_script = self._create_startup_script(quick_start_config)
            
            # 启动系统
            logger.info(f"在端口 {quick_start_config.port} 启动服务...")
            
            # 使用subprocess.Popen以便后台运行
            process = subprocess.Popen(
                [str(python_path), str(startup_script)],
                cwd=self.project_root,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            self.process_ids.append(process.pid)
            
            # 等待系统启动
            logger.info("等待系统启动...")
            time.sleep(3)
            
            # 检查进程是否还在运行
            if process.poll() is None:
                logger.info("✅ 系统启动成功")
                
                # 显示访问信息
                self._show_access_info(quick_start_config)
                
                # 自动打开浏览器
                if quick_start_config.auto_open_browser:
                    self._open_browser(quick_start_config)
                
                return True
            else:
                stdout, stderr = process.communicate()
                logger.error(f"❌ 系统启动失败: {stderr}")
                return False
            
        except Exception as e:
            logger.error(f"❌ 启动系统时出错: {e}")
            return False
    
    def _create_startup_script(self, quick_start_config: QuickStartConfig) -> Path:
        """创建启动脚本"""
        mode_config = self.mode_configs[quick_start_config.mode]
        
        script_content = f'''#!/usr/bin/env python3
"""快速启动脚本 - {quick_start_config.mode}模式"""

import asyncio
import sys
import signal
from pathlib import Path
from loguru import logger

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from src.ai_model_evaluation.optimized_main import (
    OptimizedAIModelEvaluator,
    OptimizedEvaluationConfig,
    BatchStrategy
)

# 全局变量
evaluator = None

def signal_handler(signum, frame):
    """信号处理器"""
    logger.info(f"接收到信号 {{signum}}，正在关闭系统...")
    if evaluator:
        asyncio.create_task(evaluator.shutdown())
    sys.exit(0)

async def main():
    """主函数"""
    global evaluator
    
    # 配置日志
    logger.remove()
    logger.add(
        sys.stderr,
        level="{quick_start_config.log_level}",
        format="<green>{{time:HH:mm:ss}}</green> | <level>{{level: <8}}</level> | <cyan>{{name}}</cyan> - <level>{{message}}</level>"
    )
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    logger.info("🚀 启动AI模型评估系统 - {quick_start_config.mode}模式")
    
    try:
        # 创建优化配置
        optimization_config = OptimizedEvaluationConfig(
            global_max_concurrent={mode_config['max_concurrent']},
            enable_adaptive_concurrency=True,
            batch_strategy=BatchStrategy.ADAPTIVE_SIZE,
            enable_memory_optimization=True,
            enable_request_cache=True,
            enable_streaming={str(quick_start_config.mode != 'demo').lower()},
            enable_compression={str(quick_start_config.mode != 'demo').lower()},
            enable_detailed_metrics={str(quick_start_config.enable_monitoring).lower()}
        )
        
        # 创建评估器
        evaluator = OptimizedAIModelEvaluator(
            config_path="{mode_config['config_file']}",
            optimization_config=optimization_config,
            enable_monitoring={str(quick_start_config.enable_monitoring).lower()}
        )
        
        # 启动Web服务器
        import uvicorn
        from fastapi import FastAPI
        from fastapi.middleware.cors import CORSMiddleware
        from fastapi.responses import HTMLResponse
        
        app = FastAPI(
            title="AI模型评估系统",
            description="高性能AI模型评估和批量处理系统",
            version="1.0.0",
            docs_url="/docs" if {str(mode_config['enable_debug']).lower()} else None
        )
        
        # 添加CORS中间件
        app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        @app.get("/", response_class=HTMLResponse)
        async def root():
            return """
            <!DOCTYPE html>
            <html>
            <head>
                <title>AI模型评估系统</title>
                <meta charset="utf-8">
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }}
                    .container {{ max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
                    h1 {{ color: #333; text-align: center; }}
                    .status {{ background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0; }}
                    .info {{ background: #e8f4fd; padding: 15px; border-radius: 5px; margin: 20px 0; }}
                    .links {{ display: flex; gap: 15px; justify-content: center; margin: 30px 0; }}
                    .links a {{ background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }}
                    .links a:hover {{ background: #0056b3; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>🚀 AI模型评估系统</h1>
                    <div class="status">
                        <strong>✅ 系统状态:</strong> 运行中 ({quick_start_config.mode}模式)
                    </div>
                    <div class="info">
                        <strong>📊 系统信息:</strong><br>
                        • 优化级别: {mode_config['optimization_level']}<br>
                        • 最大并发: {mode_config['max_concurrent']}<br>
                        • 监控状态: {'启用' if quick_start_config.enable_monitoring else '禁用'}<br>
                        • 服务地址: http://{quick_start_config.host}:{quick_start_config.port}
                    </div>
                    <div class="links">
                        <a href="/health">健康检查</a>
                        <a href="/metrics">性能指标</a>
                        {'<a href="/docs">API文档</a>' if mode_config['enable_debug'] else ''}
                    </div>
                </div>
            </body>
            </html>
            """
        
        @app.get("/health")
        async def health_check():
            try:
                health_status = await evaluator.get_health_status()
                return {{"status": "healthy", "details": health_status}}
            except Exception as e:
                return {{"status": "unhealthy", "error": str(e)}}
        
        @app.get("/metrics")
        async def get_metrics():
            try:
                metrics = await evaluator.get_performance_metrics()
                return {{"status": "success", "metrics": metrics}}
            except Exception as e:
                return {{"status": "error", "error": str(e)}}
        
        # 启动服务器
        config = uvicorn.Config(
            app,
            host="{quick_start_config.host}",
            port={quick_start_config.port},
            log_level="{quick_start_config.log_level.lower()}",
            access_log=False
        )
        server = uvicorn.Server(config)
        
        logger.info(f"🌐 Web服务器启动: http://{quick_start_config.host}:{quick_start_config.port}")
        await server.serve()
        
    except Exception as e:
        logger.error(f"系统启动失败: {{e}}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
'''
        
        script_file = self.project_root / f"quick_start_{quick_start_config.mode}.py"
        with open(script_file, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        script_file.chmod(0o755)
        return script_file
    
    def _show_access_info(self, quick_start_config: QuickStartConfig):
        """显示访问信息"""
        mode_config = self.mode_configs[quick_start_config.mode]
        
        print("\n" + "="*80)
        print("🎉 AI模型评估系统启动成功！")
        print("="*80)
        
        print(f"\n📋 系统信息:")
        print(f"• 运行模式: {quick_start_config.mode} ({mode_config['description']})")
        print(f"• 优化级别: {mode_config['optimization_level']}")
        print(f"• 最大并发: {mode_config['max_concurrent']}")
        print(f"• 监控状态: {'启用' if quick_start_config.enable_monitoring else '禁用'}")
        
        print(f"\n🌐 访问地址:")
        print(f"• 主页: http://{quick_start_config.host}:{quick_start_config.port}")
        print(f"• 健康检查: http://{quick_start_config.host}:{quick_start_config.port}/health")
        print(f"• 性能指标: http://{quick_start_config.host}:{quick_start_config.port}/metrics")
        
        if mode_config['enable_debug']:
            print(f"• API文档: http://{quick_start_config.host}:{quick_start_config.port}/docs")
        
        print(f"\n⚙️ 配置文件: {self.config_file}")
        
        print(f"\n🛑 停止系统: 按 Ctrl+C")
        print("="*80)
    
    def _open_browser(self, quick_start_config: QuickStartConfig):
        """自动打开浏览器"""
        try:
            import webbrowser
            url = f"http://{quick_start_config.host}:{quick_start_config.port}"
            webbrowser.open(url)
            logger.info(f"🌐 已打开浏览器: {url}")
        except Exception as e:
            logger.warning(f"⚠️ 无法自动打开浏览器: {e}")
    
    def quick_start(self, quick_start_config: QuickStartConfig) -> bool:
        """执行快速启动"""
        self.print_banner()
        
        mode_config = self.mode_configs[quick_start_config.mode]
        logger.info(f"🚀 开始快速启动 - {quick_start_config.mode}模式")
        logger.info(f"📝 {mode_config['description']}")
        
        steps = [
            ("检查环境", self.check_environment),
            ("设置虚拟环境", self.setup_virtual_environment),
            ("安装依赖", lambda: self.install_dependencies(quick_start_config)),
            ("创建配置", lambda: self.create_config_files(quick_start_config)),
            ("运行测试", lambda: self.run_tests(quick_start_config)),
            ("启动系统", lambda: self.start_system(quick_start_config))
        ]
        
        for step_name, step_func in steps:
            logger.info(f"📋 步骤: {step_name}")
            if not step_func():
                logger.error(f"❌ 步骤失败: {step_name}")
                return False
            logger.info(f"✅ 步骤完成: {step_name}")
        
        logger.info("🎉 快速启动完成！")
        return True
    
    def cleanup(self):
        """清理资源"""
        logger.info("🧹 清理资源...")
        
        # 终止启动的进程
        for pid in self.process_ids:
            try:
                import psutil
                process = psutil.Process(pid)
                process.terminate()
                logger.info(f"🛑 终止进程: {pid}")
            except Exception as e:
                logger.warning(f"⚠️ 无法终止进程 {pid}: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="AI模型评估系统快速启动工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python quick_start.py demo              # 演示模式
  python quick_start.py dev --port 8080   # 开发模式，指定端口
  python quick_start.py prod --no-browser # 生产模式，不自动打开浏览器
        """
    )
    
    parser.add_argument(
        "mode",
        choices=['demo', 'dev', 'prod'],
        help="启动模式: demo(演示), dev(开发), prod(生产)"
    )
    parser.add_argument(
        "--port", "-p",
        type=int, 
        default=8000,
        help="服务端口 (默认: 8000)"
    )
    parser.add_argument(
        "--host",
        default="localhost",
        help="服务主机 (默认: localhost)"
    )
    parser.add_argument(
        "--skip-tests",
        action="store_true",
        help="跳过测试"
    )
    parser.add_argument(
        "--skip-deps",
        action="store_true",
        help="跳过依赖安装"
    )
    parser.add_argument(
        "--no-browser",
        action="store_true",
        help="不自动打开浏览器"
    )
    parser.add_argument(
        "--no-monitoring",
        action="store_true",
        help="禁用监控"
    )
    parser.add_argument(
        "--log-level",
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help="日志级别 (默认: INFO)"
    )
    parser.add_argument(
        "--project-root",
        default=".",
        help="项目根目录 (默认: 当前目录)"
    )
    
    args = parser.parse_args()
    
    # 配置日志
    logger.remove()
    logger.add(
        sys.stderr,
        level=args.log_level,
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>"
    )
    
    # 创建快速启动配置
    quick_start_config = QuickStartConfig(
        mode=args.mode,
        skip_tests=args.skip_tests,
        skip_dependencies=args.skip_deps,
        auto_open_browser=not args.no_browser,
        port=args.port,
        host=args.host,
        log_level=args.log_level,
        enable_monitoring=not args.no_monitoring
    )
    
    # 创建快速启动管理器
    manager = QuickStartManager(args.project_root)
    
    try:
        # 执行快速启动
        success = manager.quick_start(quick_start_config)
        
        if success:
            logger.info("✅ 快速启动成功")
            
            # 保持运行
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                logger.info("\n👋 用户中断，正在关闭系统...")
        else:
            logger.error("❌ 快速启动失败")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("\n👋 用户中断")
    except Exception as e:
        logger.error(f"❌ 快速启动过程中发生异常: {e}")
        sys.exit(1)
    finally:
        manager.cleanup()


if __name__ == "__main__":
    main()