# AI 模型评估系统

一个全面的系统，用于评估和比较来自不同提供商的AI模型，支持批处理、详细分析和全面报告。

## 🚀 功能特性

- **多提供商支持**: 兼容来自各种提供商的OpenAI风格API
- **灵活配置**: 基于YAML的配置，支持环境变量
- **模板系统**: 强大的提示模板引擎，支持变量替换
- **批处理**: 高效的多模型并发评估
- **全面分析**: 统计分析、模型比较和性能指标
- **丰富报告**: 生成文本、JSON、CSV和HTML格式的报告
- **任务管理**: 完整的任务历史记录，支持日志记录和导出功能
- **命令行界面**: 用户友好的命令行界面，输出美观
- **可扩展架构**: 模块化设计，易于扩展和定制

## 📦 安装

### 从源码安装

```bash
git clone <repository-url>
cd ai-model-evaluation
pip install -e .
```

### 依赖项

系统需要Python 3.8+和以下关键依赖项：
- `aiohttp` 用于异步HTTP请求
- `pandas` 用于数据处理
- `click` 用于命令行界面
- `rich` 用于美观的终端输出
- `pyyaml` 用于配置管理

## 🏃 快速开始

### 1. 配置设置

创建一个包含提供商和模型的`config.yaml`文件：

```yaml
providers:
  - id: openai
    name: OpenAI
    base_url: https://api.openai.com/v1/
    api_key: ${OPENAI_API_KEY}
    is_active: true

  - id: anthropic
    name: Anthropic
    base_url: https://api.anthropic.com/v1/
    api_key: ${ANTHROPIC_API_KEY}
    is_active: true

models:
  - id: gpt4
    provider_id: openai
    model_name: gpt-4
    display_name: GPT-4
    temperature: 0.7
    thinking_enabled: false

  - id: claude3
    provider_id: anthropic
    model_name: claude-3-sonnet-20240229
    display_name: Claude 3 Sonnet
    temperature: 0.7
    thinking_enabled: false

prompt_templates:
  - id: qa_template
    name: Question Answering
    template: |
      Question: {original_prompt}
      
      Context A: {variable_a}
      Context B: {variable_b}
      
      Please provide a clear and accurate answer.
    variables: [original_prompt, variable_a, variable_b]
    description: Template for question answering tasks
```

### 2. 准备评估数据

创建一个包含评估数据的CSV文件：

```csv
original_prompt,variable_a,variable_b,expected_result
法国的首都是什么？,法国,欧洲国家,巴黎
2 + 2 等于多少？,数学,基础算术,4
将hello翻译成西班牙语,英语单词,西班牙语,hola
```

### 3. 运行评估

系统提供两种评估方式，都使用相同的统一评估引擎，确保结果一致性：

#### 方式一：命令行界面 (CLI)

```bash
# 基础评估
ai-eval evaluate --input data.csv --template qa_template --models gpt4,claude3 --output results/

# 使用自定义设置
ai-eval evaluate \
  --input data.csv \
  --template qa_template \
  --models gpt4,claude3 \
  --output results/ \
  --concurrent 10 \
  --name "我的评估"
```

#### 方式二：脚本方式 (auto_evaluate.py)

```bash
# 标准版本评估
python auto_evaluate.py

# 高性能优化版本评估
python auto_evaluate.py --optimized

# 自定义配置文件
python auto_evaluate.py --config custom_config.yaml --optimized
```

**重要说明**: 两种方式现在使用相同的统一评估引擎，确保：
- 相同的结果格式和准确性
- 一致的性能优化效果
- 统一的配置管理
- 相同的错误处理机制

### 4. 分析结果

```bash
# 生成全面分析
ai-eval report analyze results/evaluation_results.csv

# 生成不同格式的报告
ai-eval report generate results/evaluation_results.csv --format json
ai-eval report generate results/evaluation_results.csv --format html
```

## 📖 详细使用说明

### 配置管理

```bash
# 查看当前配置
ai-eval config show

# 验证配置
ai-eval config validate

# 添加新提供商
ai-eval config add-provider \
  --name "自定义提供商" \
  --base-url "https://api.custom.com/v1/" \
  --api-key "your-api-key"

# 添加新模型
ai-eval config add-model \
  --id custom_model \
  --provider custom_provider \
  --model-name "custom-model-v1" \
  --display-name "自定义模型 V1" \
  --temperature 0.8
```

### 性能优化配置

系统现在支持统一的性能配置，包含在主配置文件 `config.yaml` 中：

```yaml
# 性能优化配置
performance:
  # 全局性能设置
  global_performance:
    max_concurrent_requests: 800  # 全局最大并发数
    connection_pool_size: 300     # 连接池大小
    request_timeout: 60           # 请求超时时间

  # 提供商特定配置
  provider_performance:
    openai:
      max_concurrent: 120         # 提供商最大并发数
      retry_config:
        max_retries: 3
        base_delay: 0.1

  # 批处理优化
  batch_processing:
    batch_size:
      default: 150              # 默认批处理大小
      large_dataset: 500        # 大数据集批处理大小
    enable_streaming: true      # 启用流式处理
    memory_optimization:
      enable: true              # 启用内存优化
```

**性能优化特性**:
- 🚀 **高并发处理**: 支持数百个并发请求
- 📊 **智能批处理**: 根据数据集大小自动调整批处理策略
- 💾 **内存优化**: 流式处理和垃圾回收优化
- 🔄 **连接复用**: HTTP连接池和Keep-Alive支持
- ⚡ **自适应重试**: 智能错误处理和重试机制

### 数据验证

项目提供了一个配置验证脚本，可以帮助您检查 `config.yaml` 文件是否正确配置，并测试与AI模型的连接性。

```bash
poetry run python scripts/validate_config.py
```



### 任务管理

```bash
# 列出所有任务
ai-eval task list

# 显示任务详情
ai-eval task show <task-id>

# 取消运行中的任务
ai-eval task cancel <task-id>

# 查看任务统计
ai-eval task stats
```

### 历史管理

```bash
# 查看任务历史
ai-eval history list

# 搜索历史
ai-eval history search "evaluation"

# 显示详细任务信息
ai-eval history show <task-id> --show-results --show-logs

# 导出任务数据
ai-eval history export <task-id> --output task_data.json

# 清理旧任务
ai-eval history cleanup --days 30
```

### 高级分析

```bash
ai-eval report compare results.csv --models gpt4,claude3 --metric success_rate

# 使用自定义阈值分析
ai-eval report analyze results.csv --threshold 0.3

# 生成多种报告格式
for format in text json csv html; do
  ai-eval report generate results.csv --format $format --output report.$format
done
```

## 📊 数据格式

### 输入数据格式

您的CSV文件应包含以下列：

- `original_prompt`: 主要提示/问题
- `variable_a`: 第一个上下文变量
- `variable_b`: 第二个上下文变量  
- `expected_result` (可选): 用于比较的预期答案

示例:
```csv
original_prompt,variable_a,variable_b,expected_result
"天气怎么样？","晴天","25°C","天气晴朗，温度25摄氏度"
"如何煮意大利面？","意大利面条","有嚼劲","烧开水，加入面条，煮8-10分钟"
```

### 输出数据格式

结果保存时会为每个模型添加额外的列：

- `{model_id}_result`: 模型的响应
- `{model_id}_execution_time`: 请求耗时
- `{model_id}_error`: 请求失败时的错误消息

## 🔧 配置参考

### 提供商配置

```yaml
providers:
  - id: unique_provider_id          # 必需: 唯一标识符
    name: Display Name              # 必需: 人类可读的名称
    base_url: https://api.url/v1/   # 必需: API端点
    api_key: ${ENV_VAR}            # 必需: API密钥（支持环境变量）
    is_active: true                # 可选: 启用/禁用提供商
```

### 模型配置

```yaml
models:
  - id: unique_model_id             # 必需: 唯一标识符
    provider_id: provider_id        # 必需: 提供商引用
    model_name: api_model_name      # 必需: API调用的模型名称
    display_name: Human Name        # 必需: 显示名称
    temperature: 0.7                # 可选: 采样温度（0.0-2.0）
    thinking_enabled: false         # 可选: 启用思考模式
    max_tokens: 2048               # 可选: 最大响应令牌数
```

### 模板配置

```yaml
prompt_templates:
  - id: template_id                 # 必需: 唯一标识符
    name: Template Name             # 必需: 人类可读的名称
    template: |                     # 必需: 带有{变量}的模板
      您的模板内容，包含{variable_name}
    variables: [var1, var2]         # 必需: 使用的变量列表
    description: Template purpose   # 必需: 描述
```

## 🎯 最佳实践

### 1. 配置管理

- 使用环境变量存储API密钥
- 为不同环境保持独立的配置

## 📋 当前配置的模型

系统已预配置以下AI模型提供商和模型：

### 已配置的提供商

| 提供商 | 模型 | 状态 |
|--------|------|------|
| 通义千问 | qwen-plus | ✅ 已配置 |
| 百度AI | ERNIE-4.0, DeepSeek V3 | ✅ 已配置 |
| 火山AI | doubao-1.6-seed | ✅ 已配置 |
| 混元AI | hunyuan-pro | ✅ 已配置 |
| 智谱AI | glm-4 | 🔧 需要配置API密钥 |
| OpenAI | - | 🔧 需要配置API密钥 |

### 环境变量配置

1. 复制环境变量示例文件：
```bash
cp .env.example .env
```

2. 编辑 `.env` 文件，填入您的API密钥：
```bash
# 可选：如果需要使用智谱AI
ZHIPU_API_KEY=your-zhipu-api-key

# 可选：如果需要使用OpenAI
OPENAI_API_KEY=your-openai-api-key
```

### 快速开始测试

使用已配置的模型进行测试：
```bash
# 测试通义千问
ai-eval run --models qwen-plus --template default --input your_data.csv

# 测试百度模型
ai-eval run --models ernie-4,deepseek-v3 --template comparison --input your_data.csv

# 测试火山模型
ai-eval run --models doubao-1.6-seed --template simple --input your_data.csv

# 测试混元模型
ai-eval run --models hunyuan-pro --template default --input your_data.csv
```
- 运行评估前验证配置
- 为模型和模板使用描述性名称

### 2. 数据准备

- 确保数据集间列名一致
- 尽可能包含预期结果用于分析
- 在模板中使用有意义的变量名
- 评估前验证数据格式

### 3. 评估策略

- 从小数据集开始测试配置
- 使用适当的并发级别（通常5-20）
- 监控API速率限制和成本
- 为长时间评估保存中间结果

### 4. 分析和报告

- 为不同受众生成多种报告格式
- 使用适当的阈值检测问题案例
- 在与您用例相关的指标上比较模型
- 归档重要的评估结果

## 🔍 故障排除

### 常见问题

**配置错误**
```bash
# 检查配置语法
ai-eval config validate

# 测试提供商连接
ai-eval test-connectivity --provider openai
```

**数据格式问题**
```bash
# 验证数据格式
ai-eval validate-data data.csv

# 检查文件信息
ai-eval file-info data.csv
```

**模板问题**
```bash
# 预览模板渲染
ai-eval preview-template --template my_template --input data.csv

# 验证模板兼容性
ai-eval validate-data data.csv --template my_template
```

**性能问题**
- 如果遇到速率限制，减少并发数
- 对大数据集使用较小的批次大小
- 检查系统资源（内存、磁盘空间）
- 监控API响应时间

### 错误消息

- **"未找到模板变量"**: 检查所有模板变量是否存在于您的数据中
- **"提供商无响应"**: 验证API密钥和网络连接
- **"不支持的文件格式"**: 确保CSV/Excel文件格式正确
- **"未找到模型"**: 检查模型ID是否存在于配置中

## 🧪 测试

运行测试套件：

```bash
# 运行所有测试
pytest

# 运行特定测试类别
pytest tests/test_integration.py -v
pytest tests/test_performance.py -v -m performance

# 运行并生成覆盖率报告
pytest --cov=ai_model_evaluation --cov-report=html
```

## 🤝 贡献

1. Fork仓库
2. 创建功能分支
3. 为新功能添加测试
4. 确保所有测试通过
5. 提交拉取请求

## 📄 许可证

本项目采用MIT许可证 - 详情请参阅LICENSE文件。

## 🆘 支持

如有问题和疑问：
1. 查看上述故障排除部分
2. 在仓库中搜索现有问题
3. 创建包含详细信息的新问题
4. 包含配置（不含API密钥）和错误消息

## 🗺️ 路线图

- [ ] 支持更多AI提供商
- [ ] 高级模板功能（条件语句、循环）
- [ ] 评估管理的Web界面
- [ ] 与流行ML框架的集成
- [ ] 高级统计分析功能
- [ ] 实时评估监控