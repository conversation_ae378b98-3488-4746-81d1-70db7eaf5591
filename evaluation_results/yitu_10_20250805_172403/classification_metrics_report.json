{"expected": {"accuracy": 1.0, "accuracy_warning": "准确率可能受类别不平衡影响，建议重点关注F1、Precision、Recall指标", "precision": 1.0, "recall": 1.0, "f1_score": 1.0, "total_samples": 10, "valid_predictions": 10, "positive_classes": ["撩妹", "看车型", "问优惠", "问地址"], "none_class": "none", "total_positive_samples": 6, "overall_metrics": {"tp": 6, "fp": 0, "fn": 0, "tn": 4}, "macro_avg": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0}, "weighted_avg": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0}, "per_class_metrics": {"none": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 4, "tp": 4, "fp": 0, "fn": 0, "is_positive_class": false}, "撩妹": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 2, "tp": 2, "fp": 0, "fn": 0, "is_positive_class": true}, "看车型": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 2, "tp": 2, "fp": 0, "fn": 0, "is_positive_class": true}, "问优惠": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 1, "tp": 1, "fp": 0, "fn": 0, "is_positive_class": true}, "问地址": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 1, "tp": 1, "fp": 0, "fn": 0, "is_positive_class": true}}, "confusion_matrix": {"none": {"none": 4, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 0}, "撩妹": {"none": 0, "撩妹": 2, "看车型": 0, "问优惠": 0, "问地址": 0}, "看车型": {"none": 0, "撩妹": 0, "看车型": 2, "问优惠": 0, "问地址": 0}, "问优惠": {"none": 0, "撩妹": 0, "看车型": 0, "问优惠": 1, "问地址": 0}, "问地址": {"none": 0, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 1}}, "note": "基于10个样本的正向意图识别分析，其中4个正向类别，10个有效预测", "methodology": "根据sunfa.md文档，将'none'视为负例，其他类别视为正例，重点评估正向意图识别能力", "sample_analysis": [{"sample_index": 0, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 1, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 2, "expected": "看车型", "predicted": "看车型", "is_correct": true, "status": "✅"}, {"sample_index": 3, "expected": "撩妹", "predicted": "撩妹", "is_correct": true, "status": "✅"}, {"sample_index": 4, "expected": "撩妹", "predicted": "撩妹", "is_correct": true, "status": "✅"}, {"sample_index": 5, "expected": "问优惠", "predicted": "问优惠", "is_correct": true, "status": "✅"}, {"sample_index": 6, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 7, "expected": "问地址", "predicted": "问地址", "is_correct": true, "status": "✅"}, {"sample_index": 8, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 9, "expected": "看车型", "predicted": "看车型", "is_correct": true, "status": "✅"}], "valid_sample_comparison": [{"sample_index": 0, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 1, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 2, "expected": "看车型", "predicted": "看车型", "is_correct": true, "status": "✅"}, {"sample_index": 3, "expected": "撩妹", "predicted": "撩妹", "is_correct": true, "status": "✅"}, {"sample_index": 4, "expected": "撩妹", "predicted": "撩妹", "is_correct": true, "status": "✅"}, {"sample_index": 5, "expected": "问优惠", "predicted": "问优惠", "is_correct": true, "status": "✅"}, {"sample_index": 6, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 7, "expected": "问地址", "predicted": "问地址", "is_correct": true, "status": "✅"}, {"sample_index": 8, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 9, "expected": "看车型", "predicted": "看车型", "is_correct": true, "status": "✅"}], "accuracy_calculation": {"correct_predictions": 10, "total_valid_samples": 10, "accuracy": 1.0, "accuracy_percentage": "100.00%"}, "data_stats": {"total_samples": 10, "valid_samples": 10, "filtered_samples": 0}}, "ERNIE 4.5 Turbo": {"accuracy": 0.7, "accuracy_warning": "准确率可能受类别不平衡影响，建议重点关注F1、Precision、Recall指标", "precision": 1.0, "recall": 0.5, "f1_score": 0.6666666666666666, "total_samples": 10, "valid_predictions": 10, "positive_classes": ["撩妹", "看车型", "问优惠", "问地址"], "none_class": "none", "total_positive_samples": 6, "overall_metrics": {"tp": 3, "fp": 0, "fn": 3, "tn": 4}, "macro_avg": {"precision": 0.75, "recall": 0.625, "f1_score": 0.6666666666666666}, "weighted_avg": {"precision": 0.6666666666666666, "recall": 0.5, "f1_score": 0.5555555555555555}, "per_class_metrics": {"none": {"precision": 0.5714285714285714, "recall": 1.0, "f1_score": 0.7272727272727273, "support": 4, "tp": 4, "fp": 3, "fn": 0, "is_positive_class": false}, "撩妹": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "support": 2, "tp": 0, "fp": 0, "fn": 2, "is_positive_class": true}, "看车型": {"precision": 1.0, "recall": 0.5, "f1_score": 0.6666666666666666, "support": 2, "tp": 1, "fp": 0, "fn": 1, "is_positive_class": true}, "问优惠": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 1, "tp": 1, "fp": 0, "fn": 0, "is_positive_class": true}, "问地址": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 1, "tp": 1, "fp": 0, "fn": 0, "is_positive_class": true}}, "confusion_matrix": {"none": {"none": 4, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 0}, "撩妹": {"none": 2, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 0}, "看车型": {"none": 1, "撩妹": 0, "看车型": 1, "问优惠": 0, "问地址": 0}, "问优惠": {"none": 0, "撩妹": 0, "看车型": 0, "问优惠": 1, "问地址": 0}, "问地址": {"none": 0, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 1}}, "note": "基于10个样本的正向意图识别分析，其中4个正向类别，10个有效预测", "methodology": "根据sunfa.md文档，将'none'视为负例，其他类别视为正例，重点评估正向意图识别能力", "sample_analysis": [{"sample_index": 0, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 1, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 2, "expected": "看车型", "predicted": "看车型", "is_correct": true, "status": "✅"}, {"sample_index": 3, "expected": "撩妹", "predicted": "none", "is_correct": false, "status": "❌"}, {"sample_index": 4, "expected": "撩妹", "predicted": "none", "is_correct": false, "status": "❌"}, {"sample_index": 5, "expected": "问优惠", "predicted": "问优惠", "is_correct": true, "status": "✅"}, {"sample_index": 6, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 7, "expected": "问地址", "predicted": "问地址", "is_correct": true, "status": "✅"}, {"sample_index": 8, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 9, "expected": "看车型", "predicted": "none", "is_correct": false, "status": "❌"}], "valid_sample_comparison": [{"sample_index": 0, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 1, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 2, "expected": "看车型", "predicted": "看车型", "is_correct": true, "status": "✅"}, {"sample_index": 3, "expected": "撩妹", "predicted": "none", "is_correct": false, "status": "❌"}, {"sample_index": 4, "expected": "撩妹", "predicted": "none", "is_correct": false, "status": "❌"}, {"sample_index": 5, "expected": "问优惠", "predicted": "问优惠", "is_correct": true, "status": "✅"}, {"sample_index": 6, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 7, "expected": "问地址", "predicted": "问地址", "is_correct": true, "status": "✅"}, {"sample_index": 8, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 9, "expected": "看车型", "predicted": "none", "is_correct": false, "status": "❌"}], "accuracy_calculation": {"correct_predictions": 7, "total_valid_samples": 10, "accuracy": 0.7, "accuracy_percentage": "70.00%"}, "data_stats": {"total_samples": 10, "valid_samples": 10, "filtered_samples": 0}}, "deepseek-v3": {"accuracy": 0.7, "accuracy_warning": "准确率可能受类别不平衡影响，建议重点关注F1、Precision、Recall指标", "precision": 1.0, "recall": 0.5, "f1_score": 0.6666666666666666, "total_samples": 10, "valid_predictions": 10, "positive_classes": ["撩妹", "看车型", "问优惠", "问地址"], "none_class": "none", "total_positive_samples": 6, "overall_metrics": {"tp": 3, "fp": 0, "fn": 3, "tn": 4}, "macro_avg": {"precision": 0.75, "recall": 0.625, "f1_score": 0.6666666666666666}, "weighted_avg": {"precision": 0.6666666666666666, "recall": 0.5, "f1_score": 0.5555555555555555}, "per_class_metrics": {"none": {"precision": 0.5714285714285714, "recall": 1.0, "f1_score": 0.7272727272727273, "support": 4, "tp": 4, "fp": 3, "fn": 0, "is_positive_class": false}, "撩妹": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "support": 2, "tp": 0, "fp": 0, "fn": 2, "is_positive_class": true}, "看车型": {"precision": 1.0, "recall": 0.5, "f1_score": 0.6666666666666666, "support": 2, "tp": 1, "fp": 0, "fn": 1, "is_positive_class": true}, "问优惠": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 1, "tp": 1, "fp": 0, "fn": 0, "is_positive_class": true}, "问地址": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 1, "tp": 1, "fp": 0, "fn": 0, "is_positive_class": true}}, "confusion_matrix": {"none": {"none": 4, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 0}, "撩妹": {"none": 2, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 0}, "看车型": {"none": 1, "撩妹": 0, "看车型": 1, "问优惠": 0, "问地址": 0}, "问优惠": {"none": 0, "撩妹": 0, "看车型": 0, "问优惠": 1, "问地址": 0}, "问地址": {"none": 0, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 1}}, "note": "基于10个样本的正向意图识别分析，其中4个正向类别，10个有效预测", "methodology": "根据sunfa.md文档，将'none'视为负例，其他类别视为正例，重点评估正向意图识别能力", "sample_analysis": [{"sample_index": 0, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 1, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 2, "expected": "看车型", "predicted": "看车型", "is_correct": true, "status": "✅"}, {"sample_index": 3, "expected": "撩妹", "predicted": "none", "is_correct": false, "status": "❌"}, {"sample_index": 4, "expected": "撩妹", "predicted": "none", "is_correct": false, "status": "❌"}, {"sample_index": 5, "expected": "问优惠", "predicted": "问优惠", "is_correct": true, "status": "✅"}, {"sample_index": 6, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 7, "expected": "问地址", "predicted": "问地址", "is_correct": true, "status": "✅"}, {"sample_index": 8, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 9, "expected": "看车型", "predicted": "none", "is_correct": false, "status": "❌"}], "valid_sample_comparison": [{"sample_index": 0, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 1, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 2, "expected": "看车型", "predicted": "看车型", "is_correct": true, "status": "✅"}, {"sample_index": 3, "expected": "撩妹", "predicted": "none", "is_correct": false, "status": "❌"}, {"sample_index": 4, "expected": "撩妹", "predicted": "none", "is_correct": false, "status": "❌"}, {"sample_index": 5, "expected": "问优惠", "predicted": "问优惠", "is_correct": true, "status": "✅"}, {"sample_index": 6, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 7, "expected": "问地址", "predicted": "问地址", "is_correct": true, "status": "✅"}, {"sample_index": 8, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 9, "expected": "看车型", "predicted": "none", "is_correct": false, "status": "❌"}], "accuracy_calculation": {"correct_predictions": 7, "total_valid_samples": 10, "accuracy": 0.7, "accuracy_percentage": "70.00%"}, "data_stats": {"total_samples": 10, "valid_samples": 10, "filtered_samples": 0}}, "doubao-1.6-seed": {"accuracy": 0.7, "accuracy_warning": "准确率可能受类别不平衡影响，建议重点关注F1、Precision、Recall指标", "precision": 1.0, "recall": 0.5, "f1_score": 0.6666666666666666, "total_samples": 10, "valid_predictions": 10, "positive_classes": ["撩妹", "看车型", "问优惠", "问地址"], "none_class": "none", "total_positive_samples": 6, "overall_metrics": {"tp": 3, "fp": 0, "fn": 3, "tn": 4}, "macro_avg": {"precision": 0.75, "recall": 0.625, "f1_score": 0.6666666666666666}, "weighted_avg": {"precision": 0.6666666666666666, "recall": 0.5, "f1_score": 0.5555555555555555}, "per_class_metrics": {"none": {"precision": 0.5714285714285714, "recall": 1.0, "f1_score": 0.7272727272727273, "support": 4, "tp": 4, "fp": 3, "fn": 0, "is_positive_class": false}, "撩妹": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "support": 2, "tp": 0, "fp": 0, "fn": 2, "is_positive_class": true}, "看车型": {"precision": 1.0, "recall": 0.5, "f1_score": 0.6666666666666666, "support": 2, "tp": 1, "fp": 0, "fn": 1, "is_positive_class": true}, "问优惠": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 1, "tp": 1, "fp": 0, "fn": 0, "is_positive_class": true}, "问地址": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 1, "tp": 1, "fp": 0, "fn": 0, "is_positive_class": true}}, "confusion_matrix": {"none": {"none": 4, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 0}, "撩妹": {"none": 2, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 0}, "看车型": {"none": 1, "撩妹": 0, "看车型": 1, "问优惠": 0, "问地址": 0}, "问优惠": {"none": 0, "撩妹": 0, "看车型": 0, "问优惠": 1, "问地址": 0}, "问地址": {"none": 0, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 1}}, "note": "基于10个样本的正向意图识别分析，其中4个正向类别，10个有效预测", "methodology": "根据sunfa.md文档，将'none'视为负例，其他类别视为正例，重点评估正向意图识别能力", "sample_analysis": [{"sample_index": 0, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 1, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 2, "expected": "看车型", "predicted": "看车型", "is_correct": true, "status": "✅"}, {"sample_index": 3, "expected": "撩妹", "predicted": "none", "is_correct": false, "status": "❌"}, {"sample_index": 4, "expected": "撩妹", "predicted": "none", "is_correct": false, "status": "❌"}, {"sample_index": 5, "expected": "问优惠", "predicted": "问优惠", "is_correct": true, "status": "✅"}, {"sample_index": 6, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 7, "expected": "问地址", "predicted": "问地址", "is_correct": true, "status": "✅"}, {"sample_index": 8, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 9, "expected": "看车型", "predicted": "none", "is_correct": false, "status": "❌"}], "valid_sample_comparison": [{"sample_index": 0, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 1, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 2, "expected": "看车型", "predicted": "看车型", "is_correct": true, "status": "✅"}, {"sample_index": 3, "expected": "撩妹", "predicted": "none", "is_correct": false, "status": "❌"}, {"sample_index": 4, "expected": "撩妹", "predicted": "none", "is_correct": false, "status": "❌"}, {"sample_index": 5, "expected": "问优惠", "predicted": "问优惠", "is_correct": true, "status": "✅"}, {"sample_index": 6, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 7, "expected": "问地址", "predicted": "问地址", "is_correct": true, "status": "✅"}, {"sample_index": 8, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 9, "expected": "看车型", "predicted": "none", "is_correct": false, "status": "❌"}], "accuracy_calculation": {"correct_predictions": 7, "total_valid_samples": 10, "accuracy": 0.7, "accuracy_percentage": "70.00%"}, "data_stats": {"total_samples": 10, "valid_samples": 10, "filtered_samples": 0}}, "doubao1.5 pro": {"accuracy": 0.7, "accuracy_warning": "准确率可能受类别不平衡影响，建议重点关注F1、Precision、Recall指标", "precision": 1.0, "recall": 0.5, "f1_score": 0.6666666666666666, "total_samples": 10, "valid_predictions": 10, "positive_classes": ["撩妹", "看车型", "问优惠", "问地址"], "none_class": "none", "total_positive_samples": 6, "overall_metrics": {"tp": 3, "fp": 0, "fn": 3, "tn": 4}, "macro_avg": {"precision": 0.75, "recall": 0.625, "f1_score": 0.6666666666666666}, "weighted_avg": {"precision": 0.6666666666666666, "recall": 0.5, "f1_score": 0.5555555555555555}, "per_class_metrics": {"none": {"precision": 0.5714285714285714, "recall": 1.0, "f1_score": 0.7272727272727273, "support": 4, "tp": 4, "fp": 3, "fn": 0, "is_positive_class": false}, "撩妹": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "support": 2, "tp": 0, "fp": 0, "fn": 2, "is_positive_class": true}, "看车型": {"precision": 1.0, "recall": 0.5, "f1_score": 0.6666666666666666, "support": 2, "tp": 1, "fp": 0, "fn": 1, "is_positive_class": true}, "问优惠": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 1, "tp": 1, "fp": 0, "fn": 0, "is_positive_class": true}, "问地址": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 1, "tp": 1, "fp": 0, "fn": 0, "is_positive_class": true}}, "confusion_matrix": {"none": {"none": 4, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 0}, "撩妹": {"none": 2, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 0}, "看车型": {"none": 1, "撩妹": 0, "看车型": 1, "问优惠": 0, "问地址": 0}, "问优惠": {"none": 0, "撩妹": 0, "看车型": 0, "问优惠": 1, "问地址": 0}, "问地址": {"none": 0, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 1}}, "note": "基于10个样本的正向意图识别分析，其中4个正向类别，10个有效预测", "methodology": "根据sunfa.md文档，将'none'视为负例，其他类别视为正例，重点评估正向意图识别能力", "sample_analysis": [{"sample_index": 0, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 1, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 2, "expected": "看车型", "predicted": "看车型", "is_correct": true, "status": "✅"}, {"sample_index": 3, "expected": "撩妹", "predicted": "none", "is_correct": false, "status": "❌"}, {"sample_index": 4, "expected": "撩妹", "predicted": "none", "is_correct": false, "status": "❌"}, {"sample_index": 5, "expected": "问优惠", "predicted": "问优惠", "is_correct": true, "status": "✅"}, {"sample_index": 6, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 7, "expected": "问地址", "predicted": "问地址", "is_correct": true, "status": "✅"}, {"sample_index": 8, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 9, "expected": "看车型", "predicted": "none", "is_correct": false, "status": "❌"}], "valid_sample_comparison": [{"sample_index": 0, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 1, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 2, "expected": "看车型", "predicted": "看车型", "is_correct": true, "status": "✅"}, {"sample_index": 3, "expected": "撩妹", "predicted": "none", "is_correct": false, "status": "❌"}, {"sample_index": 4, "expected": "撩妹", "predicted": "none", "is_correct": false, "status": "❌"}, {"sample_index": 5, "expected": "问优惠", "predicted": "问优惠", "is_correct": true, "status": "✅"}, {"sample_index": 6, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 7, "expected": "问地址", "predicted": "问地址", "is_correct": true, "status": "✅"}, {"sample_index": 8, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 9, "expected": "看车型", "predicted": "none", "is_correct": false, "status": "❌"}], "accuracy_calculation": {"correct_predictions": 7, "total_valid_samples": 10, "accuracy": 0.7, "accuracy_percentage": "70.00%"}, "data_stats": {"total_samples": 10, "valid_samples": 10, "filtered_samples": 0}}, "qwen-plus": {"accuracy": 0.8, "accuracy_warning": "准确率可能受类别不平衡影响，建议重点关注F1、Precision、Recall指标", "precision": 1.0, "recall": 0.6666666666666666, "f1_score": 0.8, "total_samples": 10, "valid_predictions": 10, "positive_classes": ["撩妹", "看车型", "问优惠", "问地址"], "none_class": "none", "total_positive_samples": 6, "overall_metrics": {"tp": 4, "fp": 0, "fn": 2, "tn": 4}, "macro_avg": {"precision": 0.75, "recall": 0.75, "f1_score": 0.75}, "weighted_avg": {"precision": 0.6666666666666666, "recall": 0.6666666666666666, "f1_score": 0.6666666666666666}, "per_class_metrics": {"none": {"precision": 0.6666666666666666, "recall": 1.0, "f1_score": 0.8, "support": 4, "tp": 4, "fp": 2, "fn": 0, "is_positive_class": false}, "撩妹": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "support": 2, "tp": 0, "fp": 0, "fn": 2, "is_positive_class": true}, "看车型": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 2, "tp": 2, "fp": 0, "fn": 0, "is_positive_class": true}, "问优惠": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 1, "tp": 1, "fp": 0, "fn": 0, "is_positive_class": true}, "问地址": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 1, "tp": 1, "fp": 0, "fn": 0, "is_positive_class": true}}, "confusion_matrix": {"none": {"none": 4, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 0}, "撩妹": {"none": 2, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 0}, "看车型": {"none": 0, "撩妹": 0, "看车型": 2, "问优惠": 0, "问地址": 0}, "问优惠": {"none": 0, "撩妹": 0, "看车型": 0, "问优惠": 1, "问地址": 0}, "问地址": {"none": 0, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 1}}, "note": "基于10个样本的正向意图识别分析，其中4个正向类别，10个有效预测", "methodology": "根据sunfa.md文档，将'none'视为负例，其他类别视为正例，重点评估正向意图识别能力", "sample_analysis": [{"sample_index": 0, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 1, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 2, "expected": "看车型", "predicted": "看车型", "is_correct": true, "status": "✅"}, {"sample_index": 3, "expected": "撩妹", "predicted": "none", "is_correct": false, "status": "❌"}, {"sample_index": 4, "expected": "撩妹", "predicted": "none", "is_correct": false, "status": "❌"}, {"sample_index": 5, "expected": "问优惠", "predicted": "问优惠", "is_correct": true, "status": "✅"}, {"sample_index": 6, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 7, "expected": "问地址", "predicted": "问地址", "is_correct": true, "status": "✅"}, {"sample_index": 8, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 9, "expected": "看车型", "predicted": "看车型", "is_correct": true, "status": "✅"}], "valid_sample_comparison": [{"sample_index": 0, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 1, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 2, "expected": "看车型", "predicted": "看车型", "is_correct": true, "status": "✅"}, {"sample_index": 3, "expected": "撩妹", "predicted": "none", "is_correct": false, "status": "❌"}, {"sample_index": 4, "expected": "撩妹", "predicted": "none", "is_correct": false, "status": "❌"}, {"sample_index": 5, "expected": "问优惠", "predicted": "问优惠", "is_correct": true, "status": "✅"}, {"sample_index": 6, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 7, "expected": "问地址", "predicted": "问地址", "is_correct": true, "status": "✅"}, {"sample_index": 8, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 9, "expected": "看车型", "predicted": "看车型", "is_correct": true, "status": "✅"}], "accuracy_calculation": {"correct_predictions": 8, "total_valid_samples": 10, "accuracy": 0.8, "accuracy_percentage": "80.00%"}, "data_stats": {"total_samples": 10, "valid_samples": 10, "filtered_samples": 0}}, "通义千问3-235B-A22B": {"accuracy": 0.8, "accuracy_warning": "准确率可能受类别不平衡影响，建议重点关注F1、Precision、Recall指标", "precision": 1.0, "recall": 0.6666666666666666, "f1_score": 0.8, "total_samples": 10, "valid_predictions": 10, "positive_classes": ["撩妹", "看车型", "问优惠", "问地址"], "none_class": "none", "total_positive_samples": 6, "overall_metrics": {"tp": 4, "fp": 0, "fn": 2, "tn": 4}, "macro_avg": {"precision": 0.75, "recall": 0.75, "f1_score": 0.75}, "weighted_avg": {"precision": 0.6666666666666666, "recall": 0.6666666666666666, "f1_score": 0.6666666666666666}, "per_class_metrics": {"none": {"precision": 0.6666666666666666, "recall": 1.0, "f1_score": 0.8, "support": 4, "tp": 4, "fp": 2, "fn": 0, "is_positive_class": false}, "撩妹": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "support": 2, "tp": 0, "fp": 0, "fn": 2, "is_positive_class": true}, "看车型": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 2, "tp": 2, "fp": 0, "fn": 0, "is_positive_class": true}, "问优惠": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 1, "tp": 1, "fp": 0, "fn": 0, "is_positive_class": true}, "问地址": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 1, "tp": 1, "fp": 0, "fn": 0, "is_positive_class": true}}, "confusion_matrix": {"none": {"none": 4, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 0}, "撩妹": {"none": 2, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 0}, "看车型": {"none": 0, "撩妹": 0, "看车型": 2, "问优惠": 0, "问地址": 0}, "问优惠": {"none": 0, "撩妹": 0, "看车型": 0, "问优惠": 1, "问地址": 0}, "问地址": {"none": 0, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 1}}, "note": "基于10个样本的正向意图识别分析，其中4个正向类别，10个有效预测", "methodology": "根据sunfa.md文档，将'none'视为负例，其他类别视为正例，重点评估正向意图识别能力", "sample_analysis": [{"sample_index": 0, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 1, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 2, "expected": "看车型", "predicted": "看车型", "is_correct": true, "status": "✅"}, {"sample_index": 3, "expected": "撩妹", "predicted": "none", "is_correct": false, "status": "❌"}, {"sample_index": 4, "expected": "撩妹", "predicted": "none", "is_correct": false, "status": "❌"}, {"sample_index": 5, "expected": "问优惠", "predicted": "问优惠", "is_correct": true, "status": "✅"}, {"sample_index": 6, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 7, "expected": "问地址", "predicted": "问地址", "is_correct": true, "status": "✅"}, {"sample_index": 8, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 9, "expected": "看车型", "predicted": "看车型", "is_correct": true, "status": "✅"}], "valid_sample_comparison": [{"sample_index": 0, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 1, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 2, "expected": "看车型", "predicted": "看车型", "is_correct": true, "status": "✅"}, {"sample_index": 3, "expected": "撩妹", "predicted": "none", "is_correct": false, "status": "❌"}, {"sample_index": 4, "expected": "撩妹", "predicted": "none", "is_correct": false, "status": "❌"}, {"sample_index": 5, "expected": "问优惠", "predicted": "问优惠", "is_correct": true, "status": "✅"}, {"sample_index": 6, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 7, "expected": "问地址", "predicted": "问地址", "is_correct": true, "status": "✅"}, {"sample_index": 8, "expected": "none", "predicted": "none", "is_correct": true, "status": "✅"}, {"sample_index": 9, "expected": "看车型", "predicted": "看车型", "is_correct": true, "status": "✅"}], "accuracy_calculation": {"correct_predictions": 8, "total_valid_samples": 10, "accuracy": 0.8, "accuracy_percentage": "80.00%"}, "data_stats": {"total_samples": 10, "valid_samples": 10, "filtered_samples": 0}}}