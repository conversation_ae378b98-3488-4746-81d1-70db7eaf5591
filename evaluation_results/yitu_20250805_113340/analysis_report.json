{"file_info": {"result_file": "/Users/<USER>/kiro/对比/ai-model-evaluation/evaluation_results/yitu_20250805_113340/yitu_20250805_113340_results.csv", "total_rows": 345, "columns": ["history", "live_comment", "expected_result", "ERNIE 4.5 Turbo_result", "deepseek-v3_result", "doubao-1.6-seed_result", "doubao1.5 pro_result", "qwen-plus_result", "通义千问3-235B-A22B_result", "通义千问3-235B-A22B_error"]}, "model_performance": {"ERNIE 4.5 Turbo": {"total_requests": 345, "successful_requests": 345, "success_rate": 1.0, "average_execution_time": 0.0, "result_distribution": {"none": 147, "问价格": 39, "看车型": 37, "问地址": 21, "撩妹": 18, "问配置": 17, "要买车": 16, "问优惠": 15, "问置换": 9, "问政策": 9, "问试乘试驾": 6, "问颜色": 6, "问库存车": 2, "金融政策": 2, "问联系方式": 1}, "classification_metrics": {"accuracy": 0.6405797101449275, "macro_avg": {"precision": 0.6212537461276958, "recall": 0.562385566080505, "f1_score": 0.5985780548477886}, "weighted_avg": {"precision": 0.6878655939160141, "recall": 0.6405797101449275, "f1_score": 0.6345936967455108}, "per_class_metrics": {"none": {"precision": 0.5782312925170068, "recall": 0.794392523364486, "f1_score": 0.6692913385826772, "support": 107, "tp": 85, "fp": 62, "fn": 22}, "想卖车": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "support": 4, "tp": 0, "fp": 0, "fn": 4}, "撩妹": {"precision": 0.3333333333333333, "recall": 0.6, "f1_score": 0.42857142857142855, "support": 10, "tp": 6, "fp": 12, "fn": 4}, "看车型": {"precision": 0.7567567567567568, "recall": 0.5490196078431373, "f1_score": 0.6363636363636364, "support": 51, "tp": 28, "fp": 9, "fn": 23}, "要买车": {"precision": 0.5, "recall": 0.7272727272727273, "f1_score": 0.5925925925925926, "support": 11, "tp": 8, "fp": 8, "fn": 3}, "金融政策": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "support": 2, "tp": 0, "fp": 2, "fn": 2}, "问价格": {"precision": 0.8205128205128205, "recall": 0.7111111111111111, "f1_score": 0.7619047619047619, "support": 45, "tp": 32, "fp": 7, "fn": 13}, "问优惠": {"precision": 0.7333333333333333, "recall": 0.6875, "f1_score": 0.7096774193548386, "support": 16, "tp": 11, "fp": 4, "fn": 5}, "问地址": {"precision": 0.7142857142857143, "recall": 0.8823529411764706, "f1_score": 0.7894736842105262, "support": 17, "tp": 15, "fp": 6, "fn": 2}, "问库存车": {"precision": 0.5, "recall": 1.0, "f1_score": 0.6666666666666666, "support": 1, "tp": 1, "fp": 1, "fn": 0}, "问政策": {"precision": 0.8888888888888888, "recall": 0.4, "f1_score": 0.5517241379310346, "support": 20, "tp": 8, "fp": 1, "fn": 12}, "问置换": {"precision": 0.4444444444444444, "recall": 0.5, "f1_score": 0.47058823529411764, "support": 8, "tp": 4, "fp": 5, "fn": 4}, "问联系方式": {"precision": 1.0, "recall": 0.3333333333333333, "f1_score": 0.5, "support": 3, "tp": 1, "fp": 0, "fn": 2}, "问试乘试驾": {"precision": 0.8333333333333334, "recall": 0.38461538461538464, "f1_score": 0.5263157894736842, "support": 13, "tp": 5, "fp": 1, "fn": 8}, "问配置": {"precision": 0.8823529411764706, "recall": 0.42857142857142855, "f1_score": 0.5769230769230769, "support": 35, "tp": 15, "fp": 2, "fn": 20}, "问颜色": {"precision": 0.3333333333333333, "recall": 1.0, "f1_score": 0.5, "support": 2, "tp": 2, "fp": 4, "fn": 0}}, "confusion_matrix": {"none": {"none": 85, "想卖车": 0, "撩妹": 9, "看车型": 5, "要买车": 1, "金融政策": 0, "问价格": 1, "问优惠": 1, "问地址": 4, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 1}, "想卖车": {"none": 3, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 1, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "撩妹": {"none": 4, "想卖车": 0, "撩妹": 6, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "看车型": {"none": 13, "想卖车": 0, "撩妹": 1, "看车型": 28, "要买车": 1, "金融政策": 0, "问价格": 1, "问优惠": 0, "问地址": 1, "问库存车": 1, "问政策": 0, "问置换": 2, "问联系方式": 0, "问试乘试驾": 0, "问配置": 1, "问颜色": 2}, "要买车": {"none": 1, "想卖车": 0, "撩妹": 1, "看车型": 0, "要买车": 8, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 1}, "金融政策": {"none": 1, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 1, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问价格": {"none": 7, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 2, "金融政策": 0, "问价格": 32, "问优惠": 3, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 1, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问优惠": {"none": 2, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 2, "问优惠": 11, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 1, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问地址": {"none": 1, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 15, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 1, "问配置": 0, "问颜色": 0}, "问库存车": {"none": 0, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 1, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问政策": {"none": 7, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 1, "金融政策": 2, "问价格": 1, "问优惠": 0, "问地址": 1, "问库存车": 0, "问政策": 8, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问置换": {"none": 2, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 1, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 4, "问联系方式": 0, "问试乘试驾": 0, "问配置": 1, "问颜色": 0}, "问联系方式": {"none": 2, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 1, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问试乘试驾": {"none": 5, "想卖车": 0, "撩妹": 1, "看车型": 0, "要买车": 2, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 5, "问配置": 0, "问颜色": 0}, "问配置": {"none": 14, "想卖车": 0, "撩妹": 0, "看车型": 4, "要买车": 1, "金融政策": 0, "问价格": 1, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 15, "问颜色": 0}, "问颜色": {"none": 0, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 2}}, "total_samples": 345, "valid_predictions": 345, "note": "Calculated on 345 valid samples out of 345 total"}}, "deepseek-v3": {"total_requests": 345, "successful_requests": 345, "success_rate": 1.0, "average_execution_time": 0.0, "result_distribution": {"问价格": 59, "none": 57, "要买车": 46, "看车型": 37, "撩妹": 32, "问配置": 24, "问置换": 22, "问地址": 17, "问优惠": 16, "问试乘试驾": 11, "问政策": 11, "问颜色": 3, "金融政策": 3, "问联系方式": 2, "问库存车": 2, "想卖车": 2, "问价格\n问配置\n问配置\n问政策\n问配置": 1}, "classification_metrics": {"accuracy": 0.5391304347826087, "macro_avg": {"precision": 0.43237847738391566, "recall": 0.5445048031384961, "f1_score": 0.5234675227021565}, "weighted_avg": {"precision": 0.6481625607056671, "recall": 0.5391304347826087, "f1_score": 0.554969277426786}, "per_class_metrics": {"none": {"precision": 0.7368421052631579, "recall": 0.3925233644859813, "f1_score": 0.5121951219512195, "support": 107, "tp": 42, "fp": 15, "fn": 65}, "想卖车": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "support": 4, "tp": 0, "fp": 2, "fn": 4}, "撩妹": {"precision": 0.3125, "recall": 1.0, "f1_score": 0.47619047619047616, "support": 10, "tp": 10, "fp": 22, "fn": 0}, "看车型": {"precision": 0.6486486486486487, "recall": 0.47058823529411764, "f1_score": 0.5454545454545454, "support": 51, "tp": 24, "fp": 13, "fn": 27}, "要买车": {"precision": 0.17391304347826086, "recall": 0.7272727272727273, "f1_score": 0.2807017543859649, "support": 11, "tp": 8, "fp": 38, "fn": 3}, "金融政策": {"precision": 0.3333333333333333, "recall": 0.5, "f1_score": 0.4, "support": 2, "tp": 1, "fp": 2, "fn": 1}, "问价格": {"precision": 0.6610169491525424, "recall": 0.8666666666666667, "f1_score": 0.7500000000000001, "support": 45, "tp": 39, "fp": 20, "fn": 6}, "问价格\n问配置\n问配置\n问政策\n问配置": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "support": 0, "tp": 0, "fp": 1, "fn": 0}, "问优惠": {"precision": 0.625, "recall": 0.625, "f1_score": 0.625, "support": 16, "tp": 10, "fp": 6, "fn": 6}, "问地址": {"precision": 0.6470588235294118, "recall": 0.6470588235294118, "f1_score": 0.6470588235294118, "support": 17, "tp": 11, "fp": 6, "fn": 6}, "问库存车": {"precision": 0.5, "recall": 1.0, "f1_score": 0.6666666666666666, "support": 1, "tp": 1, "fp": 1, "fn": 0}, "问政策": {"precision": 0.8181818181818182, "recall": 0.45, "f1_score": 0.5806451612903226, "support": 20, "tp": 9, "fp": 2, "fn": 11}, "问置换": {"precision": 0.18181818181818182, "recall": 0.5, "f1_score": 0.26666666666666666, "support": 8, "tp": 4, "fp": 18, "fn": 4}, "问联系方式": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "support": 3, "tp": 0, "fp": 2, "fn": 3}, "问试乘试驾": {"precision": 0.5454545454545454, "recall": 0.46153846153846156, "f1_score": 0.4999999999999999, "support": 13, "tp": 6, "fp": 5, "fn": 7}, "问配置": {"precision": 0.8333333333333334, "recall": 0.5714285714285714, "f1_score": 0.6779661016949152, "support": 35, "tp": 20, "fp": 4, "fn": 15}, "问颜色": {"precision": 0.3333333333333333, "recall": 0.5, "f1_score": 0.4, "support": 2, "tp": 1, "fp": 2, "fn": 1}}, "confusion_matrix": {"none": {"none": 42, "想卖车": 2, "撩妹": 16, "看车型": 5, "要买车": 19, "金融政策": 0, "问价格": 4, "问价格\n问配置\n问配置\n问政策\n问配置": 0, "问优惠": 3, "问地址": 5, "问库存车": 0, "问政策": 2, "问置换": 5, "问联系方式": 1, "问试乘试驾": 2, "问配置": 1, "问颜色": 0}, "想卖车": {"none": 0, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 1, "问价格\n问配置\n问配置\n问政策\n问配置": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 3, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "撩妹": {"none": 0, "想卖车": 0, "撩妹": 10, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问价格\n问配置\n问配置\n问政策\n问配置": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "看车型": {"none": 4, "想卖车": 0, "撩妹": 2, "看车型": 24, "要买车": 8, "金融政策": 0, "问价格": 4, "问价格\n问配置\n问配置\n问政策\n问配置": 1, "问优惠": 0, "问地址": 1, "问库存车": 1, "问政策": 0, "问置换": 4, "问联系方式": 0, "问试乘试驾": 0, "问配置": 1, "问颜色": 1}, "要买车": {"none": 0, "想卖车": 0, "撩妹": 3, "看车型": 0, "要买车": 8, "金融政策": 0, "问价格": 0, "问价格\n问配置\n问配置\n问政策\n问配置": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "金融政策": {"none": 0, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 1, "问价格": 1, "问价格\n问配置\n问配置\n问政策\n问配置": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问价格": {"none": 1, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 2, "金融政策": 0, "问价格": 39, "问价格\n问配置\n问配置\n问政策\n问配置": 0, "问优惠": 2, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 1, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问价格\n问配置\n问配置\n问政策\n问配置": {"none": 0, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问价格\n问配置\n问配置\n问政策\n问配置": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问优惠": {"none": 0, "想卖车": 0, "撩妹": 1, "看车型": 0, "要买车": 3, "金融政策": 0, "问价格": 1, "问价格\n问配置\n问配置\n问政策\n问配置": 0, "问优惠": 10, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 1, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问地址": {"none": 1, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 2, "问价格\n问配置\n问配置\n问政策\n问配置": 0, "问优惠": 1, "问地址": 11, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 2, "问配置": 0, "问颜色": 0}, "问库存车": {"none": 0, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问价格\n问配置\n问配置\n问政策\n问配置": 0, "问优惠": 0, "问地址": 0, "问库存车": 1, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问政策": {"none": 3, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 2, "金融政策": 2, "问价格": 1, "问价格\n问配置\n问配置\n问政策\n问配置": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 9, "问置换": 3, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问置换": {"none": 1, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 1, "问价格\n问配置\n问配置\n问政策\n问配置": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 4, "问联系方式": 0, "问试乘试驾": 0, "问配置": 2, "问颜色": 0}, "问联系方式": {"none": 0, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 2, "问价格\n问配置\n问配置\n问政策\n问配置": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 1, "问配置": 0, "问颜色": 0}, "问试乘试驾": {"none": 2, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 3, "金融政策": 0, "问价格": 1, "问价格\n问配置\n问配置\n问政策\n问配置": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 1, "问试乘试驾": 6, "问配置": 0, "问颜色": 0}, "问配置": {"none": 3, "想卖车": 0, "撩妹": 0, "看车型": 8, "要买车": 1, "金融政策": 0, "问价格": 2, "问价格\n问配置\n问配置\n问政策\n问配置": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 20, "问颜色": 1}, "问颜色": {"none": 0, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问价格\n问配置\n问配置\n问政策\n问配置": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 1, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 1}}, "total_samples": 345, "valid_predictions": 345, "note": "Calculated on 345 valid samples out of 345 total"}}, "doubao-1.6-seed": {"total_requests": 345, "successful_requests": 345, "success_rate": 1.0, "average_execution_time": 0.0, "result_distribution": {"none": 99, "看车型": 43, "问价格": 43, "问配置": 29, "要买车": 28, "问地址": 24, "问优惠": 20, "问政策": 20, "问置换": 13, "撩妹": 9, "问试乘试驾": 7, "金融政策": 4, "问颜色": 4, "问库存车": 2}, "classification_metrics": {"accuracy": 0.6869565217391305, "macro_avg": {"precision": 0.628888713372904, "recall": 0.6516582526130184, "f1_score": 0.6544483424956546}, "weighted_avg": {"precision": 0.7069501927022586, "recall": 0.6869565217391305, "f1_score": 0.686209793945607}, "per_class_metrics": {"none": {"precision": 0.7272727272727273, "recall": 0.6728971962616822, "f1_score": 0.6990291262135923, "support": 107, "tp": 72, "fp": 27, "fn": 35}, "想卖车": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "support": 4, "tp": 0, "fp": 0, "fn": 4}, "撩妹": {"precision": 0.8888888888888888, "recall": 0.8, "f1_score": 0.8421052631578948, "support": 10, "tp": 8, "fp": 1, "fn": 2}, "看车型": {"precision": 0.7209302325581395, "recall": 0.6078431372549019, "f1_score": 0.6595744680851063, "support": 51, "tp": 31, "fp": 12, "fn": 20}, "要买车": {"precision": 0.32142857142857145, "recall": 0.8181818181818182, "f1_score": 0.46153846153846156, "support": 11, "tp": 9, "fp": 19, "fn": 2}, "金融政策": {"precision": 0.25, "recall": 0.5, "f1_score": 0.3333333333333333, "support": 2, "tp": 1, "fp": 3, "fn": 1}, "问价格": {"precision": 0.8372093023255814, "recall": 0.8, "f1_score": 0.8181818181818183, "support": 45, "tp": 36, "fp": 7, "fn": 9}, "问优惠": {"precision": 0.65, "recall": 0.8125, "f1_score": 0.7222222222222223, "support": 16, "tp": 13, "fp": 7, "fn": 3}, "问地址": {"precision": 0.7083333333333334, "recall": 1.0, "f1_score": 0.8292682926829268, "support": 17, "tp": 17, "fp": 7, "fn": 0}, "问库存车": {"precision": 0.5, "recall": 1.0, "f1_score": 0.6666666666666666, "support": 1, "tp": 1, "fp": 1, "fn": 0}, "问政策": {"precision": 0.7, "recall": 0.7, "f1_score": 0.7, "support": 20, "tp": 14, "fp": 6, "fn": 6}, "问置换": {"precision": 0.38461538461538464, "recall": 0.625, "f1_score": 0.4761904761904762, "support": 8, "tp": 5, "fp": 8, "fn": 3}, "问联系方式": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "support": 3, "tp": 0, "fp": 0, "fn": 3}, "问试乘试驾": {"precision": 0.8571428571428571, "recall": 0.46153846153846156, "f1_score": 0.6, "support": 13, "tp": 6, "fp": 1, "fn": 7}, "问配置": {"precision": 0.7586206896551724, "recall": 0.6285714285714286, "f1_score": 0.6875, "support": 35, "tp": 22, "fp": 7, "fn": 13}, "问颜色": {"precision": 0.5, "recall": 1.0, "f1_score": 0.6666666666666666, "support": 2, "tp": 2, "fp": 2, "fn": 0}}, "confusion_matrix": {"none": {"none": 72, "想卖车": 0, "撩妹": 1, "看车型": 5, "要买车": 9, "金融政策": 0, "问价格": 0, "问优惠": 4, "问地址": 6, "问库存车": 0, "问政策": 5, "问置换": 3, "问联系方式": 0, "问试乘试驾": 1, "问配置": 1, "问颜色": 0}, "想卖车": {"none": 2, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 2, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "撩妹": {"none": 2, "想卖车": 0, "撩妹": 8, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "看车型": {"none": 6, "想卖车": 0, "撩妹": 0, "看车型": 31, "要买车": 3, "金融政策": 0, "问价格": 1, "问优惠": 0, "问地址": 1, "问库存车": 1, "问政策": 0, "问置换": 2, "问联系方式": 0, "问试乘试驾": 0, "问配置": 4, "问颜色": 2}, "要买车": {"none": 0, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 9, "金融政策": 0, "问价格": 1, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 1, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "金融政策": {"none": 0, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 1, "问价格": 1, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问价格": {"none": 2, "想卖车": 0, "撩妹": 0, "看车型": 1, "要买车": 2, "金融政策": 1, "问价格": 36, "问优惠": 3, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问优惠": {"none": 1, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 2, "问优惠": 13, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问地址": {"none": 0, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 17, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问库存车": {"none": 0, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 1, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问政策": {"none": 1, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 1, "金融政策": 2, "问价格": 1, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 14, "问置换": 1, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问置换": {"none": 0, "想卖车": 0, "撩妹": 0, "看车型": 1, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 5, "问联系方式": 0, "问试乘试驾": 0, "问配置": 2, "问颜色": 0}, "问联系方式": {"none": 3, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问试乘试驾": {"none": 4, "想卖车": 0, "撩妹": 0, "看车型": 1, "要买车": 2, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 6, "问配置": 0, "问颜色": 0}, "问配置": {"none": 6, "想卖车": 0, "撩妹": 0, "看车型": 4, "要买车": 2, "金融政策": 0, "问价格": 1, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 22, "问颜色": 0}, "问颜色": {"none": 0, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 2}}, "total_samples": 345, "valid_predictions": 345, "note": "Calculated on 345 valid samples out of 345 total"}}, "doubao1.5 pro": {"total_requests": 345, "successful_requests": 345, "success_rate": 1.0, "average_execution_time": 0.0, "result_distribution": {"none": 78, "问价格": 46, "要买车": 46, "看车型": 45, "问配置": 35, "问优惠": 21, "问政策": 18, "问地址": 17, "撩妹": 9, "问置换": 9, "问试乘试驾": 9, "问颜色": 4, "金融政策": 3, "问联系方式": 3, "问库存车": 2}, "classification_metrics": {"accuracy": 0.6347826086956522, "macro_avg": {"precision": 0.5841720144021935, "recall": 0.6364694803032318, "f1_score": 0.603075800351342}, "weighted_avg": {"precision": 0.6849232431084983, "recall": 0.6347826086956522, "f1_score": 0.6463186674897538}, "per_class_metrics": {"none": {"precision": 0.7307692307692307, "recall": 0.5327102803738317, "f1_score": 0.6162162162162163, "support": 107, "tp": 57, "fp": 21, "fn": 50}, "想卖车": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "support": 4, "tp": 0, "fp": 0, "fn": 4}, "撩妹": {"precision": 0.8888888888888888, "recall": 0.8, "f1_score": 0.8421052631578948, "support": 10, "tp": 8, "fp": 1, "fn": 2}, "看车型": {"precision": 0.7111111111111111, "recall": 0.6274509803921569, "f1_score": 0.6666666666666666, "support": 51, "tp": 32, "fp": 13, "fn": 19}, "要买车": {"precision": 0.1956521739130435, "recall": 0.8181818181818182, "f1_score": 0.3157894736842105, "support": 11, "tp": 9, "fp": 37, "fn": 2}, "金融政策": {"precision": 0.3333333333333333, "recall": 0.5, "f1_score": 0.4, "support": 2, "tp": 1, "fp": 2, "fn": 1}, "问价格": {"precision": 0.717391304347826, "recall": 0.7333333333333333, "f1_score": 0.7252747252747253, "support": 45, "tp": 33, "fp": 13, "fn": 12}, "问优惠": {"precision": 0.6190476190476191, "recall": 0.8125, "f1_score": 0.7027027027027026, "support": 16, "tp": 13, "fp": 8, "fn": 3}, "问地址": {"precision": 0.8235294117647058, "recall": 0.8235294117647058, "f1_score": 0.8235294117647058, "support": 17, "tp": 14, "fp": 3, "fn": 3}, "问库存车": {"precision": 0.5, "recall": 1.0, "f1_score": 0.6666666666666666, "support": 1, "tp": 1, "fp": 1, "fn": 0}, "问政策": {"precision": 0.7777777777777778, "recall": 0.7, "f1_score": 0.7368421052631577, "support": 20, "tp": 14, "fp": 4, "fn": 6}, "问置换": {"precision": 0.3333333333333333, "recall": 0.375, "f1_score": 0.35294117647058826, "support": 8, "tp": 3, "fp": 6, "fn": 5}, "问联系方式": {"precision": 0.3333333333333333, "recall": 0.3333333333333333, "f1_score": 0.3333333333333333, "support": 3, "tp": 1, "fp": 2, "fn": 2}, "问试乘试驾": {"precision": 0.5555555555555556, "recall": 0.38461538461538464, "f1_score": 0.4545454545454546, "support": 13, "tp": 5, "fp": 4, "fn": 8}, "问配置": {"precision": 0.7428571428571429, "recall": 0.7428571428571429, "f1_score": 0.7428571428571429, "support": 35, "tp": 26, "fp": 9, "fn": 9}, "问颜色": {"precision": 0.5, "recall": 1.0, "f1_score": 0.6666666666666666, "support": 2, "tp": 2, "fp": 2, "fn": 0}}, "confusion_matrix": {"none": {"none": 57, "想卖车": 0, "撩妹": 1, "看车型": 8, "要买车": 23, "金融政策": 0, "问价格": 2, "问优惠": 3, "问地址": 3, "问库存车": 0, "问政策": 2, "问置换": 2, "问联系方式": 1, "问试乘试驾": 2, "问配置": 3, "问颜色": 0}, "想卖车": {"none": 2, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 2, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "撩妹": {"none": 2, "想卖车": 0, "撩妹": 8, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "看车型": {"none": 6, "想卖车": 0, "撩妹": 0, "看车型": 32, "要买车": 2, "金融政策": 0, "问价格": 3, "问优惠": 0, "问地址": 0, "问库存车": 1, "问政策": 0, "问置换": 1, "问联系方式": 0, "问试乘试驾": 0, "问配置": 4, "问颜色": 2}, "要买车": {"none": 0, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 9, "金融政策": 0, "问价格": 1, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 1, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "金融政策": {"none": 0, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 1, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 1, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问价格": {"none": 2, "想卖车": 0, "撩妹": 0, "看车型": 3, "要买车": 3, "金融政策": 0, "问价格": 33, "问优惠": 3, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 1, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问优惠": {"none": 0, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 2, "金融政策": 0, "问价格": 1, "问优惠": 13, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问地址": {"none": 1, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 14, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 2, "问配置": 0, "问颜色": 0}, "问库存车": {"none": 0, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 1, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问政策": {"none": 1, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 1, "金融政策": 2, "问价格": 1, "问优惠": 1, "问地址": 0, "问库存车": 0, "问政策": 14, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问置换": {"none": 1, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 1, "问优惠": 1, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 3, "问联系方式": 0, "问试乘试驾": 0, "问配置": 2, "问颜色": 0}, "问联系方式": {"none": 0, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 2, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 1, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问试乘试驾": {"none": 3, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 4, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 1, "问试乘试驾": 5, "问配置": 0, "问颜色": 0}, "问配置": {"none": 3, "想卖车": 0, "撩妹": 0, "看车型": 2, "要买车": 2, "金融政策": 0, "问价格": 2, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 26, "问颜色": 0}, "问颜色": {"none": 0, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 2}}, "total_samples": 345, "valid_predictions": 345, "note": "Calculated on 345 valid samples out of 345 total"}}, "qwen-plus": {"total_requests": 345, "successful_requests": 345, "success_rate": 1.0, "average_execution_time": 0.0, "result_distribution": {"none": 65, "问价格": 52, "问配置": 37, "看车型": 33, "问地址": 31, "问优惠": 25, "问政策": 22, "要买车": 20, "问置换": 19, "问试乘试驾": 15, "撩妹": 11, "问颜色": 5, "金融政策": 3, "问联系方式": 3, "问库存车": 2, "问续航": 1, "想卖车": 1}, "classification_metrics": {"accuracy": 0.5855072463768116, "macro_avg": {"precision": 0.4315259117975586, "recall": 0.5967182334150791, "f1_score": 0.5683285448491786}, "weighted_avg": {"precision": 0.6402777084259331, "recall": 0.5855072463768116, "f1_score": 0.5890306181253725}, "per_class_metrics": {"none": {"precision": 0.7076923076923077, "recall": 0.42990654205607476, "f1_score": 0.5348837209302326, "support": 107, "tp": 46, "fp": 19, "fn": 61}, "想卖车": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "support": 4, "tp": 0, "fp": 1, "fn": 4}, "撩妹": {"precision": 0.6363636363636364, "recall": 0.7, "f1_score": 0.6666666666666666, "support": 10, "tp": 7, "fp": 4, "fn": 3}, "看车型": {"precision": 0.8787878787878788, "recall": 0.5686274509803921, "f1_score": 0.6904761904761905, "support": 51, "tp": 29, "fp": 4, "fn": 22}, "要买车": {"precision": 0.3, "recall": 0.5454545454545454, "f1_score": 0.3870967741935483, "support": 11, "tp": 6, "fp": 14, "fn": 5}, "金融政策": {"precision": 0.3333333333333333, "recall": 0.5, "f1_score": 0.4, "support": 2, "tp": 1, "fp": 2, "fn": 1}, "问价格": {"precision": 0.6538461538461539, "recall": 0.7555555555555555, "f1_score": 0.7010309278350516, "support": 45, "tp": 34, "fp": 18, "fn": 11}, "问优惠": {"precision": 0.56, "recall": 0.875, "f1_score": 0.6829268292682927, "support": 16, "tp": 14, "fp": 11, "fn": 2}, "问地址": {"precision": 0.41935483870967744, "recall": 0.7647058823529411, "f1_score": 0.5416666666666666, "support": 17, "tp": 13, "fp": 18, "fn": 4}, "问库存车": {"precision": 0.5, "recall": 1.0, "f1_score": 0.6666666666666666, "support": 1, "tp": 1, "fp": 1, "fn": 0}, "问政策": {"precision": 0.5, "recall": 0.55, "f1_score": 0.5238095238095238, "support": 20, "tp": 11, "fp": 11, "fn": 9}, "问续航": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "support": 0, "tp": 0, "fp": 1, "fn": 0}, "问置换": {"precision": 0.21052631578947367, "recall": 0.5, "f1_score": 0.2962962962962963, "support": 8, "tp": 4, "fp": 15, "fn": 4}, "问联系方式": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "support": 3, "tp": 0, "fp": 3, "fn": 3}, "问试乘试驾": {"precision": 0.5333333333333333, "recall": 0.6153846153846154, "f1_score": 0.5714285714285715, "support": 13, "tp": 8, "fp": 7, "fn": 5}, "问配置": {"precision": 0.7027027027027027, "recall": 0.7428571428571429, "f1_score": 0.7222222222222223, "support": 35, "tp": 26, "fp": 11, "fn": 9}, "问颜色": {"precision": 0.4, "recall": 1.0, "f1_score": 0.5714285714285715, "support": 2, "tp": 2, "fp": 3, "fn": 0}}, "confusion_matrix": {"none": {"none": 46, "想卖车": 1, "撩妹": 4, "看车型": 1, "要买车": 9, "金融政策": 0, "问价格": 4, "问优惠": 4, "问地址": 16, "问库存车": 1, "问政策": 6, "问续航": 0, "问置换": 5, "问联系方式": 1, "问试乘试驾": 4, "问配置": 4, "问颜色": 1}, "想卖车": {"none": 1, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 1, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问续航": 0, "问置换": 2, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "撩妹": {"none": 3, "想卖车": 0, "撩妹": 7, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问续航": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "看车型": {"none": 5, "想卖车": 0, "撩妹": 0, "看车型": 29, "要买车": 1, "金融政策": 0, "问价格": 5, "问优惠": 0, "问地址": 1, "问库存车": 0, "问政策": 0, "问续航": 0, "问置换": 6, "问联系方式": 0, "问试乘试驾": 0, "问配置": 3, "问颜色": 1}, "要买车": {"none": 2, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 6, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 1, "问库存车": 0, "问政策": 1, "问续航": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 1}, "金融政策": {"none": 0, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 1, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 1, "问续航": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问价格": {"none": 0, "想卖车": 0, "撩妹": 0, "看车型": 1, "要买车": 1, "金融政策": 0, "问价格": 34, "问优惠": 3, "问地址": 0, "问库存车": 0, "问政策": 1, "问续航": 0, "问置换": 2, "问联系方式": 0, "问试乘试驾": 0, "问配置": 3, "问颜色": 0}, "问优惠": {"none": 0, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 2, "问优惠": 14, "问地址": 0, "问库存车": 0, "问政策": 0, "问续航": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问地址": {"none": 1, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 13, "问库存车": 0, "问政策": 1, "问续航": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 2, "问配置": 0, "问颜色": 0}, "问库存车": {"none": 0, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 1, "问政策": 0, "问续航": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问政策": {"none": 1, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 1, "金融政策": 2, "问价格": 2, "问优惠": 3, "问地址": 0, "问库存车": 0, "问政策": 11, "问续航": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问续航": {"none": 0, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问续航": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问置换": {"none": 1, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 1, "金融政策": 0, "问价格": 1, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问续航": 0, "问置换": 4, "问联系方式": 0, "问试乘试驾": 0, "问配置": 1, "问颜色": 0}, "问联系方式": {"none": 0, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 2, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问续航": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 1, "问配置": 0, "问颜色": 0}, "问试乘试驾": {"none": 3, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问续航": 0, "问置换": 0, "问联系方式": 2, "问试乘试驾": 8, "问配置": 0, "问颜色": 0}, "问配置": {"none": 2, "想卖车": 0, "撩妹": 0, "看车型": 2, "要买车": 1, "金融政策": 0, "问价格": 1, "问优惠": 1, "问地址": 0, "问库存车": 0, "问政策": 1, "问续航": 1, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 26, "问颜色": 0}, "问颜色": {"none": 0, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问续航": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 2}}, "total_samples": 345, "valid_predictions": 345, "note": "Calculated on 345 valid samples out of 345 total"}}, "通义千问3-235B-A22B": {"total_requests": 345, "successful_requests": 232, "success_rate": 0.672463768115942, "average_execution_time": 0.0, "result_distribution": {"none": 48, "问价格": 37, "问配置": 27, "问地址": 23, "看车型": 20, "问优惠": 14, "问政策": 12, "问置换": 11, "问试乘试驾": 11, "要买车": 10, "撩妹": 5, "问颜色": 4, "问库存车": 3, "问续航": 2, "问联系方式": 2, "想卖车": 2, "金融政策": 1}, "classification_metrics": {"accuracy": 0.5905172413793104, "macro_avg": {"precision": 0.40338423669625717, "recall": 0.5453580447330446, "f1_score": 0.5532185129438978}, "weighted_avg": {"precision": 0.6419729992068823, "recall": 0.5905172413793104, "f1_score": 0.5948902056900018}, "per_class_metrics": {"none": {"precision": 0.7083333333333334, "recall": 0.4722222222222222, "f1_score": 0.5666666666666667, "support": 72, "tp": 34, "fp": 14, "fn": 38}, "想卖车": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "support": 2, "tp": 0, "fp": 2, "fn": 2}, "撩妹": {"precision": 0.8, "recall": 0.5714285714285714, "f1_score": 0.6666666666666666, "support": 7, "tp": 4, "fp": 1, "fn": 3}, "看车型": {"precision": 0.85, "recall": 0.5151515151515151, "f1_score": 0.6415094339622641, "support": 33, "tp": 17, "fp": 3, "fn": 16}, "要买车": {"precision": 0.2, "recall": 0.3333333333333333, "f1_score": 0.25, "support": 6, "tp": 2, "fp": 8, "fn": 4}, "金融政策": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "support": 1, "tp": 0, "fp": 1, "fn": 1}, "问价格": {"precision": 0.6756756756756757, "recall": 0.8333333333333334, "f1_score": 0.746268656716418, "support": 30, "tp": 25, "fp": 12, "fn": 5}, "问优惠": {"precision": 0.7142857142857143, "recall": 1.0, "f1_score": 0.8333333333333333, "support": 10, "tp": 10, "fp": 4, "fn": 0}, "问地址": {"precision": 0.4782608695652174, "recall": 0.7857142857142857, "f1_score": 0.5945945945945946, "support": 14, "tp": 11, "fp": 12, "fn": 3}, "问库存车": {"precision": 0.3333333333333333, "recall": 1.0, "f1_score": 0.5, "support": 1, "tp": 1, "fp": 2, "fn": 0}, "问政策": {"precision": 0.4166666666666667, "recall": 0.5, "f1_score": 0.45454545454545453, "support": 10, "tp": 5, "fp": 7, "fn": 5}, "问续航": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "support": 0, "tp": 0, "fp": 2, "fn": 0}, "问置换": {"precision": 0.2727272727272727, "recall": 0.5, "f1_score": 0.3529411764705882, "support": 6, "tp": 3, "fp": 8, "fn": 3}, "问联系方式": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "support": 3, "tp": 0, "fp": 2, "fn": 3}, "问试乘试驾": {"precision": 0.45454545454545453, "recall": 0.45454545454545453, "f1_score": 0.45454545454545453, "support": 11, "tp": 5, "fp": 6, "fn": 6}, "问配置": {"precision": 0.7037037037037037, "recall": 0.76, "f1_score": 0.7307692307692308, "support": 25, "tp": 19, "fp": 8, "fn": 6}, "问颜色": {"precision": 0.25, "recall": 1.0, "f1_score": 0.4, "support": 1, "tp": 1, "fp": 3, "fn": 0}}, "confusion_matrix": {"none": {"none": 34, "想卖车": 2, "撩妹": 1, "看车型": 1, "要买车": 3, "金融政策": 0, "问价格": 4, "问优惠": 1, "问地址": 11, "问库存车": 1, "问政策": 3, "问续航": 0, "问置换": 2, "问联系方式": 1, "问试乘试驾": 3, "问配置": 4, "问颜色": 1}, "想卖车": {"none": 0, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 1, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问续航": 0, "问置换": 1, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "撩妹": {"none": 3, "想卖车": 0, "撩妹": 4, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问续航": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "看车型": {"none": 5, "想卖车": 0, "撩妹": 0, "看车型": 17, "要买车": 2, "金融政策": 0, "问价格": 1, "问优惠": 0, "问地址": 0, "问库存车": 1, "问政策": 0, "问续航": 0, "问置换": 4, "问联系方式": 0, "问试乘试驾": 0, "问配置": 2, "问颜色": 1}, "要买车": {"none": 1, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 2, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 1, "问库存车": 0, "问政策": 1, "问续航": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 1}, "金融政策": {"none": 0, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 1, "问续航": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问价格": {"none": 0, "想卖车": 0, "撩妹": 0, "看车型": 1, "要买车": 0, "金融政策": 0, "问价格": 25, "问优惠": 2, "问地址": 0, "问库存车": 0, "问政策": 0, "问续航": 0, "问置换": 1, "问联系方式": 0, "问试乘试驾": 0, "问配置": 1, "问颜色": 0}, "问优惠": {"none": 0, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 10, "问地址": 0, "问库存车": 0, "问政策": 0, "问续航": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问地址": {"none": 0, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 11, "问库存车": 0, "问政策": 1, "问续航": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 2, "问配置": 0, "问颜色": 0}, "问库存车": {"none": 0, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 1, "问政策": 0, "问续航": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问政策": {"none": 1, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 1, "问价格": 2, "问优惠": 1, "问地址": 0, "问库存车": 0, "问政策": 5, "问续航": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问续航": {"none": 0, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问续航": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问置换": {"none": 0, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 1, "金融政策": 0, "问价格": 1, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问续航": 0, "问置换": 3, "问联系方式": 0, "问试乘试驾": 0, "问配置": 1, "问颜色": 0}, "问联系方式": {"none": 0, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 2, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问续航": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 1, "问配置": 0, "问颜色": 0}, "问试乘试驾": {"none": 4, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 1, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问续航": 0, "问置换": 0, "问联系方式": 1, "问试乘试驾": 5, "问配置": 0, "问颜色": 0}, "问配置": {"none": 0, "想卖车": 0, "撩妹": 0, "看车型": 1, "要买车": 1, "金融政策": 0, "问价格": 1, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 1, "问续航": 2, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 19, "问颜色": 0}, "问颜色": {"none": 0, "想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问续航": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 1}}, "total_samples": 232, "valid_predictions": 232, "note": "Calculated on 232 valid samples out of 345 total"}}}}