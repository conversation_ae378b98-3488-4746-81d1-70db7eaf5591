
AI模型分类指标评估摘要报告
==================================================

报告信息:
- 生成时间: 2025-08-05 11:35:12
- 数据文件: /Users/<USER>/kiro/对比/ai-model-evaluation/evaluation_results/yitu_20250805_113340/yitu_20250805_113340_results.csv
- 总样本数: 345
- 分析模型: ERNIE 4.5 Turbo, deepseek-v3, doubao-1.6-seed, doubao1.5 pro, qwen-plus, 通义千问3-235B-A22B


ERNIE 4.5 Turbo 模型分析结果:
------------------------------

基本统计:
- 成功预测: 345/345 (100.0%)
- 有效对比样本: 345 (100.0%)

分类指标:
- 准确率 (Accuracy): 64.06%

宏平均指标:
- 精确率 (Precision): 62.13%
- 召回率 (Recall): 56.24%
- F1分数 (F1-Score): 59.86%

加权平均指标:
- 精确率 (Precision): 68.79%
- 召回率 (Recall): 64.06%
- F1分数 (F1-Score): 63.46%

各类别表现 (按支持度排序):
   1. none            - 精确率:57.82%, 召回率:79.44%, F1:66.93%, 支持度:107
   2. 看车型             - 精确率:75.68%, 召回率:54.90%, F1:63.64%, 支持度:51
   3. 问价格             - 精确率:82.05%, 召回率:71.11%, F1:76.19%, 支持度:45
   4. 问配置             - 精确率:88.24%, 召回率:42.86%, F1:57.69%, 支持度:35
   5. 问政策             - 精确率:88.89%, 召回率:40.00%, F1:55.17%, 支持度:20
   6. 问地址             - 精确率:71.43%, 召回率:88.24%, F1:78.95%, 支持度:17
   7. 问优惠             - 精确率:73.33%, 召回率:68.75%, F1:70.97%, 支持度:16
   8. 问试乘试驾           - 精确率:83.33%, 召回率:38.46%, F1:52.63%, 支持度:13
   9. 要买车             - 精确率:50.00%, 召回率:72.73%, F1:59.26%, 支持度:11
  10. 撩妹              - 精确率:33.33%, 召回率:60.00%, F1:42.86%, 支持度:10
  ... 还有 6 个类别

性能评估:
- 整体表现: 良好
- 类别平衡性: 一般
- 表现最佳类别: 问地址 (F1: 0.789)
- 表现最差类别: 想卖车 (F1: 0.000)

主要预测类别 (前5):
  - none: 147 (42.6%)
  - 问价格: 39 (11.3%)
  - 看车型: 37 (10.7%)
  - 问地址: 21 (6.1%)
  - 撩妹: 18 (5.2%)


deepseek-v3 模型分析结果:
------------------------------

基本统计:
- 成功预测: 345/345 (100.0%)
- 有效对比样本: 345 (100.0%)

分类指标:
- 准确率 (Accuracy): 53.91%

宏平均指标:
- 精确率 (Precision): 43.24%
- 召回率 (Recall): 54.45%
- F1分数 (F1-Score): 52.35%

加权平均指标:
- 精确率 (Precision): 64.82%
- 召回率 (Recall): 53.91%
- F1分数 (F1-Score): 55.50%

各类别表现 (按支持度排序):
   1. none            - 精确率:73.68%, 召回率:39.25%, F1:51.22%, 支持度:107
   2. 看车型             - 精确率:64.86%, 召回率:47.06%, F1:54.55%, 支持度:51
   3. 问价格             - 精确率:66.10%, 召回率:86.67%, F1:75.00%, 支持度:45
   4. 问配置             - 精确率:83.33%, 召回率:57.14%, F1:67.80%, 支持度:35
   5. 问政策             - 精确率:81.82%, 召回率:45.00%, F1:58.06%, 支持度:20
   6. 问地址             - 精确率:64.71%, 召回率:64.71%, F1:64.71%, 支持度:17
   7. 问优惠             - 精确率:62.50%, 召回率:62.50%, F1:62.50%, 支持度:16
   8. 问试乘试驾           - 精确率:54.55%, 召回率:46.15%, F1:50.00%, 支持度:13
   9. 要买车             - 精确率:17.39%, 召回率:72.73%, F1:28.07%, 支持度:11
  10. 撩妹              - 精确率:31.25%, 召回率:100.00%, F1:47.62%, 支持度:10
  ... 还有 7 个类别

性能评估:
- 整体表现: 一般
- 类别平衡性: 一般
- 表现最佳类别: 问价格 (F1: 0.750)
- 表现最差类别: 想卖车 (F1: 0.000)

主要预测类别 (前5):
  - 问价格: 59 (17.1%)
  - none: 57 (16.5%)
  - 要买车: 46 (13.3%)
  - 看车型: 37 (10.7%)
  - 撩妹: 32 (9.3%)


doubao-1.6-seed 模型分析结果:
------------------------------

基本统计:
- 成功预测: 345/345 (100.0%)
- 有效对比样本: 345 (100.0%)

分类指标:
- 准确率 (Accuracy): 68.70%

宏平均指标:
- 精确率 (Precision): 62.89%
- 召回率 (Recall): 65.17%
- F1分数 (F1-Score): 65.44%

加权平均指标:
- 精确率 (Precision): 70.70%
- 召回率 (Recall): 68.70%
- F1分数 (F1-Score): 68.62%

各类别表现 (按支持度排序):
   1. none            - 精确率:72.73%, 召回率:67.29%, F1:69.90%, 支持度:107
   2. 看车型             - 精确率:72.09%, 召回率:60.78%, F1:65.96%, 支持度:51
   3. 问价格             - 精确率:83.72%, 召回率:80.00%, F1:81.82%, 支持度:45
   4. 问配置             - 精确率:75.86%, 召回率:62.86%, F1:68.75%, 支持度:35
   5. 问政策             - 精确率:70.00%, 召回率:70.00%, F1:70.00%, 支持度:20
   6. 问地址             - 精确率:70.83%, 召回率:100.00%, F1:82.93%, 支持度:17
   7. 问优惠             - 精确率:65.00%, 召回率:81.25%, F1:72.22%, 支持度:16
   8. 问试乘试驾           - 精确率:85.71%, 召回率:46.15%, F1:60.00%, 支持度:13
   9. 要买车             - 精确率:32.14%, 召回率:81.82%, F1:46.15%, 支持度:11
  10. 撩妹              - 精确率:88.89%, 召回率:80.00%, F1:84.21%, 支持度:10
  ... 还有 6 个类别

性能评估:
- 整体表现: 良好
- 类别平衡性: 一般
- 表现最佳类别: 撩妹 (F1: 0.842)
- 表现最差类别: 想卖车 (F1: 0.000)

主要预测类别 (前5):
  - none: 99 (28.7%)
  - 看车型: 43 (12.5%)
  - 问价格: 43 (12.5%)
  - 问配置: 29 (8.4%)
  - 要买车: 28 (8.1%)


doubao1.5 pro 模型分析结果:
------------------------------

基本统计:
- 成功预测: 345/345 (100.0%)
- 有效对比样本: 345 (100.0%)

分类指标:
- 准确率 (Accuracy): 63.48%

宏平均指标:
- 精确率 (Precision): 58.42%
- 召回率 (Recall): 63.65%
- F1分数 (F1-Score): 60.31%

加权平均指标:
- 精确率 (Precision): 68.49%
- 召回率 (Recall): 63.48%
- F1分数 (F1-Score): 64.63%

各类别表现 (按支持度排序):
   1. none            - 精确率:73.08%, 召回率:53.27%, F1:61.62%, 支持度:107
   2. 看车型             - 精确率:71.11%, 召回率:62.75%, F1:66.67%, 支持度:51
   3. 问价格             - 精确率:71.74%, 召回率:73.33%, F1:72.53%, 支持度:45
   4. 问配置             - 精确率:74.29%, 召回率:74.29%, F1:74.29%, 支持度:35
   5. 问政策             - 精确率:77.78%, 召回率:70.00%, F1:73.68%, 支持度:20
   6. 问地址             - 精确率:82.35%, 召回率:82.35%, F1:82.35%, 支持度:17
   7. 问优惠             - 精确率:61.90%, 召回率:81.25%, F1:70.27%, 支持度:16
   8. 问试乘试驾           - 精确率:55.56%, 召回率:38.46%, F1:45.45%, 支持度:13
   9. 要买车             - 精确率:19.57%, 召回率:81.82%, F1:31.58%, 支持度:11
  10. 撩妹              - 精确率:88.89%, 召回率:80.00%, F1:84.21%, 支持度:10
  ... 还有 6 个类别

性能评估:
- 整体表现: 良好
- 类别平衡性: 一般
- 表现最佳类别: 撩妹 (F1: 0.842)
- 表现最差类别: 想卖车 (F1: 0.000)

主要预测类别 (前5):
  - none: 78 (22.6%)
  - 问价格: 46 (13.3%)
  - 要买车: 46 (13.3%)
  - 看车型: 45 (13.0%)
  - 问配置: 35 (10.1%)


qwen-plus 模型分析结果:
------------------------------

基本统计:
- 成功预测: 345/345 (100.0%)
- 有效对比样本: 345 (100.0%)

分类指标:
- 准确率 (Accuracy): 58.55%

宏平均指标:
- 精确率 (Precision): 43.15%
- 召回率 (Recall): 59.67%
- F1分数 (F1-Score): 56.83%

加权平均指标:
- 精确率 (Precision): 64.03%
- 召回率 (Recall): 58.55%
- F1分数 (F1-Score): 58.90%

各类别表现 (按支持度排序):
   1. none            - 精确率:70.77%, 召回率:42.99%, F1:53.49%, 支持度:107
   2. 看车型             - 精确率:87.88%, 召回率:56.86%, F1:69.05%, 支持度:51
   3. 问价格             - 精确率:65.38%, 召回率:75.56%, F1:70.10%, 支持度:45
   4. 问配置             - 精确率:70.27%, 召回率:74.29%, F1:72.22%, 支持度:35
   5. 问政策             - 精确率:50.00%, 召回率:55.00%, F1:52.38%, 支持度:20
   6. 问地址             - 精确率:41.94%, 召回率:76.47%, F1:54.17%, 支持度:17
   7. 问优惠             - 精确率:56.00%, 召回率:87.50%, F1:68.29%, 支持度:16
   8. 问试乘试驾           - 精确率:53.33%, 召回率:61.54%, F1:57.14%, 支持度:13
   9. 要买车             - 精确率:30.00%, 召回率:54.55%, F1:38.71%, 支持度:11
  10. 撩妹              - 精确率:63.64%, 召回率:70.00%, F1:66.67%, 支持度:10
  ... 还有 7 个类别

性能评估:
- 整体表现: 一般
- 类别平衡性: 一般
- 表现最佳类别: 问配置 (F1: 0.722)
- 表现最差类别: 想卖车 (F1: 0.000)

主要预测类别 (前5):
  - none: 65 (18.8%)
  - 问价格: 52 (15.1%)
  - 问配置: 37 (10.7%)
  - 看车型: 33 (9.6%)
  - 问地址: 31 (9.0%)


通义千问3-235B-A22B 模型分析结果:
------------------------------

基本统计:
- 成功预测: 232/345 (67.2%)
- 有效对比样本: 232 (67.2%)

分类指标:
- 准确率 (Accuracy): 59.05%

宏平均指标:
- 精确率 (Precision): 40.34%
- 召回率 (Recall): 54.54%
- F1分数 (F1-Score): 55.32%

加权平均指标:
- 精确率 (Precision): 64.20%
- 召回率 (Recall): 59.05%
- F1分数 (F1-Score): 59.49%

各类别表现 (按支持度排序):
   1. none            - 精确率:70.83%, 召回率:47.22%, F1:56.67%, 支持度:72
   2. 看车型             - 精确率:85.00%, 召回率:51.52%, F1:64.15%, 支持度:33
   3. 问价格             - 精确率:67.57%, 召回率:83.33%, F1:74.63%, 支持度:30
   4. 问配置             - 精确率:70.37%, 召回率:76.00%, F1:73.08%, 支持度:25
   5. 问地址             - 精确率:47.83%, 召回率:78.57%, F1:59.46%, 支持度:14
   6. 问试乘试驾           - 精确率:45.45%, 召回率:45.45%, F1:45.45%, 支持度:11
   7. 问优惠             - 精确率:71.43%, 召回率:100.00%, F1:83.33%, 支持度:10
   8. 问政策             - 精确率:41.67%, 召回率:50.00%, F1:45.45%, 支持度:10
   9. 撩妹              - 精确率:80.00%, 召回率:57.14%, F1:66.67%, 支持度:7
  10. 要买车             - 精确率:20.00%, 召回率:33.33%, F1:25.00%, 支持度:6
  ... 还有 7 个类别

性能评估:
- 整体表现: 一般
- 类别平衡性: 一般
- 表现最佳类别: 问优惠 (F1: 0.833)
- 表现最差类别: 问联系方式 (F1: 0.000)

主要预测类别 (前5):
  - none: 48 (20.7%)
  - 问价格: 37 (15.9%)
  - 问配置: 27 (11.6%)
  - 问地址: 23 (9.9%)
  - 看车型: 20 (8.6%)


改进建议:
--------------------

ERNIE 4.5 Turbo 模型:
- 整体准确率需要提升
- 减少漏报，提高召回率
- 重点改进类别: 撩妹, 问置换

deepseek-v3 模型:
- 整体准确率需要提升
- 减少误报，提高精确率
- 减少漏报，提高召回率
- 重点改进类别: 撩妹, 要买车, 问置换

doubao-1.6-seed 模型:
- 整体准确率需要提升
- 重点改进类别: 要买车, 问置换

doubao1.5 pro 模型:
- 整体准确率需要提升
- 减少误报，提高精确率
- 重点改进类别: 要买车, 问置换, 问试乘试驾

qwen-plus 模型:
- 整体准确率需要提升
- 减少误报，提高精确率
- 减少漏报，提高召回率
- 重点改进类别: 要买车, 问置换

通义千问3-235B-A22B 模型:
- 提高预测成功率 (当前 67.2%)
- 整体准确率需要提升
- 减少误报，提高精确率
- 减少漏报，提高召回率
- 重点改进类别: 要买车, 问政策, 问置换

报告生成完成 - 2025-08-05 11:35:12
