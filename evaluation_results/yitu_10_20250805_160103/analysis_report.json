{"file_info": {"total_rows": 10, "columns": ["live_comment", "expected_result", "ERNIE 4.5 Turbo_result", "deepseek-v3_result", "doubao-1.6-seed_result", "doubao1.5 pro_result", "qwen-plus_result", "通义千问3-235B-A22B_result"]}, "model_performance": {"ERNIE 4.5 Turbo": {"total_requests": 10, "successful_requests": 10, "success_rate": 1.0, "average_execution_time": 0.0, "result_distribution": {"none": 7, "看车型": 1, "问优惠": 1, "问地址": 1}, "classification_metrics": {"accuracy": 0.7, "accuracy_warning": "准确率可能受类别不平衡影响，建议重点关注F1、Precision、Recall指标", "precision": 1.0, "recall": 0.5, "f1_score": 0.6666666666666666, "total_samples": 10, "valid_predictions": 10, "positive_classes": ["撩妹", "看车型", "问优惠", "问地址"], "none_class": "none", "total_positive_samples": 6, "overall_metrics": {"tp": 3, "fp": 0, "fn": 3, "tn": 4}, "macro_avg": {"precision": 0.75, "recall": 0.625, "f1_score": 0.6666666666666666}, "weighted_avg": {"precision": 0.6666666666666666, "recall": 0.5, "f1_score": 0.5555555555555555}, "per_class_metrics": {"none": {"precision": 0.5714285714285714, "recall": 1.0, "f1_score": 0.7272727272727273, "support": 4, "tp": 4, "fp": 3, "fn": 0, "is_positive_class": false}, "撩妹": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "support": 2, "tp": 0, "fp": 0, "fn": 2, "is_positive_class": true}, "看车型": {"precision": 1.0, "recall": 0.5, "f1_score": 0.6666666666666666, "support": 2, "tp": 1, "fp": 0, "fn": 1, "is_positive_class": true}, "问优惠": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 1, "tp": 1, "fp": 0, "fn": 0, "is_positive_class": true}, "问地址": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 1, "tp": 1, "fp": 0, "fn": 0, "is_positive_class": true}}, "confusion_matrix": {"none": {"none": 4, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 0}, "撩妹": {"none": 2, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 0}, "看车型": {"none": 1, "撩妹": 0, "看车型": 1, "问优惠": 0, "问地址": 0}, "问优惠": {"none": 0, "撩妹": 0, "看车型": 0, "问优惠": 1, "问地址": 0}, "问地址": {"none": 0, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 1}}, "note": "基于10个样本的正向意图识别分析，其中4个正向类别，10个有效预测", "methodology": "根据sunfa.md文档，将'none'视为负例，其他类别视为正例，重点评估正向意图识别能力"}}, "deepseek-v3": {"total_requests": 10, "successful_requests": 10, "success_rate": 1.0, "average_execution_time": 0.0, "result_distribution": {"none": 5, "撩妹": 2, "看车型": 1, "问优惠": 1, "问地址": 1}, "classification_metrics": {"accuracy": 0.9, "accuracy_warning": "准确率可能受类别不平衡影响，建议重点关注F1、Precision、Recall指标", "precision": 1.0, "recall": 0.8333333333333334, "f1_score": 0.9090909090909091, "total_samples": 10, "valid_predictions": 10, "positive_classes": ["撩妹", "看车型", "问优惠", "问地址"], "none_class": "none", "total_positive_samples": 6, "overall_metrics": {"tp": 5, "fp": 0, "fn": 1, "tn": 4}, "macro_avg": {"precision": 1.0, "recall": 0.875, "f1_score": 0.9166666666666666}, "weighted_avg": {"precision": 1.0, "recall": 0.8333333333333334, "f1_score": 0.8888888888888888}, "per_class_metrics": {"none": {"precision": 0.8, "recall": 1.0, "f1_score": 0.888888888888889, "support": 4, "tp": 4, "fp": 1, "fn": 0, "is_positive_class": false}, "撩妹": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 2, "tp": 2, "fp": 0, "fn": 0, "is_positive_class": true}, "看车型": {"precision": 1.0, "recall": 0.5, "f1_score": 0.6666666666666666, "support": 2, "tp": 1, "fp": 0, "fn": 1, "is_positive_class": true}, "问优惠": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 1, "tp": 1, "fp": 0, "fn": 0, "is_positive_class": true}, "问地址": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 1, "tp": 1, "fp": 0, "fn": 0, "is_positive_class": true}}, "confusion_matrix": {"none": {"none": 4, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 0}, "撩妹": {"none": 0, "撩妹": 2, "看车型": 0, "问优惠": 0, "问地址": 0}, "看车型": {"none": 1, "撩妹": 0, "看车型": 1, "问优惠": 0, "问地址": 0}, "问优惠": {"none": 0, "撩妹": 0, "看车型": 0, "问优惠": 1, "问地址": 0}, "问地址": {"none": 0, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 1}}, "note": "基于10个样本的正向意图识别分析，其中4个正向类别，10个有效预测", "methodology": "根据sunfa.md文档，将'none'视为负例，其他类别视为正例，重点评估正向意图识别能力"}}, "doubao-1.6-seed": {"total_requests": 10, "successful_requests": 10, "success_rate": 1.0, "average_execution_time": 0.0, "result_distribution": {"none": 6, "看车型": 2, "问优惠": 1, "问地址": 1}, "classification_metrics": {"accuracy": 0.8, "accuracy_warning": "准确率可能受类别不平衡影响，建议重点关注F1、Precision、Recall指标", "precision": 1.0, "recall": 0.6666666666666666, "f1_score": 0.8, "total_samples": 10, "valid_predictions": 10, "positive_classes": ["撩妹", "看车型", "问优惠", "问地址"], "none_class": "none", "total_positive_samples": 6, "overall_metrics": {"tp": 4, "fp": 0, "fn": 2, "tn": 4}, "macro_avg": {"precision": 0.75, "recall": 0.75, "f1_score": 0.75}, "weighted_avg": {"precision": 0.6666666666666666, "recall": 0.6666666666666666, "f1_score": 0.6666666666666666}, "per_class_metrics": {"none": {"precision": 0.6666666666666666, "recall": 1.0, "f1_score": 0.8, "support": 4, "tp": 4, "fp": 2, "fn": 0, "is_positive_class": false}, "撩妹": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "support": 2, "tp": 0, "fp": 0, "fn": 2, "is_positive_class": true}, "看车型": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 2, "tp": 2, "fp": 0, "fn": 0, "is_positive_class": true}, "问优惠": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 1, "tp": 1, "fp": 0, "fn": 0, "is_positive_class": true}, "问地址": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 1, "tp": 1, "fp": 0, "fn": 0, "is_positive_class": true}}, "confusion_matrix": {"none": {"none": 4, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 0}, "撩妹": {"none": 2, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 0}, "看车型": {"none": 0, "撩妹": 0, "看车型": 2, "问优惠": 0, "问地址": 0}, "问优惠": {"none": 0, "撩妹": 0, "看车型": 0, "问优惠": 1, "问地址": 0}, "问地址": {"none": 0, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 1}}, "note": "基于10个样本的正向意图识别分析，其中4个正向类别，10个有效预测", "methodology": "根据sunfa.md文档，将'none'视为负例，其他类别视为正例，重点评估正向意图识别能力"}}, "doubao1.5 pro": {"total_requests": 10, "successful_requests": 10, "success_rate": 1.0, "average_execution_time": 0.0, "result_distribution": {"none": 7, "看车型": 1, "问优惠": 1, "问地址": 1}, "classification_metrics": {"accuracy": 0.7, "accuracy_warning": "准确率可能受类别不平衡影响，建议重点关注F1、Precision、Recall指标", "precision": 1.0, "recall": 0.5, "f1_score": 0.6666666666666666, "total_samples": 10, "valid_predictions": 10, "positive_classes": ["撩妹", "看车型", "问优惠", "问地址"], "none_class": "none", "total_positive_samples": 6, "overall_metrics": {"tp": 3, "fp": 0, "fn": 3, "tn": 4}, "macro_avg": {"precision": 0.75, "recall": 0.625, "f1_score": 0.6666666666666666}, "weighted_avg": {"precision": 0.6666666666666666, "recall": 0.5, "f1_score": 0.5555555555555555}, "per_class_metrics": {"none": {"precision": 0.5714285714285714, "recall": 1.0, "f1_score": 0.7272727272727273, "support": 4, "tp": 4, "fp": 3, "fn": 0, "is_positive_class": false}, "撩妹": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "support": 2, "tp": 0, "fp": 0, "fn": 2, "is_positive_class": true}, "看车型": {"precision": 1.0, "recall": 0.5, "f1_score": 0.6666666666666666, "support": 2, "tp": 1, "fp": 0, "fn": 1, "is_positive_class": true}, "问优惠": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 1, "tp": 1, "fp": 0, "fn": 0, "is_positive_class": true}, "问地址": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 1, "tp": 1, "fp": 0, "fn": 0, "is_positive_class": true}}, "confusion_matrix": {"none": {"none": 4, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 0}, "撩妹": {"none": 2, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 0}, "看车型": {"none": 1, "撩妹": 0, "看车型": 1, "问优惠": 0, "问地址": 0}, "问优惠": {"none": 0, "撩妹": 0, "看车型": 0, "问优惠": 1, "问地址": 0}, "问地址": {"none": 0, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 1}}, "note": "基于10个样本的正向意图识别分析，其中4个正向类别，10个有效预测", "methodology": "根据sunfa.md文档，将'none'视为负例，其他类别视为正例，重点评估正向意图识别能力"}}, "qwen-plus": {"total_requests": 10, "successful_requests": 10, "success_rate": 1.0, "average_execution_time": 0.0, "result_distribution": {"none": 7, "看车型": 1, "问优惠": 1, "问地址": 1}, "classification_metrics": {"accuracy": 0.7, "accuracy_warning": "准确率可能受类别不平衡影响，建议重点关注F1、Precision、Recall指标", "precision": 1.0, "recall": 0.5, "f1_score": 0.6666666666666666, "total_samples": 10, "valid_predictions": 10, "positive_classes": ["撩妹", "看车型", "问优惠", "问地址"], "none_class": "none", "total_positive_samples": 6, "overall_metrics": {"tp": 3, "fp": 0, "fn": 3, "tn": 4}, "macro_avg": {"precision": 0.75, "recall": 0.625, "f1_score": 0.6666666666666666}, "weighted_avg": {"precision": 0.6666666666666666, "recall": 0.5, "f1_score": 0.5555555555555555}, "per_class_metrics": {"none": {"precision": 0.5714285714285714, "recall": 1.0, "f1_score": 0.7272727272727273, "support": 4, "tp": 4, "fp": 3, "fn": 0, "is_positive_class": false}, "撩妹": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "support": 2, "tp": 0, "fp": 0, "fn": 2, "is_positive_class": true}, "看车型": {"precision": 1.0, "recall": 0.5, "f1_score": 0.6666666666666666, "support": 2, "tp": 1, "fp": 0, "fn": 1, "is_positive_class": true}, "问优惠": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 1, "tp": 1, "fp": 0, "fn": 0, "is_positive_class": true}, "问地址": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 1, "tp": 1, "fp": 0, "fn": 0, "is_positive_class": true}}, "confusion_matrix": {"none": {"none": 4, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 0}, "撩妹": {"none": 2, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 0}, "看车型": {"none": 1, "撩妹": 0, "看车型": 1, "问优惠": 0, "问地址": 0}, "问优惠": {"none": 0, "撩妹": 0, "看车型": 0, "问优惠": 1, "问地址": 0}, "问地址": {"none": 0, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 1}}, "note": "基于10个样本的正向意图识别分析，其中4个正向类别，10个有效预测", "methodology": "根据sunfa.md文档，将'none'视为负例，其他类别视为正例，重点评估正向意图识别能力"}}, "通义千问3-235B-A22B": {"total_requests": 10, "successful_requests": 10, "success_rate": 1.0, "average_execution_time": 0.0, "result_distribution": {"none": 6, "看车型": 2, "问优惠": 1, "问地址": 1}, "classification_metrics": {"accuracy": 0.8, "accuracy_warning": "准确率可能受类别不平衡影响，建议重点关注F1、Precision、Recall指标", "precision": 1.0, "recall": 0.6666666666666666, "f1_score": 0.8, "total_samples": 10, "valid_predictions": 10, "positive_classes": ["撩妹", "看车型", "问优惠", "问地址"], "none_class": "none", "total_positive_samples": 6, "overall_metrics": {"tp": 4, "fp": 0, "fn": 2, "tn": 4}, "macro_avg": {"precision": 0.75, "recall": 0.75, "f1_score": 0.75}, "weighted_avg": {"precision": 0.6666666666666666, "recall": 0.6666666666666666, "f1_score": 0.6666666666666666}, "per_class_metrics": {"none": {"precision": 0.6666666666666666, "recall": 1.0, "f1_score": 0.8, "support": 4, "tp": 4, "fp": 2, "fn": 0, "is_positive_class": false}, "撩妹": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "support": 2, "tp": 0, "fp": 0, "fn": 2, "is_positive_class": true}, "看车型": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 2, "tp": 2, "fp": 0, "fn": 0, "is_positive_class": true}, "问优惠": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 1, "tp": 1, "fp": 0, "fn": 0, "is_positive_class": true}, "问地址": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 1, "tp": 1, "fp": 0, "fn": 0, "is_positive_class": true}}, "confusion_matrix": {"none": {"none": 4, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 0}, "撩妹": {"none": 2, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 0}, "看车型": {"none": 0, "撩妹": 0, "看车型": 2, "问优惠": 0, "问地址": 0}, "问优惠": {"none": 0, "撩妹": 0, "看车型": 0, "问优惠": 1, "问地址": 0}, "问地址": {"none": 0, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 1}}, "note": "基于10个样本的正向意图识别分析，其中4个正向类别，10个有效预测", "methodology": "根据sunfa.md文档，将'none'视为负例，其他类别视为正例，重点评估正向意图识别能力"}}}}