{"file_info": {"total_rows": 10, "columns": ["live_comment", "expected_result", "ERNIE 4.5 Turbo_result", "deepseek-v3_result", "doubao-1.6-seed_result", "doubao1.5 pro_result", "qwen-plus_result", "通义千问3-235B-A22B_result"]}, "model_performance": {"ERNIE 4.5 Turbo": {"total_requests": 10, "successful_requests": 10, "success_rate": 1.0, "average_execution_time": 0.0, "result_distribution": {"none": 7, "看车型": 1, "问优惠": 1, "问地址": 1}, "classification_metrics": {"accuracy": 0.7, "total_samples": 10, "valid_predictions": 10, "macro_avg": {"precision": 0.7142857142857142, "recall": 0.7, "f1_score": 0.6787878787878788}, "weighted_avg": {"precision": 0.6285714285714286, "recall": 0.7, "f1_score": 0.6242424242424243}, "per_class_metrics": {"none": {"precision": 0.5714285714285714, "recall": 1.0, "f1_score": 0.7272727272727273, "support": 4, "tp": 4, "fp": 3, "fn": 0}, "撩妹": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "support": 2, "tp": 0, "fp": 0, "fn": 2}, "看车型": {"precision": 1.0, "recall": 0.5, "f1_score": 0.6666666666666666, "support": 2, "tp": 1, "fp": 0, "fn": 1}, "问优惠": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 1, "tp": 1, "fp": 0, "fn": 0}, "问地址": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 1, "tp": 1, "fp": 0, "fn": 0}}, "confusion_matrix": {"none": {"none": 4, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 0}, "撩妹": {"none": 2, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 0}, "看车型": {"none": 1, "撩妹": 0, "看车型": 1, "问优惠": 0, "问地址": 0}, "问优惠": {"none": 0, "撩妹": 0, "看车型": 0, "问优惠": 1, "问地址": 0}, "问地址": {"none": 0, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 1}}, "note": "分类报告基于10个样本，其中10个有效预测"}}, "deepseek-v3": {"total_requests": 10, "successful_requests": 10, "success_rate": 1.0, "average_execution_time": 0.0, "result_distribution": {"none": 5, "撩妹": 2, "看车型": 1, "问优惠": 1, "问地址": 1}, "classification_metrics": {"accuracy": 0.9, "total_samples": 10, "valid_predictions": 10, "macro_avg": {"precision": 0.96, "recall": 0.9, "f1_score": 0.9111111111111111}, "weighted_avg": {"precision": 0.9199999999999999, "recall": 0.9, "f1_score": 0.888888888888889}, "per_class_metrics": {"none": {"precision": 0.8, "recall": 1.0, "f1_score": 0.888888888888889, "support": 4, "tp": 4, "fp": 1, "fn": 0}, "撩妹": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 2, "tp": 2, "fp": 0, "fn": 0}, "看车型": {"precision": 1.0, "recall": 0.5, "f1_score": 0.6666666666666666, "support": 2, "tp": 1, "fp": 0, "fn": 1}, "问优惠": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 1, "tp": 1, "fp": 0, "fn": 0}, "问地址": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 1, "tp": 1, "fp": 0, "fn": 0}}, "confusion_matrix": {"none": {"none": 4, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 0}, "撩妹": {"none": 0, "撩妹": 2, "看车型": 0, "问优惠": 0, "问地址": 0}, "看车型": {"none": 1, "撩妹": 0, "看车型": 1, "问优惠": 0, "问地址": 0}, "问优惠": {"none": 0, "撩妹": 0, "看车型": 0, "问优惠": 1, "问地址": 0}, "问地址": {"none": 0, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 1}}, "note": "分类报告基于10个样本，其中10个有效预测"}}, "doubao-1.6-seed": {"total_requests": 10, "successful_requests": 10, "success_rate": 1.0, "average_execution_time": 0.0, "result_distribution": {"none": 5, "撩妹": 2, "看车型": 1, "问优惠": 1, "问地址": 1}, "classification_metrics": {"accuracy": 0.9, "total_samples": 10, "valid_predictions": 10, "macro_avg": {"precision": 0.96, "recall": 0.9, "f1_score": 0.9111111111111111}, "weighted_avg": {"precision": 0.9199999999999999, "recall": 0.9, "f1_score": 0.888888888888889}, "per_class_metrics": {"none": {"precision": 0.8, "recall": 1.0, "f1_score": 0.888888888888889, "support": 4, "tp": 4, "fp": 1, "fn": 0}, "撩妹": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 2, "tp": 2, "fp": 0, "fn": 0}, "看车型": {"precision": 1.0, "recall": 0.5, "f1_score": 0.6666666666666666, "support": 2, "tp": 1, "fp": 0, "fn": 1}, "问优惠": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 1, "tp": 1, "fp": 0, "fn": 0}, "问地址": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 1, "tp": 1, "fp": 0, "fn": 0}}, "confusion_matrix": {"none": {"none": 4, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 0}, "撩妹": {"none": 0, "撩妹": 2, "看车型": 0, "问优惠": 0, "问地址": 0}, "看车型": {"none": 1, "撩妹": 0, "看车型": 1, "问优惠": 0, "问地址": 0}, "问优惠": {"none": 0, "撩妹": 0, "看车型": 0, "问优惠": 1, "问地址": 0}, "问地址": {"none": 0, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 1}}, "note": "分类报告基于10个样本，其中10个有效预测"}}, "doubao1.5 pro": {"total_requests": 10, "successful_requests": 10, "success_rate": 1.0, "average_execution_time": 0.0, "result_distribution": {"none": 7, "看车型": 1, "问优惠": 1, "问地址": 1}, "classification_metrics": {"accuracy": 0.7, "total_samples": 10, "valid_predictions": 10, "macro_avg": {"precision": 0.7142857142857142, "recall": 0.7, "f1_score": 0.6787878787878788}, "weighted_avg": {"precision": 0.6285714285714286, "recall": 0.7, "f1_score": 0.6242424242424243}, "per_class_metrics": {"none": {"precision": 0.5714285714285714, "recall": 1.0, "f1_score": 0.7272727272727273, "support": 4, "tp": 4, "fp": 3, "fn": 0}, "撩妹": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "support": 2, "tp": 0, "fp": 0, "fn": 2}, "看车型": {"precision": 1.0, "recall": 0.5, "f1_score": 0.6666666666666666, "support": 2, "tp": 1, "fp": 0, "fn": 1}, "问优惠": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 1, "tp": 1, "fp": 0, "fn": 0}, "问地址": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 1, "tp": 1, "fp": 0, "fn": 0}}, "confusion_matrix": {"none": {"none": 4, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 0}, "撩妹": {"none": 2, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 0}, "看车型": {"none": 1, "撩妹": 0, "看车型": 1, "问优惠": 0, "问地址": 0}, "问优惠": {"none": 0, "撩妹": 0, "看车型": 0, "问优惠": 1, "问地址": 0}, "问地址": {"none": 0, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 1}}, "note": "分类报告基于10个样本，其中10个有效预测"}}, "qwen-plus": {"total_requests": 10, "successful_requests": 10, "success_rate": 1.0, "average_execution_time": 0.0, "result_distribution": {"none": 7, "看车型": 1, "问优惠": 1, "问地址": 1}, "classification_metrics": {"accuracy": 0.7, "total_samples": 10, "valid_predictions": 10, "macro_avg": {"precision": 0.7142857142857142, "recall": 0.7, "f1_score": 0.6787878787878788}, "weighted_avg": {"precision": 0.6285714285714286, "recall": 0.7, "f1_score": 0.6242424242424243}, "per_class_metrics": {"none": {"precision": 0.5714285714285714, "recall": 1.0, "f1_score": 0.7272727272727273, "support": 4, "tp": 4, "fp": 3, "fn": 0}, "撩妹": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "support": 2, "tp": 0, "fp": 0, "fn": 2}, "看车型": {"precision": 1.0, "recall": 0.5, "f1_score": 0.6666666666666666, "support": 2, "tp": 1, "fp": 0, "fn": 1}, "问优惠": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 1, "tp": 1, "fp": 0, "fn": 0}, "问地址": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 1, "tp": 1, "fp": 0, "fn": 0}}, "confusion_matrix": {"none": {"none": 4, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 0}, "撩妹": {"none": 2, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 0}, "看车型": {"none": 1, "撩妹": 0, "看车型": 1, "问优惠": 0, "问地址": 0}, "问优惠": {"none": 0, "撩妹": 0, "看车型": 0, "问优惠": 1, "问地址": 0}, "问地址": {"none": 0, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 1}}, "note": "分类报告基于10个样本，其中10个有效预测"}}, "通义千问3-235B-A22B": {"total_requests": 10, "successful_requests": 10, "success_rate": 1.0, "average_execution_time": 0.0, "result_distribution": {"none": 6, "看车型": 2, "问优惠": 1, "问地址": 1}, "classification_metrics": {"accuracy": 0.8, "total_samples": 10, "valid_predictions": 10, "macro_avg": {"precision": 0.7333333333333333, "recall": 0.8, "f1_score": 0.76}, "weighted_avg": {"precision": 0.6666666666666666, "recall": 0.8, "f1_score": 0.72}, "per_class_metrics": {"none": {"precision": 0.6666666666666666, "recall": 1.0, "f1_score": 0.8, "support": 4, "tp": 4, "fp": 2, "fn": 0}, "撩妹": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "support": 2, "tp": 0, "fp": 0, "fn": 2}, "看车型": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 2, "tp": 2, "fp": 0, "fn": 0}, "问优惠": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 1, "tp": 1, "fp": 0, "fn": 0}, "问地址": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 1, "tp": 1, "fp": 0, "fn": 0}}, "confusion_matrix": {"none": {"none": 4, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 0}, "撩妹": {"none": 2, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 0}, "看车型": {"none": 0, "撩妹": 0, "看车型": 2, "问优惠": 0, "问地址": 0}, "问优惠": {"none": 0, "撩妹": 0, "看车型": 0, "问优惠": 1, "问地址": 0}, "问地址": {"none": 0, "撩妹": 0, "看车型": 0, "问优惠": 0, "问地址": 1}}, "note": "分类报告基于10个样本，其中10个有效预测"}}}}