# 优化版AI模型评估配置文件
# 包含所有性能优化选项和最佳实践配置

# 模型提供商配置
providers:
  openai:
    api_key: "${OPENAI_API_KEY}"
    base_url: "https://api.openai.com/v1"
    timeout: 60
    max_retries: 3
    retry_delay: 1.0
    # 性能优化配置
    max_concurrent: 100
    connection_pool_size: 200
    connection_pool_maxsize: 200
    keepalive_timeout: 30
    enable_http2: true
    enable_compression: true
    
  zhipu:
    api_key: "${ZHIPU_API_KEY}"
    base_url: "https://open.bigmodel.cn/api/paas/v4"
    timeout: 60
    max_retries: 3
    retry_delay: 1.0
    # 性能优化配置
    max_concurrent: 80
    connection_pool_size: 160
    connection_pool_maxsize: 160
    keepalive_timeout: 30
    enable_http2: true
    enable_compression: true
    
  anthropic:
    api_key: "${ANTHROPIC_API_KEY}"
    base_url: "https://api.anthropic.com"
    timeout: 60
    max_retries: 3
    retry_delay: 1.0
    # 性能优化配置
    max_concurrent: 60
    connection_pool_size: 120
    connection_pool_maxsize: 120
    keepalive_timeout: 30
    enable_http2: true
    enable_compression: true
    
  qwen:
    api_key: "${QWEN_API_KEY}"
    base_url: "https://dashscope.aliyuncs.com/api/v1"
    timeout: 60
    max_retries: 3
    retry_delay: 1.0
    # 性能优化配置
    max_concurrent: 50
    connection_pool_size: 100
    connection_pool_maxsize: 100
    keepalive_timeout: 30
    enable_http2: true
    enable_compression: true

# 模型配置
models:
  # OpenAI模型
  gpt-4o:
    provider: openai
    model_name: "gpt-4o"
    max_tokens: 4096
    temperature: 0.1
    top_p: 0.9
    frequency_penalty: 0.0
    presence_penalty: 0.0
    # 性能优化
    max_concurrent: 50
    priority_weight: 1.0
    
  gpt-4o-mini:
    provider: openai
    model_name: "gpt-4o-mini"
    max_tokens: 4096
    temperature: 0.1
    top_p: 0.9
    frequency_penalty: 0.0
    presence_penalty: 0.0
    # 性能优化
    max_concurrent: 80
    priority_weight: 0.8
    
  gpt-3.5-turbo:
    provider: openai
    model_name: "gpt-3.5-turbo"
    max_tokens: 4096
    temperature: 0.1
    top_p: 0.9
    frequency_penalty: 0.0
    presence_penalty: 0.0
    # 性能优化
    max_concurrent: 100
    priority_weight: 0.6
    
  # 智谱模型
  glm-4-plus:
    provider: zhipu
    model_name: "glm-4-plus"
    max_tokens: 4096
    temperature: 0.1
    top_p: 0.9
    # 性能优化
    max_concurrent: 40
    priority_weight: 1.0
    
  glm-4-0520:
    provider: zhipu
    model_name: "glm-4-0520"
    max_tokens: 4096
    temperature: 0.1
    top_p: 0.9
    # 性能优化
    max_concurrent: 60
    priority_weight: 0.9
    
  glm-4-air:
    provider: zhipu
    model_name: "glm-4-air"
    max_tokens: 4096
    temperature: 0.1
    top_p: 0.9
    # 性能优化
    max_concurrent: 80
    priority_weight: 0.7
    
  # Anthropic模型
  claude-3-5-sonnet:
    provider: anthropic
    model_name: "claude-3-5-sonnet-20241022"
    max_tokens: 4096
    temperature: 0.1
    top_p: 0.9
    # 性能优化
    max_concurrent: 30
    priority_weight: 1.0
    
  claude-3-haiku:
    provider: anthropic
    model_name: "claude-3-haiku-20240307"
    max_tokens: 4096
    temperature: 0.1
    top_p: 0.9
    # 性能优化
    max_concurrent: 60
    priority_weight: 0.8
    
  # 通义千问模型
  qwen-max:
    provider: qwen
    model_name: "qwen-max"
    max_tokens: 4096
    temperature: 0.1
    top_p: 0.9
    # 性能优化
    max_concurrent: 25
    priority_weight: 1.0
    
  qwen-plus:
    provider: qwen
    model_name: "qwen-plus"
    max_tokens: 4096
    temperature: 0.1
    top_p: 0.9
    # 性能优化
    max_concurrent: 40
    priority_weight: 0.9
    
  qwen-turbo:
    provider: qwen
    model_name: "qwen-turbo"
    max_tokens: 4096
    temperature: 0.1
    top_p: 0.9
    # 性能优化
    max_concurrent: 50
    priority_weight: 0.7

# 提示词模板配置
prompt_templates:
  # 引用外部模板文件
  intent_recognition:
    template_file: "intent_templates.yaml"
    
  # 内联简单模板
  simple_qa:
    template: |
      请回答以下问题：
      
      问题：{question}
      
      要求：
      1. 回答要准确、简洁
      2. 如果不确定，请说明
      3. 用中文回答
      
      回答：
    variables:
      - question
    
  text_classification:
    template: |
      请对以下文本进行分类：
      
      文本：{text}
      
      分类选项：{categories}
      
      要求：
      1. 只返回最合适的分类
      2. 不要解释原因
      3. 如果无法分类，返回"未知"
      
      分类结果：
    variables:
      - text
      - categories
    
  sentiment_analysis:
    template: |
      请分析以下文本的情感倾向：
      
      文本：{text}
      
      请从以下选项中选择：
      - 正面
      - 负面
      - 中性
      
      只返回情感倾向，不要解释：
    variables:
      - text

# 全局性能配置
performance:
  # 全局并发控制
  global_max_concurrent: 300
  global_timeout: 120
  
  # 连接池配置
  connection_pool:
    total_pool_size: 500
    per_host_pool_size: 100
    pool_timeout: 30
    keepalive_timeout: 60
    
  # DNS和网络优化
  network:
    dns_cache_ttl: 300
    enable_connection_prewarming: true
    prewarming_connections: 10
    tcp_keepalive: true
    tcp_nodelay: true
    
  # HTTP优化
  http:
    enable_http2: true
    enable_compression: true
    compression_threshold: 1024
    max_redirects: 3
    
  # 重试和错误处理
  retry:
    max_retries: 5
    base_delay: 1.0
    max_delay: 60.0
    exponential_base: 2.0
    jitter: true
    retry_on_timeout: true
    retry_on_connection_error: true
    
  # 批处理优化
  batch:
    default_strategy: "hybrid"
    min_batch_size: 10
    max_batch_size: 100
    batch_timeout: 5.0
    memory_threshold_mb: 2000
    enable_request_deduplication: true
    enable_request_compression: true
    
  # 内存优化
  memory:
    max_memory_usage_mb: 4000
    gc_threshold: 0.8
    enable_memory_monitoring: true
    memory_check_interval: 30
    
  # 缓存配置
  cache:
    enable_request_cache: true
    cache_ttl: 3600
    max_cache_size: 10000
    cache_compression: true
    cache_cleanup_interval: 300
    
  # 监控和指标
  monitoring:
    enable_detailed_metrics: true
    metrics_interval: 5
    enable_system_monitoring: true
    system_monitor_interval: 1
    metrics_retention_hours: 24
    
  # 日志配置
  logging:
    level: "INFO"
    enable_request_logging: false
    enable_response_logging: false
    enable_performance_logging: true
    log_slow_requests: true
    slow_request_threshold: 5.0

# 提供商特定性能配置
provider_performance:
  openai:
    max_concurrent: 100
    request_rate_limit: 3000  # 每分钟
    token_rate_limit: 1000000  # 每分钟
    adaptive_rate_limiting: true
    burst_allowance: 1.5
    
  zhipu:
    max_concurrent: 80
    request_rate_limit: 2000
    token_rate_limit: 500000
    adaptive_rate_limiting: true
    burst_allowance: 1.3
    
  anthropic:
    max_concurrent: 60
    request_rate_limit: 1000
    token_rate_limit: 300000
    adaptive_rate_limiting: true
    burst_allowance: 1.2
    
  qwen:
    max_concurrent: 50
    request_rate_limit: 1500
    token_rate_limit: 400000
    adaptive_rate_limiting: true
    burst_allowance: 1.2

# 场景化配置
scenarios:
  # 高吞吐量场景
  high_throughput:
    global_max_concurrent: 500
    batch_strategy: "adaptive"
    min_batch_size: 20
    max_batch_size: 200
    enable_aggressive_caching: true
    memory_threshold_mb: 6000
    
  # 低延迟场景
  low_latency:
    global_max_concurrent: 100
    batch_strategy: "fixed"
    min_batch_size: 1
    max_batch_size: 10
    enable_request_cache: false
    connection_prewarming: 20
    
  # 资源受限场景
  resource_constrained:
    global_max_concurrent: 50
    batch_strategy: "memory_aware"
    min_batch_size: 5
    max_batch_size: 20
    memory_threshold_mb: 1000
    enable_compression: true
    
  # 开发测试场景
  development:
    global_max_concurrent: 10
    batch_strategy: "fixed"
    min_batch_size: 1
    max_batch_size: 5
    enable_detailed_logging: true
    enable_request_logging: true

# 实验性功能
experimental:
  # HTTP/3支持
  enable_http3: false
  
  # 请求管道化
  enable_request_pipelining: true
  pipeline_max_requests: 10
  
  # 智能重试
  enable_smart_retry: true
  retry_learning_rate: 0.1
  
  # 动态并发调整
  enable_dynamic_concurrency: true
  concurrency_adjustment_interval: 30
  target_response_time: 2.0
  
  # 预测性缓存
  enable_predictive_caching: true
  cache_prediction_model: "simple"
  
  # 自适应批处理
  enable_adaptive_batching: true
  batching_learning_rate: 0.05
  
  # 智能负载均衡
  enable_smart_load_balancing: true
  load_balancing_algorithm: "weighted_round_robin"
  
  # 异步文件IO
  enable_async_file_io: true
  file_io_buffer_size: 65536
  
  # 内存映射文件
  enable_memory_mapped_files: false
  mmap_threshold_mb: 100

# 默认配置
defaults:
  template_name: "intent_recognition"
  output_format: "csv"
  enable_progress_bar: true
  save_intermediate_results: false
  
# 安全配置
security:
  # API密钥加密
  encrypt_api_keys: false
  encryption_key: "${ENCRYPTION_KEY}"
  
  # 请求签名
  enable_request_signing: false
  signing_algorithm: "hmac-sha256"
  
  # 速率限制
  enable_rate_limiting: true
  rate_limit_strategy: "token_bucket"
  
  # 请求验证
  validate_requests: true
  max_request_size_mb: 10
  
  # 响应过滤
  filter_sensitive_data: true
  sensitive_patterns:
    - "password"
    - "secret"
    - "token"
    - "key"

# 调试配置
debug:
  enable_debug_mode: false
  debug_log_level: "DEBUG"
  save_debug_info: false
  debug_output_dir: "debug"
  enable_profiling: false
  profiling_output_file: "profile.prof"