#!/usr/bin/env python3
"""AI模型评估系统演示脚本

这个脚本提供了一个简化的演示，展示系统的核心功能：
- 批量模型评估
- 性能监控
- 结果分析
- 优化效果对比
"""

import asyncio
import json
import time
import random
from pathlib import Path
from typing import List, Dict, Any
from dataclasses import dataclass, asdict
from loguru import logger
import pandas as pd


@dataclass
class DemoConfig:
    """演示配置"""
    num_samples: int = 20
    models: List[str] = None
    enable_optimization: bool = True
    show_progress: bool = True
    save_results: bool = True
    output_dir: str = "demo_results"
    
    def __post_init__(self):
        if self.models is None:
            self.models = ['gpt-3.5-turbo', 'gpt-4o-mini', 'glm-4-flash']


class DemoDataGenerator:
    """演示数据生成器"""
    
    def __init__(self):
        self.question_templates = [
            "什么是{topic}？",
            "请解释{topic}的概念。",
            "如何理解{topic}？",
            "{topic}有什么特点？",
            "请简述{topic}的重要性。"
        ]
        
        self.topics = [
            "人工智能", "机器学习", "深度学习", "自然语言处理", "计算机视觉",
            "数据科学", "云计算", "区块链", "物联网", "大数据",
            "网络安全", "软件工程", "算法设计", "数据结构", "操作系统"
        ]
        
        self.text_samples = [
            "这个产品真的很棒，我非常满意！",
            "服务质量一般，有待改进。",
            "价格合理，性价比不错。",
            "客服态度很好，解决问题及时。",
            "产品质量有问题，不推荐购买。",
            "物流速度很快，包装也很好。",
            "功能强大，操作简单易懂。",
            "界面设计美观，用户体验佳。",
            "性能稳定，运行流畅。",
            "文档详细，学习成本低。"
        ]
        
        self.categories = ["正面", "负面", "中性"]
    
    def generate_qa_samples(self, num_samples: int) -> List[Dict[str, Any]]:
        """生成问答样本"""
        samples = []
        
        for i in range(num_samples):
            topic = random.choice(self.topics)
            template = random.choice(self.question_templates)
            question = template.format(topic=topic)
            
            sample = {
                'id': f'qa_{i+1}',
                'type': 'simple_qa',
                'data': {
                    'question': question
                },
                'expected_keywords': [topic, '定义', '概念', '特点'],
                'metadata': {
                    'topic': topic,
                    'difficulty': random.choice(['easy', 'medium', 'hard']),
                    'category': 'knowledge_qa'
                }
            }
            samples.append(sample)
        
        return samples
    
    def generate_classification_samples(self, num_samples: int) -> List[Dict[str, Any]]:
        """生成分类样本"""
        samples = []
        
        for i in range(num_samples):
            text = random.choice(self.text_samples)
            
            sample = {
                'id': f'cls_{i+1}',
                'type': 'text_classification',
                'data': {
                    'text': text,
                    'categories': ', '.join(self.categories)
                },
                'expected_category': self._get_expected_sentiment(text),
                'metadata': {
                    'domain': 'product_review',
                    'language': 'chinese',
                    'length': len(text)
                }
            }
            samples.append(sample)
        
        return samples
    
    def generate_sentiment_samples(self, num_samples: int) -> List[Dict[str, Any]]:
        """生成情感分析样本"""
        samples = []
        
        for i in range(num_samples):
            text = random.choice(self.text_samples)
            
            sample = {
                'id': f'sent_{i+1}',
                'type': 'sentiment_analysis',
                'data': {
                    'text': text
                },
                'expected_sentiment': self._get_expected_sentiment(text),
                'metadata': {
                    'domain': 'product_review',
                    'confidence_threshold': 0.7
                }
            }
            samples.append(sample)
        
        return samples
    
    def _get_expected_sentiment(self, text: str) -> str:
        """获取预期情感（简单规则）"""
        positive_words = ['棒', '满意', '好', '快', '美观', '强大', '稳定', '流畅', '详细']
        negative_words = ['问题', '一般', '有待改进', '不推荐']
        
        positive_count = sum(1 for word in positive_words if word in text)
        negative_count = sum(1 for word in negative_words if word in text)
        
        if positive_count > negative_count:
            return '正面'
        elif negative_count > positive_count:
            return '负面'
        else:
            return '中性'
    
    def generate_mixed_samples(self, num_samples: int) -> List[Dict[str, Any]]:
        """生成混合样本"""
        samples = []
        
        # 分配样本类型
        qa_count = num_samples // 3
        cls_count = num_samples // 3
        sent_count = num_samples - qa_count - cls_count
        
        samples.extend(self.generate_qa_samples(qa_count))
        samples.extend(self.generate_classification_samples(cls_count))
        samples.extend(self.generate_sentiment_samples(sent_count))
        
        # 随机打乱
        random.shuffle(samples)
        
        return samples


class MockOptimizedEvaluator:
    """模拟优化评估器（用于演示）"""
    
    def __init__(self, enable_optimization: bool = True):
        self.enable_optimization = enable_optimization
        self.metrics = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'total_time': 0,
            'avg_response_time': 0,
            'throughput': 0,
            'cache_hits': 0,
            'cache_misses': 0
        }
        
        # 模拟优化效果
        if enable_optimization:
            self.base_latency = 0.5  # 优化后延迟更低
            self.error_rate = 0.02   # 优化后错误率更低
            self.cache_hit_rate = 0.3  # 优化后缓存命中率更高
        else:
            self.base_latency = 1.2  # 未优化延迟较高
            self.error_rate = 0.08   # 未优化错误率较高
            self.cache_hit_rate = 0.1  # 未优化缓存命中率较低
    
    async def evaluate_batch(self, samples: List[Dict[str, Any]], models: List[str]) -> Dict[str, Any]:
        """批量评估（模拟）"""
        start_time = time.time()
        results = []
        
        total_requests = len(samples) * len(models)
        self.metrics['total_requests'] += total_requests
        
        for i, sample in enumerate(samples):
            if i % 5 == 0:  # 每5个样本显示一次进度
                progress = (i + 1) / len(samples) * 100
                logger.info(f"📊 评估进度: {progress:.1f}% ({i+1}/{len(samples)})")
            
            sample_results = []
            
            for model in models:
                # 模拟API调用延迟
                latency = self.base_latency + random.uniform(-0.2, 0.3)
                await asyncio.sleep(latency / 10)  # 加速演示
                
                # 模拟缓存命中
                is_cache_hit = random.random() < self.cache_hit_rate
                if is_cache_hit:
                    self.metrics['cache_hits'] += 1
                    latency *= 0.1  # 缓存命中延迟很低
                else:
                    self.metrics['cache_misses'] += 1
                
                # 模拟错误
                is_error = random.random() < self.error_rate
                
                if is_error:
                    self.metrics['failed_requests'] += 1
                    result = {
                        'model': model,
                        'status': 'error',
                        'error': 'API调用失败',
                        'latency': latency,
                        'cached': is_cache_hit
                    }
                else:
                    self.metrics['successful_requests'] += 1
                    
                    # 生成模拟响应
                    response = self._generate_mock_response(sample, model)
                    
                    result = {
                        'model': model,
                        'status': 'success',
                        'response': response,
                        'latency': latency,
                        'cached': is_cache_hit,
                        'tokens_used': random.randint(50, 200)
                    }
                
                sample_results.append(result)
                self.metrics['total_time'] += latency
            
            results.append({
                'sample_id': sample['id'],
                'sample_type': sample['type'],
                'results': sample_results
            })
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # 更新指标
        self.metrics['avg_response_time'] = self.metrics['total_time'] / max(self.metrics['total_requests'], 1)
        self.metrics['throughput'] = self.metrics['total_requests'] / total_time
        
        return {
            'results': results,
            'metrics': self.metrics.copy(),
            'total_time': total_time,
            'optimization_enabled': self.enable_optimization
        }
    
    def _generate_mock_response(self, sample: Dict[str, Any], model: str) -> str:
        """生成模拟响应"""
        sample_type = sample['type']
        
        if sample_type == 'simple_qa':
            question = sample['data']['question']
            if '什么是' in question:
                topic = question.replace('什么是', '').replace('？', '').strip()
                return f"{topic}是一个重要的技术概念，具有广泛的应用前景。"
            else:
                return "这是一个很好的问题，需要从多个角度来理解。"
        
        elif sample_type == 'text_classification':
            text = sample['data']['text']
            if any(word in text for word in ['棒', '满意', '好', '快']):
                return '正面'
            elif any(word in text for word in ['问题', '不推荐']):
                return '负面'
            else:
                return '中性'
        
        elif sample_type == 'sentiment_analysis':
            text = sample['data']['text']
            if any(word in text for word in ['棒', '满意', '好', '快']):
                return '正面'
            elif any(word in text for word in ['问题', '不推荐']):
                return '负面'
            else:
                return '中性'
        
        return "模拟响应"


class DemoRunner:
    """演示运行器"""
    
    def __init__(self, config: DemoConfig):
        self.config = config
        self.data_generator = DemoDataGenerator()
        self.output_dir = Path(config.output_dir)
        self.output_dir.mkdir(exist_ok=True)
    
    def print_banner(self):
        """打印演示横幅"""
        banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                    🎯 AI模型评估系统 - 功能演示                              ║
║                                                                              ║
║  本演示将展示系统的核心功能：                                                ║
║  • 📊 批量模型评估                                                          ║
║  • ⚡ 性能优化效果                                                          ║
║  • 📈 实时监控指标                                                          ║
║  • 📋 结果分析报告                                                          ║
╚══════════════════════════════════════════════════════════════════════════════╝
"""
        print(banner)
    
    async def run_demo(self):
        """运行演示"""
        self.print_banner()
        
        logger.info("🚀 开始AI模型评估系统演示")
        logger.info(f"📋 演示配置: {self.config.num_samples}个样本, {len(self.config.models)}个模型")
        
        # 生成演示数据
        logger.info("📝 生成演示数据...")
        samples = self.data_generator.generate_mixed_samples(self.config.num_samples)
        logger.info(f"✅ 生成了{len(samples)}个测试样本")
        
        # 保存测试数据
        if self.config.save_results:
            samples_file = self.output_dir / "demo_samples.json"
            with open(samples_file, 'w', encoding='utf-8') as f:
                json.dump(samples, f, ensure_ascii=False, indent=2)
            logger.info(f"💾 测试数据已保存: {samples_file}")
        
        # 运行优化版评估
        logger.info("\n⚡ 运行优化版评估...")
        optimized_evaluator = MockOptimizedEvaluator(enable_optimization=True)
        optimized_results = await optimized_evaluator.evaluate_batch(samples, self.config.models)
        
        # 运行基础版评估（对比）
        logger.info("\n🐌 运行基础版评估（对比）...")
        basic_evaluator = MockOptimizedEvaluator(enable_optimization=False)
        basic_results = await basic_evaluator.evaluate_batch(samples, self.config.models)
        
        # 分析结果
        logger.info("\n📊 分析评估结果...")
        analysis = self._analyze_results(optimized_results, basic_results)
        
        # 显示结果
        self._display_results(analysis)
        
        # 保存结果
        if self.config.save_results:
            await self._save_results(optimized_results, basic_results, analysis)
        
        logger.info("\n🎉 演示完成！")
        return analysis
    
    def _analyze_results(self, optimized_results: Dict[str, Any], basic_results: Dict[str, Any]) -> Dict[str, Any]:
        """分析结果"""
        opt_metrics = optimized_results['metrics']
        basic_metrics = basic_results['metrics']
        
        # 计算改进幅度
        improvements = {}
        
        # 响应时间改进
        if basic_metrics['avg_response_time'] > 0:
            response_time_improvement = (
                (basic_metrics['avg_response_time'] - opt_metrics['avg_response_time']) / 
                basic_metrics['avg_response_time'] * 100
            )
            improvements['response_time'] = response_time_improvement
        
        # 吞吐量改进
        if basic_metrics['throughput'] > 0:
            throughput_improvement = (
                (opt_metrics['throughput'] - basic_metrics['throughput']) / 
                basic_metrics['throughput'] * 100
            )
            improvements['throughput'] = throughput_improvement
        
        # 成功率改进
        opt_success_rate = opt_metrics['successful_requests'] / max(opt_metrics['total_requests'], 1) * 100
        basic_success_rate = basic_metrics['successful_requests'] / max(basic_metrics['total_requests'], 1) * 100
        improvements['success_rate'] = opt_success_rate - basic_success_rate
        
        # 缓存命中率改进
        opt_cache_rate = opt_metrics['cache_hits'] / max(opt_metrics['cache_hits'] + opt_metrics['cache_misses'], 1) * 100
        basic_cache_rate = basic_metrics['cache_hits'] / max(basic_metrics['cache_hits'] + basic_metrics['cache_misses'], 1) * 100
        improvements['cache_hit_rate'] = opt_cache_rate - basic_cache_rate
        
        return {
            'optimized_metrics': opt_metrics,
            'basic_metrics': basic_metrics,
            'improvements': improvements,
            'optimized_results': optimized_results,
            'basic_results': basic_results
        }
    
    def _display_results(self, analysis: Dict[str, Any]):
        """显示结果"""
        opt_metrics = analysis['optimized_metrics']
        basic_metrics = analysis['basic_metrics']
        improvements = analysis['improvements']
        
        print("\n" + "="*80)
        print("📊 评估结果对比")
        print("="*80)
        
        # 基础指标对比
        print("\n📈 性能指标对比:")
        print(f"{'指标':<20} {'基础版':<15} {'优化版':<15} {'改进幅度':<15}")
        print("-" * 70)
        
        print(f"{'平均响应时间(s)':<20} {basic_metrics['avg_response_time']:<15.3f} {opt_metrics['avg_response_time']:<15.3f} {improvements['response_time']:>+13.1f}%")
        print(f"{'吞吐量(req/s)':<20} {basic_metrics['throughput']:<15.2f} {opt_metrics['throughput']:<15.2f} {improvements['throughput']:>+13.1f}%")
        
        basic_success_rate = basic_metrics['successful_requests'] / max(basic_metrics['total_requests'], 1) * 100
        opt_success_rate = opt_metrics['successful_requests'] / max(opt_metrics['total_requests'], 1) * 100
        print(f"{'成功率(%)':<20} {basic_success_rate:<15.1f} {opt_success_rate:<15.1f} {improvements['success_rate']:>+13.1f}%")
        
        basic_cache_rate = basic_metrics['cache_hits'] / max(basic_metrics['cache_hits'] + basic_metrics['cache_misses'], 1) * 100
        opt_cache_rate = opt_metrics['cache_hits'] / max(opt_metrics['cache_hits'] + opt_metrics['cache_misses'], 1) * 100
        print(f"{'缓存命中率(%)':<20} {basic_cache_rate:<15.1f} {opt_cache_rate:<15.1f} {improvements['cache_hit_rate']:>+13.1f}%")
        
        # 详细统计
        print("\n📋 详细统计:")
        print(f"• 总请求数: {opt_metrics['total_requests']}")
        print(f"• 成功请求: {opt_metrics['successful_requests']}")
        print(f"• 失败请求: {opt_metrics['failed_requests']}")
        print(f"• 缓存命中: {opt_metrics['cache_hits']}")
        print(f"• 缓存未命中: {opt_metrics['cache_misses']}")
        
        # 优化效果总结
        print("\n🎯 优化效果总结:")
        if improvements['response_time'] > 0:
            print(f"✅ 响应时间提升 {improvements['response_time']:.1f}%")
        if improvements['throughput'] > 0:
            print(f"✅ 吞吐量提升 {improvements['throughput']:.1f}%")
        if improvements['success_rate'] > 0:
            print(f"✅ 成功率提升 {improvements['success_rate']:.1f}%")
        if improvements['cache_hit_rate'] > 0:
            print(f"✅ 缓存命中率提升 {improvements['cache_hit_rate']:.1f}%")
        
        print("\n💡 优化技术:")
        print("• 🚀 异步并发处理")
        print("• 🧠 智能批处理")
        print("• 💾 请求缓存")
        print("• 🔄 自适应重试")
        print("• 📊 性能监控")
        print("• 🎯 连接池优化")
        
        print("="*80)
    
    async def _save_results(self, optimized_results: Dict[str, Any], basic_results: Dict[str, Any], analysis: Dict[str, Any]):
        """保存结果"""
        # 保存详细结果
        results_file = self.output_dir / "demo_results.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump({
                'optimized_results': optimized_results,
                'basic_results': basic_results,
                'analysis': analysis,
                'config': asdict(self.config),
                'timestamp': time.time()
            }, f, ensure_ascii=False, indent=2)
        
        logger.info(f"💾 详细结果已保存: {results_file}")
        
        # 保存CSV格式的指标对比
        metrics_data = []
        
        for version, metrics in [('基础版', analysis['basic_metrics']), ('优化版', analysis['optimized_metrics'])]:
            metrics_data.append({
                '版本': version,
                '平均响应时间(s)': metrics['avg_response_time'],
                '吞吐量(req/s)': metrics['throughput'],
                '总请求数': metrics['total_requests'],
                '成功请求数': metrics['successful_requests'],
                '失败请求数': metrics['failed_requests'],
                '成功率(%)': metrics['successful_requests'] / max(metrics['total_requests'], 1) * 100,
                '缓存命中数': metrics['cache_hits'],
                '缓存未命中数': metrics['cache_misses'],
                '缓存命中率(%)': metrics['cache_hits'] / max(metrics['cache_hits'] + metrics['cache_misses'], 1) * 100
            })
        
        df = pd.DataFrame(metrics_data)
        csv_file = self.output_dir / "demo_metrics.csv"
        df.to_csv(csv_file, index=False, encoding='utf-8-sig')
        
        logger.info(f"📊 指标对比已保存: {csv_file}")
        
        # 生成Markdown报告
        await self._generate_markdown_report(analysis)
    
    async def _generate_markdown_report(self, analysis: Dict[str, Any]):
        """生成Markdown报告"""
        opt_metrics = analysis['optimized_metrics']
        basic_metrics = analysis['basic_metrics']
        improvements = analysis['improvements']
        
        report = f"""# AI模型评估系统演示报告

## 📋 演示概览

- **演示时间**: {time.strftime('%Y-%m-%d %H:%M:%S')}
- **测试样本数**: {self.config.num_samples}
- **测试模型**: {', '.join(self.config.models)}
- **优化状态**: {'启用' if self.config.enable_optimization else '禁用'}

## 📊 性能对比

### 核心指标

| 指标 | 基础版 | 优化版 | 改进幅度 |
|------|--------|--------|----------|
| 平均响应时间(s) | {basic_metrics['avg_response_time']:.3f} | {opt_metrics['avg_response_time']:.3f} | {improvements['response_time']:+.1f}% |
| 吞吐量(req/s) | {basic_metrics['throughput']:.2f} | {opt_metrics['throughput']:.2f} | {improvements['throughput']:+.1f}% |
| 成功率(%) | {basic_metrics['successful_requests'] / max(basic_metrics['total_requests'], 1) * 100:.1f} | {opt_metrics['successful_requests'] / max(opt_metrics['total_requests'], 1) * 100:.1f} | {improvements['success_rate']:+.1f}% |
| 缓存命中率(%) | {basic_metrics['cache_hits'] / max(basic_metrics['cache_hits'] + basic_metrics['cache_misses'], 1) * 100:.1f} | {opt_metrics['cache_hits'] / max(opt_metrics['cache_hits'] + opt_metrics['cache_misses'], 1) * 100:.1f} | {improvements['cache_hit_rate']:+.1f}% |

### 详细统计

- **总请求数**: {opt_metrics['total_requests']}
- **成功请求**: {opt_metrics['successful_requests']}
- **失败请求**: {opt_metrics['failed_requests']}
- **缓存命中**: {opt_metrics['cache_hits']}
- **缓存未命中**: {opt_metrics['cache_misses']}

## 🎯 优化效果

### 主要改进

"""
        
        if improvements['response_time'] > 0:
            report += f"- ✅ **响应时间提升 {improvements['response_time']:.1f}%**: 通过异步并发和连接池优化\n"
        
        if improvements['throughput'] > 0:
            report += f"- ✅ **吞吐量提升 {improvements['throughput']:.1f}%**: 智能批处理和并发控制\n"
        
        if improvements['success_rate'] > 0:
            report += f"- ✅ **成功率提升 {improvements['success_rate']:.1f}%**: 自适应重试和错误处理\n"
        
        if improvements['cache_hit_rate'] > 0:
            report += f"- ✅ **缓存命中率提升 {improvements['cache_hit_rate']:.1f}%**: 智能缓存策略\n"
        
        report += f"""

### 优化技术

1. **🚀 异步并发处理**
   - 使用asyncio实现高并发
   - 智能并发控制和限流
   - 连接池复用和优化

2. **🧠 智能批处理**
   - 自适应批处理大小
   - 内存感知的批处理策略
   - 优先级队列管理

3. **💾 请求缓存**
   - LRU缓存策略
   - 智能缓存失效
   - 分布式缓存支持

4. **🔄 自适应重试**
   - 指数退避重试
   - 智能错误分类
   - 熔断器模式

5. **📊 性能监控**
   - 实时指标收集
   - 性能瓶颈识别
   - 自动性能调优

## 📈 结论

通过实施多项性能优化技术，AI模型评估系统在以下方面取得了显著改进：

- **性能提升**: 响应时间和吞吐量显著改善
- **稳定性增强**: 成功率提升，错误处理更加完善
- **资源利用**: 缓存机制减少重复请求，提高资源利用率
- **可扩展性**: 支持更高并发和更大规模的评估任务

这些优化使得系统能够更好地满足生产环境的性能要求，为AI模型评估提供了高效、稳定的解决方案。

---

*报告生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}*
"""
        
        report_file = self.output_dir / "demo_report.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info(f"📄 演示报告已生成: {report_file}")


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(
        description="AI模型评估系统功能演示",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python demo.py                    # 默认演示
  python demo.py --samples 50       # 50个样本演示
  python demo.py --no-optimization  # 禁用优化对比
  python demo.py --models gpt-3.5-turbo gpt-4o-mini  # 指定模型
        """
    )
    
    parser.add_argument(
        "--samples", "-n",
        type=int, 
        default=20,
        help="测试样本数量 (默认: 20)"
    )
    parser.add_argument(
        "--models", "-m",
        nargs="+",
        default=['gpt-3.5-turbo', 'gpt-4o-mini', 'glm-4-flash'],
        help="测试模型列表"
    )
    parser.add_argument(
        "--no-optimization",
        action="store_true",
        help="禁用优化（仅运行基础版）"
    )
    parser.add_argument(
        "--no-save",
        action="store_true",
        help="不保存结果文件"
    )
    parser.add_argument(
        "--output-dir",
        default="demo_results",
        help="输出目录 (默认: demo_results)"
    )
    parser.add_argument(
        "--quiet", "-q",
        action="store_true",
        help="静默模式（减少输出）"
    )
    
    args = parser.parse_args()
    
    # 配置日志
    if args.quiet:
        logger.remove()
        logger.add(lambda msg: None)  # 静默
    else:
        logger.remove()
        logger.add(
            lambda msg: print(msg, end=''),
            format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>\n"
        )
    
    # 创建演示配置
    config = DemoConfig(
        num_samples=args.samples,
        models=args.models,
        enable_optimization=not args.no_optimization,
        save_results=not args.no_save,
        output_dir=args.output_dir,
        show_progress=not args.quiet
    )
    
    # 运行演示
    runner = DemoRunner(config)
    
    try:
        analysis = await runner.run_demo()
        
        if not args.quiet:
            print("\n🎉 演示完成！查看结果文件:")
            print(f"• 详细结果: {config.output_dir}/demo_results.json")
            print(f"• 指标对比: {config.output_dir}/demo_metrics.csv")
            print(f"• 演示报告: {config.output_dir}/demo_report.md")
        
        return analysis
        
    except KeyboardInterrupt:
        logger.info("\n👋 用户中断演示")
    except Exception as e:
        logger.error(f"❌ 演示过程中发生错误: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())