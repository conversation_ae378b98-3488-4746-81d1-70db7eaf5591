#!/usr/bin/env python3
"""
测试脚本：验证修改后的HTML分类指标报告生成

这个脚本测试修改后的HTML报告生成器，确保生成的报告结构
与参考报告 yitu_20250805_113340 一致，并包含正确的分类指标。
"""

import pandas as pd
import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from ai_model_evaluation.services.evaluation_report_generator import EvaluationReportGenerator

def create_test_data():
    """创建测试数据，包含多种分类情况"""
    data = [
        # booking 类别
        {'expected_result': 'booking', 'model_a': 'booking', 'model_b': 'booking'},
        {'expected_result': 'booking', 'model_a': 'booking', 'model_b': 'cancel'},
        {'expected_result': 'booking', 'model_a': 'cancel', 'model_b': 'booking'},
        {'expected_result': 'booking', 'model_a': 'none', 'model_b': 'none'},
        {'expected_result': 'booking', 'model_a': 'booking', 'model_b': 'booking'},
        
        # cancel 类别
        {'expected_result': 'cancel', 'model_a': 'cancel', 'model_b': 'cancel'},
        {'expected_result': 'cancel', 'model_a': 'cancel', 'model_b': 'booking'},
        {'expected_result': 'cancel', 'model_a': 'booking', 'model_b': 'cancel'},
        {'expected_result': 'cancel', 'model_a': 'none', 'model_b': 'none'},
        {'expected_result': 'cancel', 'model_a': 'cancel', 'model_b': 'cancel'},
        
        # query 类别
        {'expected_result': 'query', 'model_a': 'query', 'model_b': 'query'},
        {'expected_result': 'query', 'model_a': 'query', 'model_b': 'booking'},
        {'expected_result': 'query', 'model_a': 'none', 'model_b': 'query'},
        
        # none 类别
        {'expected_result': 'none', 'model_a': 'none', 'model_b': 'none'},
        {'expected_result': 'none', 'model_a': 'none', 'model_b': 'booking'},
        {'expected_result': 'none', 'model_a': 'booking', 'model_b': 'none'},
        {'expected_result': 'none', 'model_a': 'cancel', 'model_b': 'query'},
        {'expected_result': 'none', 'model_a': 'none', 'model_b': 'none'},
        {'expected_result': 'none', 'model_a': 'none', 'model_b': 'none'},
        {'expected_result': 'none', 'model_a': 'none', 'model_b': 'none'},
    ]
    
    return pd.DataFrame(data)

def test_html_report_generation():
    """测试HTML报告生成"""
    print("=" * 60)
    print("测试HTML分类指标报告生成")
    print("=" * 60)
    
    # 创建测试数据
    df = create_test_data()
    print(f"创建测试数据: {len(df)} 个样本")
    print(f"类别分布:")
    print(df['expected_result'].value_counts())
    print()
    
    # 创建报告生成器
    generator = EvaluationReportGenerator()
    
    # 计算分类指标
    models = ['model_a', 'model_b']
    metrics_data = {}
    
    for model in models:
        print(f"计算 {model} 的分类指标...")
        metrics = generator._calculate_classification_metrics(df, model)
        metrics_data[model] = metrics
        
        if 'error' in metrics:
            print(f"❌ {model} 计算出错: {metrics['error']}")
        else:
            print(f"✅ {model} 计算成功")
            print(f"   准确率: {metrics.get('accuracy', 0):.2%}")
            print(f"   精确率: {metrics.get('precision', 0):.2%}")
            print(f"   召回率: {metrics.get('recall', 0):.2%}")
            print(f"   F1分数: {metrics.get('f1_score', 0):.2%}")
            print(f"   总样本: {metrics.get('total_samples', 0)}")
            print(f"   正向类别: {metrics.get('positive_classes', [])}")
            print(f"   None类别: {metrics.get('none_class', 'N/A')}")
        print()
    
    # 生成HTML报告
    output_dir = Path("test_output")
    output_dir.mkdir(exist_ok=True)
    
    html_file = output_dir / "test_classification_metrics_report.html"
    
    print(f"生成HTML报告: {html_file}")
    generator._generate_classification_html_report(metrics_data, html_file)
    
    if html_file.exists():
        print(f"✅ HTML报告生成成功: {html_file}")
        print(f"文件大小: {html_file.stat().st_size} 字节")
        
        # 读取并显示部分内容
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
            print(f"内容长度: {len(content)} 字符")
            
            # 检查关键内容
            key_elements = [
                "AI模型分类指标评估报告",
                "基本统计",
                "分类指标",
                "各类别详细指标",
                "混淆矩阵",
                "预测结果分布",
                "精确率",
                "召回率",
                "F1分数"
            ]
            
            print("\n检查关键元素:")
            for element in key_elements:
                if element in content:
                    print(f"✅ {element}")
                else:
                    print(f"❌ {element}")
    else:
        print(f"❌ HTML报告生成失败")
    
    return html_file

def compare_with_reference():
    """与参考报告进行对比"""
    print("=" * 60)
    print("与参考报告对比")
    print("=" * 60)
    
    reference_file = Path("evaluation_results/yitu_20250805_113340/classification_metrics_report.html")
    
    if not reference_file.exists():
        print(f"❌ 参考文件不存在: {reference_file}")
        return
    
    print(f"参考文件: {reference_file}")
    
    # 读取参考文件内容
    with open(reference_file, 'r', encoding='utf-8') as f:
        reference_content = f.read()
    
    print(f"参考文件大小: {len(reference_content)} 字符")
    
    # 分析参考文件的结构
    key_sections = [
        "AI模型分类指标评估报告",
        "基本统计",
        "分类指标", 
        "详细指标对比",
        "各类别详细指标",
        "混淆矩阵",
        "预测结果分布"
    ]
    
    print("\n参考文件包含的关键部分:")
    for section in key_sections:
        if section in reference_content:
            print(f"✅ {section}")
        else:
            print(f"❌ {section}")

def print_metrics_summary(metrics_data):
    """打印指标摘要"""
    print("=" * 60)
    print("指标摘要")
    print("=" * 60)
    
    for model_name, metrics in metrics_data.items():
        if 'error' in metrics:
            continue
            
        print(f"\n🤖 {model_name}:")
        print(f"  准确率: {metrics.get('accuracy', 0):.2%}")
        print(f"  精确率: {metrics.get('precision', 0):.2%}")
        print(f"  召回率: {metrics.get('recall', 0):.2%}")
        print(f"  F1分数: {metrics.get('f1_score', 0):.2%}")
        
        # 宏平均
        macro_avg = metrics.get('macro_avg', {})
        print(f"  宏平均 - 精确率: {macro_avg.get('precision', 0):.2%}")
        print(f"  宏平均 - 召回率: {macro_avg.get('recall', 0):.2%}")
        print(f"  宏平均 - F1分数: {macro_avg.get('f1_score', 0):.2%}")
        
        # 加权平均
        weighted_avg = metrics.get('weighted_avg', {})
        print(f"  加权平均 - 精确率: {weighted_avg.get('precision', 0):.2%}")
        print(f"  加权平均 - 召回率: {weighted_avg.get('recall', 0):.2%}")
        print(f"  加权平均 - F1分数: {weighted_avg.get('f1_score', 0):.2%}")
        
        # 总体指标
        overall_metrics = metrics.get('overall_metrics', {})
        print(f"  TP: {overall_metrics.get('tp', 0)}")
        print(f"  FP: {overall_metrics.get('fp', 0)}")
        print(f"  FN: {overall_metrics.get('fn', 0)}")
        print(f"  TN: {overall_metrics.get('tn', 0)}")

if __name__ == "__main__":
    print("测试HTML分类指标报告生成器")
    print("=" * 60)
    
    # 生成测试报告
    html_file = test_html_report_generation()
    
    # 与参考报告对比
    compare_with_reference()
    
    print(f"\n🎉 测试完成！")
    print(f"生成的报告文件: {html_file}")
    print(f"请在浏览器中打开查看效果。")
