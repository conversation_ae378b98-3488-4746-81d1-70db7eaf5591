#!/usr/bin/env python3
"""
自动化AI模型评估脚本
支持配置文件路径、模型列表，自动生成结果文件夹
现在支持高性能优化版本！
"""

import os
import sys
import subprocess
import json
import asyncio
import time
import yaml
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Optional

# ==================== 配置区域 ====================
# 在此区域修改评估配置，无需深入代码内部

# 1. 版本和配置设置
DEFAULT_USE_OPTIMIZED = True  # 🚀 默认使用高性能优化版本
DEFAULT_CONFIG_PATH = "config.yaml"  # 默认配置文件路径

# 2. 单个评估配置
SINGLE_EVALUATION_CONFIG = {
    "input_file": "yitu_10.csv",  # 输入文件路径
    "template": "yitu",           # 模板名称
    "models": ["qwen-plus", "通义千问3-235B-A22B", "ERNIE 4.5 Turbo", 'doubao-1.6-seed', "deepseek-v3",
               "doubao1.5 pro"],
    "output_name": None           # 输出名称（None为自动生成）
}

# 3. 批量评估配置
BATCH_EVALUATION_CONFIGS = [
    {
        "input_file": "yitu.xlsx",
        "template": "yitu",
        "models": ["qwen-plus", "通义千问3-235B-A22B"],
        "output_name": "yitu_batch_1"
    },
    {
        "input_file": "data/sample.csv",
        "template": "qa_template",
        "models": ["gpt4", "claude3"],
        "output_name": "sample_batch_1"
    }
]

# 4. 运行模式配置
DEFAULT_RUN_MODE = "single"  # "single" 或 "batch"

# ==================== 核心类定义 ====================

class AutoEvaluator:
    """自动化评估器 - 使用统一评估引擎"""

    def __init__(self, use_optimized: bool = False, config_path: str = "config.yaml"):
        """
        初始化评估器

        Args:
            use_optimized: 是否使用优化版本
            config_path: 配置文件路径
        """
        self.base_dir = Path.cwd()
        self.results_base_dir = self.base_dir / "evaluation_results"
        self.use_optimized = use_optimized
        self.config_path = config_path

        # 使用与CLI相同的配置管理器和评估引擎
        from src.ai_model_evaluation.config.manager import ConfigManager
        from src.ai_model_evaluation.services.evaluation import UnifiedEvaluationEngine
        from src.ai_model_evaluation.services.evaluation_report_generator import EvaluationReportGenerator
        from src.ai_model_evaluation.models.core import (
            Provider, ModelConfig, PromptTemplate, EvaluationRow, EvaluationTask, TaskStatus
        )
        from src.ai_model_evaluation.services.file_handler import FileHandler
        from src.ai_model_evaluation.utils.helpers import generate_id

        # 初始化配置管理器
        self.config_manager = ConfigManager(config_path)
        self.config_data = self.config_manager.load_config()

        # 初始化统一评估引擎
        self.evaluation_engine = UnifiedEvaluationEngine(
            use_optimized=use_optimized,
            config_manager=self.config_manager
        )

        # 初始化文件处理器和报告生成器
        self.file_handler = FileHandler()
        self.report_generator = EvaluationReportGenerator()

        print(f"🎯 AutoEvaluator初始化完成 - {'优化版本' if use_optimized else '标准版本'}")

        
    def run_evaluation(self,
                      input_file: str,
                      template: str,
                      models: List[str],
                      output_name: Optional[str] = None) -> Dict:
        """
        运行评估 - 使用统一评估引擎

        Args:
            input_file: 输入文件路径
            template: 模板名称
            models: 模型列表
            output_name: 输出名称（可选）

        Returns:
            评估结果信息
        """
        # 验证输入文件
        input_path = Path(input_file)
        if not input_path.exists():
            raise FileNotFoundError(f"输入文件不存在: {input_file}")

        # 生成输出文件夹名称
        if output_name is None:
            file_stem = input_path.stem  # 不包含扩展名的文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_name = f"{file_stem}_{timestamp}"

        # 创建结果目录
        output_dir = self.results_base_dir / output_name
        output_dir.mkdir(parents=True, exist_ok=True)

        # 使用统一评估引擎运行评估
        return asyncio.run(self._run_unified_evaluation(
            input_file, template, models, output_name, output_dir
        ))

    async def _run_unified_evaluation(self, input_file: str, template: str, models: List[str],
                                    output_name: str, output_dir: Path) -> Dict:
        """使用统一评估引擎运行评估"""
        from src.ai_model_evaluation.utils.helpers import generate_id

        start_time = time.time()

        print(f"🚀 开始评估 ({'优化版本' if self.use_optimized else '标准版本'})...")
        print(f"📁 输入文件: {input_file}")
        print(f"📋 模板: {template}")
        print(f"🤖 模型: {models}")
        print(f"📂 输出目录: {output_dir}")
        print("-" * 60)

        try:
            # 先获取模板配置
            template_config = self.config_data.get_prompt_template(template)
            if not template_config:
                raise ValueError(f"未找到模板: {template}")

            # 使用模板变量创建专用的文件处理器
            from src.ai_model_evaluation.services.file_handler import FileHandler
            template_file_handler = FileHandler(required_columns=template_config.variables)

            # 读取评估数据
            evaluation_data = template_file_handler.read_evaluation_data(input_file)
            print(f"📊 读取到 {len(evaluation_data)} 条评估数据")

            # 获取模型配置
            model_configs = []
            providers = []
            for model_id in models:
                model_config = self.config_data.get_model(model_id)
                if not model_config:
                    raise ValueError(f"未找到模型: {model_id}")
                model_configs.append(model_config)

                # 获取对应的提供商
                provider = self.config_data.get_provider(model_config.provider_id)
                if provider and provider not in providers:
                    providers.append(provider)

            print(f"🔧 配置的模型: {[m.display_name for m in model_configs]}")
            print(f"🌐 使用的提供商: {[p.name for p in providers]}")

            # 创建评估任务
            from src.ai_model_evaluation.models.core import EvaluationTask, TaskStatus
            from datetime import datetime
            task = EvaluationTask(
                id=generate_id(),
                name=output_name,
                prompt_template_id=template,
                selected_models=models,
                input_file_path=input_file,
                output_directory=str(output_dir),
                status=TaskStatus.PENDING,
                created_at=datetime.now()
            )

            # 设置进度回调
            def progress_callback(progress: float):
                print(f"📈 评估进度: {progress:.1%}")

            self.evaluation_engine.set_progress_callback(progress_callback)

            # 运行评估
            results = await self.evaluation_engine.run_evaluation(
                task, providers, model_configs, template_config, evaluation_data
            )

            # 保存结果
            result_file = output_dir / f"{output_name}_results.csv"
            self.file_handler.write_results(results, str(result_file))

            end_time = time.time()
            execution_time = end_time - start_time

            print(f"✅ 评估完成! 耗时: {execution_time:.2f}秒")
            print(f"📊 结果文件: {result_file}")

            # 获取性能统计
            performance_stats = self.evaluation_engine.get_performance_stats()

            evaluation_info = {
                "status": "success",
                "version": "unified_optimized" if self.use_optimized else "unified_standard",
                "input_file": input_file,
                "template": template,
                "models": models,
                "output_dir": str(output_dir),
                "result_files": [str(result_file)],
                "execution_time": datetime.now().isoformat(),
                "duration_seconds": execution_time,
                "data_count": len(evaluation_data),
                "performance_stats": performance_stats
            }

            # 保存评估信息
            info_file = output_dir / "evaluation_info.json"
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(evaluation_info, f, ensure_ascii=False, indent=2)

            print(f"📋 评估信息: {info_file}")

            # 自动生成分析报告
            self.report_generator.generate_analysis_report(result_file, output_dir)
            self.report_generator.generate_classification_metrics_report(result_file, output_dir)

            return evaluation_info

        except Exception as e:
            end_time = time.time()
            execution_time = end_time - start_time

            print(f"❌ 评估失败: {e}")

            evaluation_info = {
                "status": "failed",
                "version": "unified_optimized" if self.use_optimized else "unified_standard",
                "input_file": input_file,
                "template": template,
                "models": models,
                "output_dir": str(output_dir),
                "error": str(e),
                "execution_time": datetime.now().isoformat(),
                "duration_seconds": execution_time
            }

            # 保存失败信息
            info_file = output_dir / "evaluation_info.json"
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(evaluation_info, f, ensure_ascii=False, indent=2)

            raise





    def batch_evaluate(self, evaluations: List[Dict]) -> List[Dict]:
        """
        批量评估
        
        Args:
            evaluations: 评估配置列表，每个包含 input_file, template, models, output_name
            
        Returns:
            评估结果列表
        """
        results = []
        
        for i, eval_config in enumerate(evaluations, 1):
            print(f"\n{'='*60}")
            print(f"📋 批量评估 {i}/{len(evaluations)}")
            print(f"{'='*60}")
            
            try:
                result = self.run_evaluation(**eval_config)
                results.append(result)
            except Exception as e:
                print(f"❌ 评估 {i} 失败: {e}")
                results.append({
                    "status": "error",
                    "error": str(e),
                    "config": eval_config
                })
        
        # 保存批量评估总结
        summary_file = self.results_base_dir / f"batch_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump({
                "total_evaluations": len(evaluations),
                "successful": len([r for r in results if r.get("status") == "success"]),
                "failed": len([r for r in results if r.get("status") != "success"]),
                "results": results,
                "summary_time": datetime.now().isoformat()
            }, f, ensure_ascii=False, indent=2)
        
        print(f"\n📋 批量评估总结保存到: {summary_file}")
        return results


# ==================== 主函数 ====================

def main():
    """主函数 - 配置和运行评估"""
    import argparse

    # 解析命令行参数
    parser = argparse.ArgumentParser(description='AI模型评估系统 - 自动化评估脚本')
    parser.add_argument('--optimized', action='store_true',default=DEFAULT_USE_OPTIMIZED, help='使用高性能优化版本')
    parser.add_argument('--config', default=DEFAULT_CONFIG_PATH, help='配置文件路径')
    parser.add_argument('--mode', choices=['single', 'batch'], default=DEFAULT_RUN_MODE, help='运行模式')
    args = parser.parse_args()

    # 显示系统信息
    print("🎯 AI模型评估系统")
    print(f"📊 版本: {'🚀 高性能优化版本' if args.optimized else '📋 标准版本'}")
    print("=" * 60)

    try:
        # 初始化评估器
        evaluator = AutoEvaluator(
            use_optimized=args.optimized,
            config_path=args.config
        )

        if args.mode == "single":
            print("🔄 运行单个评估...")

            # 使用配置区域的设置
            result = evaluator.run_evaluation(
                input_file=SINGLE_EVALUATION_CONFIG["input_file"],
                template=SINGLE_EVALUATION_CONFIG["template"],
                models=SINGLE_EVALUATION_CONFIG["models"],
                output_name=SINGLE_EVALUATION_CONFIG["output_name"]
            )

            print(f"\n✅ 评估完成! 状态: {result['status']}")

        elif args.mode == "batch":
            print("🔄 运行批量评估...")

            # 使用配置区域的设置
            results = evaluator.batch_evaluate(BATCH_EVALUATION_CONFIGS)

            successful = len([r for r in results if r.get("status") == "success"])
            total = len(results)
            print(f"\n✅ 批量评估完成! 成功: {successful}/{total}")

    except Exception as e:
        print(f"❌ 评估过程中发生错误: {e}")
        if args.optimized:
            print("💡 提示: 如果优化版本出现问题，可以设置 USE_OPTIMIZED = False 使用原版本")
        raise


if __name__ == "__main__":
    main()
