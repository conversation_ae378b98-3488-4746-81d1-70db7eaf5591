"""优化版主入口文件
整合所有性能优化组件，提供统一的高性能AI模型评估接口
"""

import asyncio
import sys
import time
from typing import List, Dict, Optional, Any
from pathlib import Path
from loguru import logger

# 导入优化组件
from .config.manager import ConfigManager
from .services.optimized_evaluation_engine import (
    OptimizedEvaluationEngine,
    OptimizedEvaluationConfig,
    BatchStrategy,
    Priority
)
from .services.performance_monitor import (
    PerformanceMonitor,
    create_performance_monitor
)
from .services.async_file_handler import AsyncFileHandler
from .models.core import ModelConfig, PromptTemplate
from .cli.optimized_cli import optimized_cli


# 配置日志
logger.remove()
logger.add(
    sys.stderr,
    format="<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
    level="INFO"
)


class OptimizedAIModelEvaluator:
    """优化版AI模型评估器
    
    整合所有性能优化组件，提供高性能的模型评估服务
    """
    
    def __init__(self, 
                 config_path: str = "config.yaml",
                 optimization_config: Optional[OptimizedEvaluationConfig] = None,
                 enable_monitoring: bool = True,
                 enable_system_monitoring: bool = True):
        self.config_path = config_path
        self.config_manager: Optional[ConfigManager] = None
        self.evaluation_engine: Optional[OptimizedEvaluationEngine] = None
        self.performance_monitor: Optional[PerformanceMonitor] = None
        self.file_handler: Optional[AsyncFileHandler] = None
        
        # 优化配置
        self.optimization_config = optimization_config or OptimizedEvaluationConfig()
        
        # 监控配置
        self.enable_monitoring = enable_monitoring
        self.enable_system_monitoring = enable_system_monitoring
        
        # 状态
        self._initialized = False
        self._running = False
    
    async def initialize(self):
        """初始化评估器"""
        if self._initialized:
            return
        
        logger.info("正在初始化优化版AI模型评估器...")
        start_time = time.time()
        
        try:
            # 初始化配置管理器
            self.config_manager = ConfigManager(self.config_path)
            await self.config_manager.load_config()
            logger.info("配置管理器初始化完成")
            
            # 初始化性能监控器
            if self.enable_monitoring:
                self.performance_monitor = create_performance_monitor(
                    enable_system_monitoring=self.enable_system_monitoring,
                    system_monitor_interval=1.0,
                    metrics_retention_hours=24
                )
                await self.performance_monitor.start()
                logger.info("性能监控器初始化完成")
            
            # 初始化异步文件处理器
            self.file_handler = AsyncFileHandler(
                max_memory_mb=self.optimization_config.memory_threshold_mb,
                chunk_size=self.optimization_config.min_batch_size * 100,
                enable_compression=True
            )
            logger.info("异步文件处理器初始化完成")
            
            # 初始化评估引擎
            self.evaluation_engine = OptimizedEvaluationEngine(
                config_manager=self.config_manager,
                optimization_config=self.optimization_config,
                performance_monitor=self.performance_monitor
            )
            await self.evaluation_engine.__aenter__()
            logger.info("优化评估引擎初始化完成")
            
            self._initialized = True
            init_time = time.time() - start_time
            logger.info(f"优化版AI模型评估器初始化完成，耗时: {init_time:.2f}秒")
            
        except Exception as e:
            logger.error(f"初始化失败: {e}")
            await self.cleanup()
            raise
    
    async def cleanup(self):
        """清理资源"""
        logger.info("正在清理资源...")
        
        try:
            if self.evaluation_engine:
                await self.evaluation_engine.__aexit__(None, None, None)
                self.evaluation_engine = None
            
            if self.performance_monitor:
                await self.performance_monitor.stop()
                self.performance_monitor = None
            
            if self.file_handler:
                await self.file_handler.cleanup()
                self.file_handler = None
            
            self._initialized = False
            self._running = False
            logger.info("资源清理完成")
            
        except Exception as e:
            logger.error(f"清理资源时出错: {e}")
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.cleanup()
    
    async def evaluate_models(self,
                            input_file: str,
                            output_file: str,
                            model_ids: Optional[List[str]] = None,
                            template_name: Optional[str] = None,
                            priority: Priority = Priority.NORMAL,
                            **kwargs) -> Dict[str, Any]:
        """评估模型
        
        Args:
            input_file: 输入数据文件路径
            output_file: 输出结果文件路径
            model_ids: 要评估的模型ID列表，None表示使用所有模型
            template_name: 提示词模板名称，None表示使用默认模板
            priority: 请求优先级
            **kwargs: 其他参数
        
        Returns:
            评估结果字典
        """
        if not self._initialized:
            await self.initialize()
        
        logger.info(f"开始模型评估: {input_file} -> {output_file}")
        start_time = time.time()
        
        try:
            # 获取模型配置
            models = []
            if model_ids:
                for model_id in model_ids:
                    model_config = self.config_manager.get_model_config(model_id)
                    if model_config:
                        models.append(model_config)
                    else:
                        logger.warning(f"未找到模型配置: {model_id}")
            else:
                models = self.config_manager.get_all_models()
            
            if not models:
                raise ValueError("没有可用的模型配置")
            
            logger.info(f"将评估 {len(models)} 个模型: {', '.join(m.id for m in models)}")
            
            # 获取提示词模板
            if not template_name:
                template_name = self.config_manager.get_default_template_name()
            
            template = self.config_manager.get_prompt_template(template_name)
            if not template:
                raise ValueError(f"未找到提示词模板: {template_name}")
            
            logger.info(f"使用提示词模板: {template_name}")
            
            # 执行评估
            result = await self.evaluation_engine.evaluate_models_optimized(
                input_file=input_file,
                output_file=output_file,
                models=models,
                prompt_template=template,
                priority=priority
            )
            
            # 计算总耗时
            total_time = time.time() - start_time
            
            # 构建结果
            evaluation_result = {
                'success': True,
                'total_time': total_time,
                'models_evaluated': len(models),
                'model_ids': [m.id for m in models],
                'template_name': template_name,
                'input_file': input_file,
                'output_file': output_file,
                'evaluation_metrics': {
                    'total_requests': result.total_requests,
                    'successful_requests': result.successful_requests,
                    'failed_requests': result.failed_requests,
                    'success_rate': (result.successful_requests / max(1, result.total_requests)) * 100,
                    'requests_per_second': result.requests_per_second,
                    'average_response_time': result.average_response_time,
                    'total_time': result.total_time
                }
            }
            
            # 添加性能摘要
            if self.performance_monitor:
                performance_summary = self.evaluation_engine.get_performance_summary()
                evaluation_result['performance_summary'] = performance_summary
            
            logger.info(f"模型评估完成，总耗时: {total_time:.2f}秒")
            logger.info(f"总请求数: {result.total_requests}, 成功率: {evaluation_result['evaluation_metrics']['success_rate']:.1f}%")
            logger.info(f"平均RPS: {result.requests_per_second:.2f}, 平均响应时间: {result.average_response_time:.2f}秒")
            
            return evaluation_result
            
        except Exception as e:
            logger.error(f"模型评估失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'total_time': time.time() - start_time
            }
    
    async def batch_evaluate(self,
                           input_files: List[str],
                           output_dir: str,
                           model_ids: Optional[List[str]] = None,
                           template_name: Optional[str] = None,
                           priority: Priority = Priority.NORMAL) -> List[Dict[str, Any]]:
        """批量评估多个文件
        
        Args:
            input_files: 输入文件列表
            output_dir: 输出目录
            model_ids: 要评估的模型ID列表
            template_name: 提示词模板名称
            priority: 请求优先级
        
        Returns:
            评估结果列表
        """
        if not self._initialized:
            await self.initialize()
        
        logger.info(f"开始批量评估 {len(input_files)} 个文件")
        results = []
        
        # 创建输出目录
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 并发评估所有文件
        tasks = []
        for i, input_file in enumerate(input_files):
            input_path = Path(input_file)
            output_file = output_path / f"{input_path.stem}_result_{i}.csv"
            
            task = self.evaluate_models(
                input_file=input_file,
                output_file=str(output_file),
                model_ids=model_ids,
                template_name=template_name,
                priority=priority
            )
            tasks.append(task)
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    'success': False,
                    'error': str(result),
                    'input_file': input_files[i]
                })
            else:
                processed_results.append(result)
        
        # 统计结果
        successful = sum(1 for r in processed_results if r.get('success', False))
        logger.info(f"批量评估完成: {successful}/{len(input_files)} 个文件成功")
        
        return processed_results
    
    def get_performance_metrics(self) -> Optional[Dict[str, Any]]:
        """获取性能指标"""
        if not self.performance_monitor:
            return None
        
        return self.performance_monitor.get_current_metrics().to_dict()
    
    def get_performance_report(self, hours: int = 1) -> Optional[Dict[str, Any]]:
        """生成性能报告"""
        if not self.performance_monitor:
            return None
        
        return self.performance_monitor.generate_report(hours)
    
    def export_performance_metrics(self, file_path: str):
        """导出性能指标"""
        if not self.performance_monitor:
            logger.warning("性能监控未启用，无法导出指标")
            return
        
        self.performance_monitor.export_metrics(file_path)
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        health_status = {
            'status': 'healthy',
            'timestamp': time.time(),
            'components': {}
        }
        
        try:
            # 检查配置管理器
            if self.config_manager:
                health_status['components']['config_manager'] = 'healthy'
            else:
                health_status['components']['config_manager'] = 'not_initialized'
                health_status['status'] = 'degraded'
            
            # 检查评估引擎
            if self.evaluation_engine:
                health_status['components']['evaluation_engine'] = 'healthy'
            else:
                health_status['components']['evaluation_engine'] = 'not_initialized'
                health_status['status'] = 'degraded'
            
            # 检查性能监控器
            if self.performance_monitor:
                health_status['components']['performance_monitor'] = 'healthy'
                # 添加当前性能指标
                current_metrics = self.performance_monitor.get_current_metrics()
                health_status['current_metrics'] = {
                    'cpu_usage': current_metrics.cpu_usage,
                    'memory_usage_percent': current_metrics.memory_usage_percent,
                    'requests_per_second': current_metrics.requests_per_second,
                    'error_rate': current_metrics.error_rate
                }
            else:
                health_status['components']['performance_monitor'] = 'disabled'
            
            # 检查文件处理器
            if self.file_handler:
                health_status['components']['file_handler'] = 'healthy'
            else:
                health_status['components']['file_handler'] = 'not_initialized'
                health_status['status'] = 'degraded'
            
            # 检查系统资源
            import psutil
            cpu_percent = psutil.cpu_percent()
            memory = psutil.virtual_memory()
            
            if cpu_percent > 90:
                health_status['status'] = 'unhealthy'
                health_status['warnings'] = health_status.get('warnings', [])
                health_status['warnings'].append(f'High CPU usage: {cpu_percent}%')
            
            if memory.percent > 90:
                health_status['status'] = 'unhealthy'
                health_status['warnings'] = health_status.get('warnings', [])
                health_status['warnings'].append(f'High memory usage: {memory.percent}%')
            
            health_status['system_resources'] = {
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_available_gb': memory.available / 1024 / 1024 / 1024
            }
            
        except Exception as e:
            health_status['status'] = 'unhealthy'
            health_status['error'] = str(e)
        
        return health_status


# 便捷函数
async def quick_evaluate(input_file: str,
                        output_file: str,
                        config_path: str = "config.yaml",
                        model_ids: Optional[List[str]] = None,
                        template_name: Optional[str] = None,
                        **optimization_kwargs) -> Dict[str, Any]:
    """快速评估函数
    
    Args:
        input_file: 输入数据文件路径
        output_file: 输出结果文件路径
        config_path: 配置文件路径
        model_ids: 要评估的模型ID列表
        template_name: 提示词模板名称
        **optimization_kwargs: 优化配置参数
    
    Returns:
        评估结果字典
    """
    # 创建优化配置
    optimization_config = OptimizedEvaluationConfig(**optimization_kwargs)
    
    # 创建评估器并执行评估
    async with OptimizedAIModelEvaluator(
        config_path=config_path,
        optimization_config=optimization_config
    ) as evaluator:
        return await evaluator.evaluate_models(
            input_file=input_file,
            output_file=output_file,
            model_ids=model_ids,
            template_name=template_name
        )


def main():
    """主函数 - 启动优化版CLI"""
    try:
        optimized_cli()
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()