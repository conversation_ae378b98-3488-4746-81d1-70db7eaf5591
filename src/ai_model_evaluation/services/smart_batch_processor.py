"""智能批处理优化器
实现动态批处理策略、内存优化和智能调度
"""

import asyncio
import time
import gc
from typing import Dict, List, Optional, Tuple, Callable, Any
from dataclasses import dataclass, field
from collections import defaultdict, deque
from enum import Enum
import heapq
import weakref
from loguru import logger

from ..models.core import APIRequest, APIResponse, ModelConfig
from ..utils.exceptions import BatchProcessingError


class BatchStrategy(Enum):
    """批处理策略枚举"""
    FIXED_SIZE = "fixed_size"  # 固定大小批处理
    ADAPTIVE_SIZE = "adaptive_size"  # 自适应大小批处理
    TIME_BASED = "time_based"  # 基于时间的批处理
    MEMORY_AWARE = "memory_aware"  # 内存感知批处理
    HYBRID = "hybrid"  # 混合策略


class Priority(Enum):
    """请求优先级"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4


@dataclass
class BatchConfig:
    """批处理配置"""
    strategy: BatchStrategy = BatchStrategy.ADAPTIVE_SIZE
    min_batch_size: int = 5
    max_batch_size: int = 50
    max_wait_time: float = 2.0  # 最大等待时间（秒）
    memory_threshold_mb: float = 500.0  # 内存阈值（MB）
    adaptive_factor: float = 1.2  # 自适应因子
    priority_enabled: bool = True
    enable_compression: bool = True
    enable_deduplication: bool = True


@dataclass
class PrioritizedRequest:
    """优先级请求"""
    request: APIRequest
    priority: Priority
    timestamp: float
    retry_count: int = 0
    
    def __lt__(self, other):
        # 优先级高的先处理，时间早的先处理
        if self.priority.value != other.priority.value:
            return self.priority.value > other.priority.value
        return self.timestamp < other.timestamp


@dataclass
class BatchMetrics:
    """批处理指标"""
    total_batches: int = 0
    total_requests: int = 0
    avg_batch_size: float = 0.0
    avg_wait_time: float = 0.0
    avg_processing_time: float = 0.0
    memory_usage_mb: float = 0.0
    compression_ratio: float = 0.0
    deduplication_ratio: float = 0.0
    throughput_rps: float = 0.0
    batch_sizes: deque = field(default_factory=lambda: deque(maxlen=1000))
    wait_times: deque = field(default_factory=lambda: deque(maxlen=1000))
    processing_times: deque = field(default_factory=lambda: deque(maxlen=1000))
    
    def update_batch_metrics(self, batch_size: int, wait_time: float, processing_time: float):
        """更新批处理指标"""
        self.total_batches += 1
        self.total_requests += batch_size
        
        self.batch_sizes.append(batch_size)
        self.wait_times.append(wait_time)
        self.processing_times.append(processing_time)
        
        # 计算平均值
        if self.batch_sizes:
            self.avg_batch_size = sum(self.batch_sizes) / len(self.batch_sizes)
        if self.wait_times:
            self.avg_wait_time = sum(self.wait_times) / len(self.wait_times)
        if self.processing_times:
            self.avg_processing_time = sum(self.processing_times) / len(self.processing_times)
        
        # 计算吞吐量
        if self.avg_processing_time > 0:
            self.throughput_rps = self.avg_batch_size / self.avg_processing_time


class MemoryMonitor:
    """内存监控器"""
    
    def __init__(self, threshold_mb: float = 500.0):
        self.threshold_mb = threshold_mb
        self.current_usage_mb = 0.0
        self.peak_usage_mb = 0.0
        self.last_gc_time = time.time()
        self.gc_interval = 30.0  # 30秒
    
    def get_memory_usage(self) -> float:
        """获取当前内存使用量（MB）"""
        try:
            import psutil
            import os
            process = psutil.Process(os.getpid())
            self.current_usage_mb = process.memory_info().rss / 1024 / 1024
            self.peak_usage_mb = max(self.peak_usage_mb, self.current_usage_mb)
            return self.current_usage_mb
        except ImportError:
            return 0.0
    
    def is_memory_pressure(self) -> bool:
        """检查是否存在内存压力"""
        return self.get_memory_usage() > self.threshold_mb
    
    async def periodic_gc(self):
        """定期垃圾回收"""
        current_time = time.time()
        if current_time - self.last_gc_time > self.gc_interval:
            gc.collect()
            self.last_gc_time = current_time
            logger.debug(f"Periodic GC executed, memory usage: {self.current_usage_mb:.1f}MB")


class RequestDeduplicator:
    """请求去重器"""
    
    def __init__(self, max_cache_size: int = 10000):
        self.request_cache = weakref.WeakValueDictionary()
        self.hash_cache = {}
        self.max_cache_size = max_cache_size
        self.dedup_count = 0
        self.total_count = 0
    
    def get_request_hash(self, request: APIRequest) -> str:
        """获取请求哈希"""
        # 基于模型ID、提示词和参数生成哈希
        key_parts = [
            request.model.id,
            request.prompt,
            str(request.model.temperature),
            str(request.model.max_tokens)
        ]
        return hash(tuple(key_parts))
    
    def deduplicate_requests(self, requests: List[APIRequest]) -> Tuple[List[APIRequest], Dict[str, List[int]]]:
        """去重请求并返回去重后的请求和映射关系"""
        self.total_count += len(requests)
        
        unique_requests = []
        duplicate_map = defaultdict(list)  # hash -> [original_indices]
        hash_to_unique_index = {}  # hash -> unique_index
        
        for i, request in enumerate(requests):
            request_hash = self.get_request_hash(request)
            
            if request_hash in hash_to_unique_index:
                # 重复请求
                unique_index = hash_to_unique_index[request_hash]
                duplicate_map[request_hash].append(i)
                self.dedup_count += 1
            else:
                # 唯一请求
                unique_index = len(unique_requests)
                unique_requests.append(request)
                hash_to_unique_index[request_hash] = unique_index
                duplicate_map[request_hash] = [i]
        
        # 清理缓存
        if len(self.hash_cache) > self.max_cache_size:
            self.hash_cache.clear()
        
        return unique_requests, duplicate_map
    
    def get_deduplication_ratio(self) -> float:
        """获取去重比例"""
        if self.total_count == 0:
            return 0.0
        return self.dedup_count / self.total_count


class RequestCompressor:
    """请求压缩器"""
    
    def __init__(self, enable_compression: bool = True):
        self.enable_compression = enable_compression
        self.original_size = 0
        self.compressed_size = 0
    
    def compress_requests(self, requests: List[APIRequest]) -> Tuple[List[APIRequest], float]:
        """压缩请求（这里主要是优化数据结构）"""
        if not self.enable_compression:
            return requests, 1.0
        
        # 计算原始大小
        original_size = sum(len(req.prompt) for req in requests)
        self.original_size += original_size
        
        # 简单的压缩策略：去除多余空白字符
        compressed_requests = []
        for request in requests:
            compressed_prompt = ' '.join(request.prompt.split())
            compressed_request = APIRequest(
                request_id=request.request_id,
                model=request.model,
                prompt=compressed_prompt
            )
            compressed_requests.append(compressed_request)
        
        # 计算压缩后大小
        compressed_size = sum(len(req.prompt) for req in compressed_requests)
        self.compressed_size += compressed_size
        
        compression_ratio = compressed_size / original_size if original_size > 0 else 1.0
        
        return compressed_requests, compression_ratio
    
    def get_compression_ratio(self) -> float:
        """获取总体压缩比例"""
        if self.original_size == 0:
            return 1.0
        return self.compressed_size / self.original_size


class SmartBatchProcessor:
    """智能批处理优化器"""
    
    def __init__(self, 
                 config: BatchConfig = None,
                 api_client_manager=None):
        """初始化智能批处理器"""
        self.config = config or BatchConfig()
        self.api_client_manager = api_client_manager
        
        # 请求队列（按提供商分组）
        self._request_queues: Dict[str, List[PrioritizedRequest]] = defaultdict(list)
        self._queue_locks: Dict[str, asyncio.Lock] = defaultdict(asyncio.Lock)
        
        # 组件
        self.memory_monitor = MemoryMonitor(self.config.memory_threshold_mb)
        self.deduplicator = RequestDeduplicator() if self.config.enable_deduplication else None
        self.compressor = RequestCompressor(self.config.enable_compression)
        
        # 指标
        self.metrics = BatchMetrics()
        
        # 自适应参数
        self._adaptive_batch_sizes: Dict[str, int] = defaultdict(lambda: self.config.min_batch_size)
        self._performance_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        
        # 处理任务
        self._processing_tasks: Dict[str, asyncio.Task] = {}
        self._shutdown_event = asyncio.Event()
        
        logger.info(f"SmartBatchProcessor initialized: strategy={self.config.strategy.value}, min_batch={self.config.min_batch_size}, max_batch={self.config.max_batch_size}")
    
    async def start(self):
        """启动批处理器"""
        logger.info("Starting SmartBatchProcessor")
        
        # 为每个已知的提供商启动处理任务
        if self.api_client_manager:
            for provider_id in self.api_client_manager._clients.keys():
                await self._start_provider_processor(provider_id)
    
    async def stop(self):
        """停止批处理器"""
        logger.info("Stopping SmartBatchProcessor")
        
        self._shutdown_event.set()
        
        # 等待所有处理任务完成
        if self._processing_tasks:
            await asyncio.gather(*self._processing_tasks.values(), return_exceptions=True)
        
        self._processing_tasks.clear()
        
        logger.info("SmartBatchProcessor stopped")
    
    async def _start_provider_processor(self, provider_id: str):
        """为提供商启动处理任务"""
        if provider_id not in self._processing_tasks:
            task = asyncio.create_task(self._process_provider_queue(provider_id))
            self._processing_tasks[provider_id] = task
            logger.debug(f"Started processor for provider: {provider_id}")
    
    async def add_requests(self, 
                          requests: List[APIRequest], 
                          provider_id: str,
                          priority: Priority = Priority.NORMAL) -> List[APIResponse]:
        """添加请求到批处理队列"""
        if not requests:
            return []
        
        # 确保提供商处理器已启动
        await self._start_provider_processor(provider_id)
        
        # 创建优先级请求
        prioritized_requests = [
            PrioritizedRequest(
                request=req,
                priority=priority,
                timestamp=time.time()
            ) for req in requests
        ]
        
        # 添加到队列
        async with self._queue_locks[provider_id]:
            self._request_queues[provider_id].extend(prioritized_requests)
            # 按优先级排序
            if self.config.priority_enabled:
                self._request_queues[provider_id].sort()
        
        logger.debug(f"Added {len(requests)} requests to {provider_id} queue (priority: {priority.name})")
        
        # 创建响应占位符
        response_futures = [asyncio.Future() for _ in requests]
        
        # 将future与请求关联
        for req, future in zip(prioritized_requests, response_futures):
            req.response_future = future
        
        # 等待所有响应
        try:
            responses = await asyncio.gather(*response_futures, timeout=60.0)
            return responses
        except asyncio.TimeoutError:
            logger.error(f"Batch request timeout for provider: {provider_id}")
            # 返回超时错误响应
            return [
                APIResponse(
                    request_id=req.request.request_id,
                    content="",
                    success=False,
                    error_message="Batch processing timeout",
                    execution_time=60.0
                ) for req in prioritized_requests
            ]
    
    async def _process_provider_queue(self, provider_id: str):
        """处理提供商队列"""
        logger.info(f"Started queue processor for provider: {provider_id}")
        
        while not self._shutdown_event.is_set():
            try:
                # 获取批次
                batch = await self._get_next_batch(provider_id)
                
                if not batch:
                    await asyncio.sleep(0.1)  # 短暂等待
                    continue
                
                # 处理批次
                await self._process_batch(provider_id, batch)
                
                # 内存管理
                await self.memory_monitor.periodic_gc()
                
            except Exception as e:
                logger.error(f"Error in provider queue processor {provider_id}: {e}")
                await asyncio.sleep(1.0)  # 错误后等待
        
        logger.info(f"Queue processor stopped for provider: {provider_id}")
    
    async def _get_next_batch(self, provider_id: str) -> List[PrioritizedRequest]:
        """获取下一个批次"""
        batch = []
        wait_start_time = time.time()
        
        while len(batch) < self.config.max_batch_size:
            async with self._queue_locks[provider_id]:
                queue = self._request_queues[provider_id]
                
                if not queue:
                    # 队列为空
                    if batch and len(batch) >= self.config.min_batch_size:
                        break  # 已有足够的请求
                    elif batch and (time.time() - wait_start_time) > self.config.max_wait_time:
                        break  # 等待时间过长
                    else:
                        # 继续等待
                        pass
                else:
                    # 从队列中取出请求
                    batch_size = self._calculate_optimal_batch_size(provider_id)
                    take_count = min(batch_size - len(batch), len(queue))
                    
                    for _ in range(take_count):
                        if queue:
                            batch.append(queue.pop(0))
            
            # 检查批次条件
            if self._should_process_batch(batch, wait_start_time):
                break
            
            await asyncio.sleep(0.01)  # 短暂等待
        
        return batch
    
    def _calculate_optimal_batch_size(self, provider_id: str) -> int:
        """计算最优批次大小"""
        if self.config.strategy == BatchStrategy.FIXED_SIZE:
            return self.config.max_batch_size
        
        elif self.config.strategy == BatchStrategy.ADAPTIVE_SIZE:
            # 基于历史性能调整
            current_size = self._adaptive_batch_sizes[provider_id]
            history = self._performance_history[provider_id]
            
            if len(history) >= 5:
                # 分析最近的性能
                recent_performance = list(history)[-5:]
                avg_throughput = sum(p['throughput'] for p in recent_performance) / len(recent_performance)
                
                # 如果吞吐量好，增加批次大小
                if avg_throughput > 10.0:  # 每秒10个请求
                    current_size = min(self.config.max_batch_size, int(current_size * self.config.adaptive_factor))
                elif avg_throughput < 5.0:  # 每秒5个请求
                    current_size = max(self.config.min_batch_size, int(current_size / self.config.adaptive_factor))
            
            self._adaptive_batch_sizes[provider_id] = current_size
            return current_size
        
        elif self.config.strategy == BatchStrategy.MEMORY_AWARE:
            # 基于内存使用调整
            if self.memory_monitor.is_memory_pressure():
                return self.config.min_batch_size
            else:
                return self.config.max_batch_size
        
        elif self.config.strategy == BatchStrategy.HYBRID:
            # 混合策略
            adaptive_size = self._calculate_optimal_batch_size_adaptive(provider_id)
            memory_size = self.config.min_batch_size if self.memory_monitor.is_memory_pressure() else self.config.max_batch_size
            return min(adaptive_size, memory_size)
        
        else:
            return self.config.max_batch_size
    
    def _calculate_optimal_batch_size_adaptive(self, provider_id: str) -> int:
        """自适应批次大小计算"""
        current_size = self._adaptive_batch_sizes[provider_id]
        history = self._performance_history[provider_id]
        
        if len(history) >= 3:
            recent_performance = list(history)[-3:]
            avg_response_time = sum(p['avg_response_time'] for p in recent_performance) / len(recent_performance)
            
            # 基于响应时间调整
            if avg_response_time < 1.0:  # 响应快，增加批次
                current_size = min(self.config.max_batch_size, int(current_size * 1.1))
            elif avg_response_time > 3.0:  # 响应慢，减少批次
                current_size = max(self.config.min_batch_size, int(current_size * 0.9))
        
        return current_size
    
    def _should_process_batch(self, batch: List[PrioritizedRequest], wait_start_time: float) -> bool:
        """判断是否应该处理批次"""
        if not batch:
            return False
        
        # 检查批次大小
        if len(batch) >= self.config.max_batch_size:
            return True
        
        # 检查等待时间
        if (time.time() - wait_start_time) > self.config.max_wait_time:
            return True
        
        # 检查最小批次大小
        if len(batch) >= self.config.min_batch_size:
            # 检查是否有高优先级请求
            if self.config.priority_enabled:
                high_priority_count = sum(1 for req in batch if req.priority.value >= Priority.HIGH.value)
                if high_priority_count > 0:
                    return True
        
        # 检查内存压力
        if self.memory_monitor.is_memory_pressure() and len(batch) >= self.config.min_batch_size:
            return True
        
        return False
    
    async def _process_batch(self, provider_id: str, batch: List[PrioritizedRequest]):
        """处理批次"""
        if not batch:
            return
        
        batch_start_time = time.time()
        wait_time = batch_start_time - batch[0].timestamp
        
        logger.debug(f"Processing batch for {provider_id}: size={len(batch)}, wait_time={wait_time:.2f}s")
        
        try:
            # 提取请求
            requests = [req.request for req in batch]
            
            # 去重处理
            if self.deduplicator:
                unique_requests, duplicate_map = self.deduplicator.deduplicate_requests(requests)
                logger.debug(f"Deduplication: {len(requests)} -> {len(unique_requests)} requests")
            else:
                unique_requests = requests
                duplicate_map = {i: [i] for i in range(len(requests))}
            
            # 压缩处理
            compressed_requests, compression_ratio = self.compressor.compress_requests(unique_requests)
            
            # 发送请求
            client = self.api_client_manager.get_client(provider_id)
            if not client:
                raise BatchProcessingError(f"No client found for provider: {provider_id}")
            
            processing_start_time = time.time()
            unique_responses = await client.batch_requests_enhanced(compressed_requests)
            processing_time = time.time() - processing_start_time
            
            # 重建完整响应列表
            responses = self._rebuild_responses(unique_responses, duplicate_map)
            
            # 设置响应
            for req, response in zip(batch, responses):
                if hasattr(req, 'response_future') and not req.response_future.done():
                    req.response_future.set_result(response)
            
            # 更新指标
            self.metrics.update_batch_metrics(len(batch), wait_time, processing_time)
            self.metrics.compression_ratio = compression_ratio
            if self.deduplicator:
                self.metrics.deduplication_ratio = self.deduplicator.get_deduplication_ratio()
            
            # 记录性能历史
            performance_record = {
                'timestamp': time.time(),
                'batch_size': len(batch),
                'wait_time': wait_time,
                'processing_time': processing_time,
                'throughput': len(batch) / processing_time if processing_time > 0 else 0,
                'avg_response_time': processing_time / len(batch) if len(batch) > 0 else 0,
                'success_rate': sum(1 for r in responses if r.success) / len(responses) if responses else 0
            }
            self._performance_history[provider_id].append(performance_record)
            
            logger.debug(f"Batch processed for {provider_id}: {len(batch)} requests in {processing_time:.2f}s")
            
        except Exception as e:
            logger.error(f"Batch processing failed for {provider_id}: {e}")
            
            # 设置错误响应
            error_response = APIResponse(
                request_id="batch_error",
                content="",
                success=False,
                error_message=str(e),
                execution_time=time.time() - batch_start_time
            )
            
            for req in batch:
                if hasattr(req, 'response_future') and not req.response_future.done():
                    req.response_future.set_result(error_response)
    
    def _rebuild_responses(self, unique_responses: List[APIResponse], duplicate_map: Dict) -> List[APIResponse]:
        """重建完整的响应列表"""
        if not duplicate_map:
            return unique_responses
        
        # 如果没有去重，直接返回
        if len(duplicate_map) == len(unique_responses):
            return unique_responses
        
        # 重建响应
        responses = [None] * sum(len(indices) for indices in duplicate_map.values())
        
        unique_index = 0
        for request_hash, original_indices in duplicate_map.items():
            if unique_index < len(unique_responses):
                response = unique_responses[unique_index]
                for original_index in original_indices:
                    responses[original_index] = response
                unique_index += 1
        
        return [r for r in responses if r is not None]
    
    def get_metrics(self) -> BatchMetrics:
        """获取批处理指标"""
        # 更新内存使用
        self.metrics.memory_usage_mb = self.memory_monitor.get_memory_usage()
        return self.metrics
    
    def get_performance_summary(self) -> Dict:
        """获取性能摘要"""
        metrics = self.get_metrics()
        
        summary = {
            'strategy': self.config.strategy.value,
            'total_batches': metrics.total_batches,
            'total_requests': metrics.total_requests,
            'avg_batch_size': metrics.avg_batch_size,
            'avg_wait_time': metrics.avg_wait_time,
            'avg_processing_time': metrics.avg_processing_time,
            'throughput_rps': metrics.throughput_rps,
            'memory_usage_mb': metrics.memory_usage_mb,
            'memory_peak_mb': self.memory_monitor.peak_usage_mb,
            'compression_ratio': metrics.compression_ratio,
            'deduplication_ratio': metrics.deduplication_ratio,
            'adaptive_batch_sizes': dict(self._adaptive_batch_sizes),
            'queue_lengths': {pid: len(queue) for pid, queue in self._request_queues.items()}
        }
        
        # 添加提供商性能历史
        summary['provider_performance'] = {}
        for provider_id, history in self._performance_history.items():
            if history:
                recent = list(history)[-10:]  # 最近10次
                summary['provider_performance'][provider_id] = {
                    'avg_throughput': sum(p['throughput'] for p in recent) / len(recent),
                    'avg_response_time': sum(p['avg_response_time'] for p in recent) / len(recent),
                    'avg_success_rate': sum(p['success_rate'] for p in recent) / len(recent),
                    'batch_count': len(history)
                }
        
        return summary
    
    async def __aenter__(self):
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.stop()