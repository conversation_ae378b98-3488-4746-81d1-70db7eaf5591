"""
高性能优化的AI API客户端
专注于提升大模型请求速度和并发性能
"""

import asyncio
import time
import json
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from collections import defaultdict
import aiohttp
import ssl

from ..models.core import Provider, ModelConfig, APIRequest, APIResponse
from ..utils.exceptions import APIError


@dataclass
class PerformanceMetrics:
    """性能指标"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    total_time: float = 0.0
    avg_response_time: float = 0.0
    requests_per_second: float = 0.0
    concurrent_peak: int = 0


class OptimizedAPIClient:
    """高性能优化的API客户端"""
    
    def __init__(self, 
                 provider: Provider, 
                 max_concurrent: int = 150,  # 进一步提升并发数
                 timeout: int = 60,  # 增加超时时间
                 connection_pool_size: int = 150,  # 连接池大小
                 enable_http2: bool = True,  # 启用HTTP/2
                 retry_config: Optional[Dict] = None):
        """
        初始化优化的API客户端
        
        参数:
            provider: 提供商配置
            max_concurrent: 最大并发请求数（进一步提升）
            timeout: 请求超时时间
            connection_pool_size: 连接池大小
            enable_http2: 是否启用HTTP/2
            retry_config: 重试配置
        """
        self.provider = provider
        self.max_concurrent = max_concurrent
        self.timeout = aiohttp.ClientTimeout(total=timeout, connect=10)
        self.connection_pool_size = connection_pool_size
        self.enable_http2 = enable_http2
        
        # 优化的重试配置
        self.retry_config = retry_config or {
            'max_retries': 2,  # 减少重试次数
            'base_delay': 0.1,  # 减少基础延迟
            'max_delay': 2.0,   # 限制最大延迟
            'backoff_factor': 1.5,  # 减少退避因子
            'retryable_status_codes': {429, 500, 502, 503, 504}
        }
        
        self._session: Optional[aiohttp.ClientSession] = None
        self._semaphore = asyncio.Semaphore(max_concurrent)
        self._metrics = PerformanceMetrics()
        self._active_requests = 0
        self._request_times = []
        
    async def __aenter__(self):
        await self._ensure_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()
    
    async def _ensure_session(self):
        """创建优化的HTTP会话"""
        if self._session is None or self._session.closed:
            headers = {
                'Authorization': f'Bearer {self.provider.api_key}',
                'Content-Type': 'application/json',
                'User-Agent': 'AI-Model-Evaluation-Optimized/2.0',
                'Connection': 'keep-alive',
                'Keep-Alive': 'timeout=60, max=1000'
            }
            
            # 优化的SSL配置
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE
            
            # 优化的连接器配置（兼容不同aiohttp版本）
            connector_kwargs = {
                'limit': self.connection_pool_size,  # 总连接池大小
                'limit_per_host': self.connection_pool_size,  # 每个主机的连接数
                'ttl_dns_cache': 300,  # DNS缓存TTL
                'use_dns_cache': True,  # 启用DNS缓存
                'ssl': ssl_context,
                'enable_cleanup_closed': True,  # 自动清理关闭的连接
                'keepalive_timeout': 60,  # 保持连接时间
                'force_close': False,  # 不强制关闭连接
            }
            
            # 只有在aiohttp支持时才添加http2参数
            try:
                import inspect
                sig = inspect.signature(aiohttp.TCPConnector.__init__)
                if 'http2' in sig.parameters:
                    connector_kwargs['http2'] = self.enable_http2
            except:
                # 如果检查失败，忽略http2参数
                pass
            
            connector = aiohttp.TCPConnector(**connector_kwargs)
            
            self._session = aiohttp.ClientSession(
                headers=headers,
                timeout=self.timeout,
                connector=connector,
                json_serialize=json.dumps  # 使用标准JSON序列化
            )
    
    async def close(self):
        """关闭会话"""
        if self._session and not self._session.closed:
            await self._session.close()
            self._session = None
    
    async def batch_requests_optimized(self, requests: List[APIRequest]) -> List[APIResponse]:
        """
        优化的批量请求处理
        
        参数:
            requests: API请求列表
            
        返回:
            API响应列表
        """
        if not requests:
            return []
        
        await self._ensure_session()
        
        # 重置指标
        start_time = time.time()
        self._metrics.total_requests = len(requests)
        
        # 创建任务组，实现真正的并发
        tasks = []
        for request in requests:
            task = asyncio.create_task(self._send_request_with_metrics(request))
            tasks.append(task)
        
        # 使用gather实现最大并发
        responses = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常响应
        result = []
        for i, response in enumerate(responses):
            if isinstance(response, Exception):
                error_response = APIResponse(
                    request_id=requests[i].request_id,
                    content="",
                    success=False,
                    error_message=str(response),
                    execution_time=0.0
                )
                result.append(error_response)
            else:
                result.append(response)
        
        # 更新性能指标
        total_time = time.time() - start_time
        self._update_metrics(total_time)
        
        return result
    
    async def _send_request_with_metrics(self, request: APIRequest) -> APIResponse:
        """带性能指标的请求发送"""
        async with self._semaphore:
            self._active_requests += 1
            self._metrics.concurrent_peak = max(self._metrics.concurrent_peak, self._active_requests)
            
            try:
                response = await self._send_single_request_optimized(request)
                if response.success:
                    self._metrics.successful_requests += 1
                else:
                    self._metrics.failed_requests += 1
                return response
            finally:
                self._active_requests -= 1
    
    async def _send_single_request_optimized(self, request: APIRequest) -> APIResponse:
        """
        优化的单个请求发送
        
        参数:
            request: API请求
            
        返回:
            API响应
        """
        start_time = time.time()
        last_exception = None
        
        for attempt in range(self.retry_config['max_retries'] + 1):
            try:
                # 准备请求数据
                request_data = request.to_openai_format()
                url = f"{self.provider.base_url}chat/completions"
                
                # 发送请求
                async with self._session.post(url, json=request_data) as response:
                    execution_time = time.time() - start_time
                    self._request_times.append(execution_time)
                    
                    if response.status == 200:
                        response_data = await response.json()
                        return APIResponse.from_openai_response(
                            request_id=request.request_id,
                            response_data=response_data,
                            execution_time=execution_time
                        )
                    elif response.status in self.retry_config['retryable_status_codes'] and attempt < self.retry_config['max_retries']:
                        # 可重试的错误
                        error_text = await response.text()
                        last_exception = APIError(f"HTTP {response.status}: {error_text}", request.model.id)
                        
                        # 智能延迟：根据错误类型调整
                        if response.status == 429:  # 速率限制
                            delay = min(self.retry_config['base_delay'] * (2 ** attempt), self.retry_config['max_delay'])
                        else:
                            delay = min(self.retry_config['base_delay'] * (self.retry_config['backoff_factor'] ** attempt), 
                                      self.retry_config['max_delay'])
                        
                        await asyncio.sleep(delay)
                        continue
                    else:
                        # 不可重试的错误
                        error_text = await response.text()
                        return APIResponse(
                            request_id=request.request_id,
                            content="",
                            success=False,
                            error_message=f"HTTP {response.status}: {error_text}",
                            execution_time=execution_time
                        )
            
            except asyncio.TimeoutError:
                execution_time = time.time() - start_time
                if attempt < self.retry_config['max_retries']:
                    last_exception = APIError(f"Request timeout after {execution_time:.1f}s", request.model.id)
                    await asyncio.sleep(self.retry_config['base_delay'])
                    continue
                else:
                    return APIResponse(
                        request_id=request.request_id,
                        content="",
                        success=False,
                        error_message=f"Request timeout after {execution_time:.1f}s",
                        execution_time=execution_time
                    )
            
            except aiohttp.ClientError as e:
                execution_time = time.time() - start_time
                if attempt < self.retry_config['max_retries']:
                    last_exception = APIError(f"Network error: {e}", request.model.id)
                    await asyncio.sleep(self.retry_config['base_delay'])
                    continue
                else:
                    return APIResponse(
                        request_id=request.request_id,
                        content="",
                        success=False,
                        error_message=f"Network error: {e}",
                        execution_time=execution_time
                    )
        
        # 如果所有重试都失败了
        execution_time = time.time() - start_time
        if last_exception:
            return APIResponse(
                request_id=request.request_id,
                content="",
                success=False,
                error_message=str(last_exception),
                execution_time=execution_time
            )
        
        return APIResponse(
            request_id=request.request_id,
            content="",
            success=False,
            error_message="Unknown error occurred",
            execution_time=execution_time
        )
    
    def _update_metrics(self, total_time: float):
        """更新性能指标"""
        self._metrics.total_time = total_time
        if self._request_times:
            self._metrics.avg_response_time = sum(self._request_times) / len(self._request_times)
        if total_time > 0:
            self._metrics.requests_per_second = self._metrics.total_requests / total_time
    
    def get_performance_metrics(self) -> PerformanceMetrics:
        """获取性能指标"""
        return self._metrics
    
    def get_performance_summary(self) -> Dict:
        """获取性能摘要"""
        return {
            'total_requests': self._metrics.total_requests,
            'successful_requests': self._metrics.successful_requests,
            'failed_requests': self._metrics.failed_requests,
            'success_rate': self._metrics.successful_requests / max(self._metrics.total_requests, 1),
            'total_time': self._metrics.total_time,
            'avg_response_time': self._metrics.avg_response_time,
            'requests_per_second': self._metrics.requests_per_second,
            'concurrent_peak': self._metrics.concurrent_peak,
            'provider': self.provider.name
        }


class HighPerformanceAPIClientManager:
    """高性能API客户端管理器"""
    
    def __init__(self, global_max_concurrent: int = 800, config_manager=None):
        """
        初始化高性能客户端管理器
        
        参数:
            global_max_concurrent: 全局最大并发数
            config_manager: 配置管理器实例
        """
        self.global_max_concurrent = global_max_concurrent
        self.config_manager = config_manager
        self._clients: Dict[str, OptimizedAPIClient] = {}
        self._global_semaphore = asyncio.Semaphore(global_max_concurrent)
        self._performance_stats = defaultdict(list)
    
    def add_client(self, provider: Provider, **kwargs) -> OptimizedAPIClient:
        """添加优化的客户端"""
        # 根据提供商调整并发数
        provider_concurrent = self._get_provider_optimal_concurrent(provider.id)
        
        client = OptimizedAPIClient(
            provider, 
            max_concurrent=provider_concurrent,
            **kwargs
        )
        self._clients[provider.id] = client
        return client
    
    def _get_provider_optimal_concurrent(self, provider_id: str) -> int:
        """根据提供商获取最优并发数"""
        # 如果有配置管理器，从配置文件读取
        if self.config_manager:
            return self.config_manager.get_provider_max_concurrent(provider_id)
        
        # 否则使用提升后的默认配置
        provider_configs = {
            'openai': 120,
            'zhipu': 100,
            'tongyi': 120,
            'hunyuan': 80,
            'baidu': 80,
            'huoshan': 100,
            'default': 80
        }
        return provider_configs.get(provider_id, provider_configs['default'])
    
    async def batch_requests_across_providers(self, 
                                            requests_by_provider: Dict[str, List[APIRequest]]) -> Dict[str, List[APIResponse]]:
        """
        跨提供商的批量请求处理
        
        参数:
            requests_by_provider: 按提供商分组的请求
            
        返回:
            按提供商分组的响应
        """
        tasks = {}
        
        # 为每个提供商创建任务
        for provider_id, requests in requests_by_provider.items():
            if provider_id in self._clients and requests:
                client = self._clients[provider_id]
                task = asyncio.create_task(client.batch_requests_optimized(requests))
                tasks[provider_id] = task
        
        # 等待所有提供商的请求完成
        results = {}
        completed_tasks = await asyncio.gather(*tasks.values(), return_exceptions=True)
        
        for (provider_id, task), result in zip(tasks.items(), completed_tasks):
            if isinstance(result, Exception):
                # 处理提供商级别的错误
                results[provider_id] = [
                    APIResponse(
                        request_id=f"error_{i}",
                        content="",
                        success=False,
                        error_message=str(result),
                        execution_time=0.0
                    ) for i in range(len(requests_by_provider[provider_id]))
                ]
            else:
                results[provider_id] = result
        
        return results
    
    def get_client(self, provider_id: str) -> Optional[OptimizedAPIClient]:
        """获取客户端"""
        return self._clients.get(provider_id)
    
    async def close_all(self):
        """关闭所有客户端"""
        for client in self._clients.values():
            await client.close()
        self._clients.clear()
    
    def get_global_performance_summary(self) -> Dict:
        """获取全局性能摘要"""
        summaries = {}
        total_requests = 0
        total_successful = 0
        total_time = 0
        
        for provider_id, client in self._clients.items():
            summary = client.get_performance_summary()
            summaries[provider_id] = summary
            total_requests += summary['total_requests']
            total_successful += summary['successful_requests']
            total_time = max(total_time, summary['total_time'])
        
        return {
            'provider_summaries': summaries,
            'global_stats': {
                'total_requests': total_requests,
                'total_successful': total_successful,
                'global_success_rate': total_successful / max(total_requests, 1),
                'total_time': total_time,
                'global_requests_per_second': total_requests / max(total_time, 0.001)
            }
        }