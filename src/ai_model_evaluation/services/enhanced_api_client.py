"""增强版高性能API客户端管理器
专注于极致性能优化和智能并发控制
"""

import asyncio
import time
import json
import gc
from typing import Dict, List, Optional, Tuple, Set
from dataclasses import dataclass, field
from collections import defaultdict, deque
import aiohttp
import ssl
import weakref
from contextlib import asynccontextmanager
from loguru import logger

from ..models.core import Provider, ModelConfig, APIRequest, APIResponse
from ..utils.exceptions import APIError


@dataclass
class EnhancedPerformanceMetrics:
    """增强版性能指标"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    timeout_requests: int = 0
    retry_requests: int = 0
    total_time: float = 0.0
    avg_response_time: float = 0.0
    min_response_time: float = float('inf')
    max_response_time: float = 0.0
    requests_per_second: float = 0.0
    concurrent_peak: int = 0
    current_concurrent: int = 0
    connection_pool_usage: float = 0.0
    memory_usage_mb: float = 0.0
    error_rate: float = 0.0
    response_times: deque = field(default_factory=lambda: deque(maxlen=1000))
    
    def update_response_time(self, response_time: float):
        """更新响应时间统计"""
        self.response_times.append(response_time)
        self.min_response_time = min(self.min_response_time, response_time)
        self.max_response_time = max(self.max_response_time, response_time)
        
        if self.response_times:
            self.avg_response_time = sum(self.response_times) / len(self.response_times)
    
    def get_percentiles(self) -> Dict[str, float]:
        """获取响应时间百分位数"""
        if not self.response_times:
            return {}
        
        sorted_times = sorted(self.response_times)
        length = len(sorted_times)
        
        return {
            'p50': sorted_times[int(length * 0.5)],
            'p90': sorted_times[int(length * 0.9)],
            'p95': sorted_times[int(length * 0.95)],
            'p99': sorted_times[int(length * 0.99)]
        }


class AdaptiveConcurrencyController:
    """自适应并发控制器"""
    
    def __init__(self, initial_limit: int = 100, min_limit: int = 10, max_limit: int = 500):
        self.current_limit = initial_limit
        self.min_limit = min_limit
        self.max_limit = max_limit
        self.success_count = 0
        self.error_count = 0
        self.last_adjustment = time.time()
        self.adjustment_interval = 10.0  # 10秒调整一次
        self.semaphore = asyncio.Semaphore(initial_limit)
        
    async def acquire(self):
        """获取并发许可"""
        await self.semaphore.acquire()
        
    def release(self, success: bool = True):
        """释放并发许可并记录结果"""
        self.semaphore.release()
        
        if success:
            self.success_count += 1
        else:
            self.error_count += 1
        
        # 定期调整并发限制
        if time.time() - self.last_adjustment > self.adjustment_interval:
            self._adjust_concurrency()
    
    def _adjust_concurrency(self):
        """调整并发限制"""
        total_requests = self.success_count + self.error_count
        if total_requests == 0:
            return
        
        error_rate = self.error_count / total_requests
        
        # 根据错误率调整并发数
        if error_rate < 0.01:  # 错误率低于1%，增加并发
            new_limit = min(self.max_limit, int(self.current_limit * 1.2))
        elif error_rate > 0.05:  # 错误率高于5%，减少并发
            new_limit = max(self.min_limit, int(self.current_limit * 0.8))
        else:
            new_limit = self.current_limit
        
        if new_limit != self.current_limit:
            logger.info(f"Adjusting concurrency from {self.current_limit} to {new_limit} (error_rate: {error_rate:.2%})")
            self.current_limit = new_limit
            self.semaphore = asyncio.Semaphore(new_limit)
        
        # 重置计数器
        self.success_count = 0
        self.error_count = 0
        self.last_adjustment = time.time()


class EnhancedAPIClient:
    """增强版高性能API客户端"""
    
    def __init__(self, 
                 provider: Provider,
                 initial_concurrent: int = 100,
                 max_concurrent: int = 300,
                 timeout: int = 60,
                 connection_pool_size: int = 200,
                 enable_http2: bool = True,
                 enable_adaptive_concurrency: bool = True,
                 retry_config: Optional[Dict] = None):
        """初始化增强版API客户端"""
        self.provider = provider
        self.timeout = aiohttp.ClientTimeout(total=timeout, connect=15)
        self.connection_pool_size = connection_pool_size
        self.enable_http2 = enable_http2
        self.enable_adaptive_concurrency = enable_adaptive_concurrency
        
        # 自适应并发控制
        if enable_adaptive_concurrency:
            self.concurrency_controller = AdaptiveConcurrencyController(
                initial_limit=initial_concurrent,
                min_limit=max(10, initial_concurrent // 10),
                max_limit=max_concurrent
            )
        else:
            self.concurrency_controller = None
            self._semaphore = asyncio.Semaphore(initial_concurrent)
        
        # 优化的重试配置
        self.retry_config = retry_config or {
            'max_retries': 3,
            'base_delay': 0.1,
            'max_delay': 5.0,
            'backoff_factor': 2.0,
            'jitter': True,
            'retryable_status_codes': {429, 500, 502, 503, 504},
            'retryable_exceptions': (asyncio.TimeoutError, aiohttp.ClientError)
        }
        
        self._session: Optional[aiohttp.ClientSession] = None
        self._metrics = EnhancedPerformanceMetrics()
        self._active_requests = 0
        self._connection_pool_monitor = None
        
        # 内存管理
        self._request_cache = weakref.WeakValueDictionary()
        self._last_gc = time.time()
        self._gc_interval = 60.0  # 60秒执行一次垃圾回收
        
        logger.info(f"EnhancedAPIClient initialized for {provider.id}: concurrent={initial_concurrent}, pool_size={connection_pool_size}")
    
    async def __aenter__(self):
        await self._ensure_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()
    
    async def _ensure_session(self):
        """创建优化的HTTP会话"""
        if self._session is None or self._session.closed:
            headers = {
                'Authorization': f'Bearer {self.provider.api_key}',
                'Content-Type': 'application/json',
                'User-Agent': 'AI-Model-Evaluation-Enhanced/3.0',
                'Connection': 'keep-alive',
                'Keep-Alive': 'timeout=120, max=2000'
            }
            
            # 优化的SSL配置
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE
            ssl_context.set_ciphers('ECDHE+AESGCM:ECDHE+CHACHA20:DHE+AESGCM:DHE+CHACHA20:!aNULL:!MD5:!DSS')
            
            # 增强的连接器配置
            connector_kwargs = {
                'limit': self.connection_pool_size,
                'limit_per_host': self.connection_pool_size,
                'ttl_dns_cache': 600,  # 增加DNS缓存时间
                'use_dns_cache': True,
                'ssl': ssl_context,
                'enable_cleanup_closed': True,
                'keepalive_timeout': 120,  # 增加保持连接时间
                'force_close': False,
                'happy_eyeballs_delay': 0.25,  # IPv6/IPv4双栈优化
            }
            
            # HTTP/2支持检测
            try:
                import inspect
                sig = inspect.signature(aiohttp.TCPConnector.__init__)
                if 'http2' in sig.parameters and self.enable_http2:
                    connector_kwargs['http2'] = True
                    logger.info(f"HTTP/2 enabled for {self.provider.id}")
            except Exception as e:
                logger.debug(f"HTTP/2 detection failed: {e}")
            
            connector = aiohttp.TCPConnector(**connector_kwargs)
            
            self._session = aiohttp.ClientSession(
                headers=headers,
                timeout=self.timeout,
                connector=connector,
                json_serialize=json.dumps,
                raise_for_status=False  # 手动处理状态码
            )
            
            # 启动连接池监控
            if self._connection_pool_monitor is None:
                self._connection_pool_monitor = asyncio.create_task(self._monitor_connection_pool())
    
    async def _monitor_connection_pool(self):
        """监控连接池使用情况"""
        while self._session and not self._session.closed:
            try:
                if hasattr(self._session.connector, '_conns'):
                    total_connections = sum(len(conns) for conns in self._session.connector._conns.values())
                    usage_rate = total_connections / self.connection_pool_size
                    self._metrics.connection_pool_usage = usage_rate
                    
                    if usage_rate > 0.9:
                        logger.warning(f"High connection pool usage: {usage_rate:.1%} for {self.provider.id}")
                
                await asyncio.sleep(30)  # 每30秒检查一次
            except Exception as e:
                logger.debug(f"Connection pool monitoring error: {e}")
                await asyncio.sleep(60)
    
    async def close(self):
        """关闭会话和清理资源"""
        if self._connection_pool_monitor:
            self._connection_pool_monitor.cancel()
            self._connection_pool_monitor = None
        
        if self._session and not self._session.closed:
            await self._session.close()
            self._session = None
        
        # 清理缓存
        self._request_cache.clear()
        
        # 强制垃圾回收
        gc.collect()
        
        logger.info(f"EnhancedAPIClient closed for {self.provider.id}")
    
    async def batch_requests_enhanced(self, requests: List[APIRequest]) -> List[APIResponse]:
        """增强版批量请求处理"""
        if not requests:
            return []
        
        await self._ensure_session()
        
        start_time = time.time()
        self._metrics.total_requests += len(requests)
        
        # 创建任务并发执行
        tasks = []
        for request in requests:
            task = asyncio.create_task(self._send_request_with_enhanced_metrics(request))
            tasks.append(task)
        
        # 使用gather执行所有任务
        responses = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常响应
        result = []
        for i, response in enumerate(responses):
            if isinstance(response, Exception):
                error_response = APIResponse(
                    request_id=requests[i].request_id,
                    content="",
                    success=False,
                    error_message=str(response),
                    execution_time=0.0
                )
                result.append(error_response)
                self._metrics.failed_requests += 1
            else:
                result.append(response)
                if response.success:
                    self._metrics.successful_requests += 1
                else:
                    self._metrics.failed_requests += 1
        
        # 更新性能指标
        total_time = time.time() - start_time
        self._update_enhanced_metrics(total_time)
        
        # 定期垃圾回收
        await self._periodic_gc()
        
        return result
    
    async def _send_request_with_enhanced_metrics(self, request: APIRequest) -> APIResponse:
        """发送请求并记录增强指标"""
        start_time = time.time()
        
        # 并发控制
        if self.enable_adaptive_concurrency and self.concurrency_controller:
            await self.concurrency_controller.acquire()
        else:
            await self._semaphore.acquire()
        
        try:
            self._active_requests += 1
            self._metrics.current_concurrent = self._active_requests
            self._metrics.concurrent_peak = max(self._metrics.concurrent_peak, self._active_requests)
            
            response = await self._send_single_request_enhanced(request)
            
            execution_time = time.time() - start_time
            self._metrics.update_response_time(execution_time)
            
            # 释放并发控制
            if self.enable_adaptive_concurrency and self.concurrency_controller:
                self.concurrency_controller.release(response.success)
            else:
                self._semaphore.release()
            
            return response
            
        except Exception as e:
            execution_time = time.time() - start_time
            
            # 释放并发控制
            if self.enable_adaptive_concurrency and self.concurrency_controller:
                self.concurrency_controller.release(False)
            else:
                self._semaphore.release()
            
            logger.error(f"Request failed for {self.provider.id}: {e}")
            return APIResponse(
                request_id=request.request_id,
                content="",
                success=False,
                error_message=str(e),
                execution_time=execution_time
            )
        finally:
            self._active_requests -= 1
            self._metrics.current_concurrent = self._active_requests
    
    async def _send_single_request_enhanced(self, request: APIRequest) -> APIResponse:
        """发送单个增强请求"""
        last_exception = None
        
        for attempt in range(self.retry_config['max_retries'] + 1):
            try:
                start_time = time.time()
                
                # 构建请求数据
                request_data = {
                    "model": request.model.id,
                    "messages": [
                        {"role": "user", "content": request.prompt}
                    ],
                    "temperature": request.model.temperature,
                    "max_tokens": request.model.max_tokens
                }
                
                # 发送请求
                async with self._session.post(
                    self.provider.base_url,
                    json=request_data,
                    headers={'X-Request-ID': request.request_id}
                ) as response:
                    execution_time = time.time() - start_time
                    
                    if response.status == 200:
                        response_data = await response.json()
                        content = self._extract_content_from_response(response_data)
                        
                        return APIResponse(
                            request_id=request.request_id,
                            content=content,
                            success=True,
                            error_message=None,
                            execution_time=execution_time
                        )
                    
                    elif response.status in self.retry_config['retryable_status_codes'] and attempt < self.retry_config['max_retries']:
                        # 可重试的错误
                        error_text = await response.text()
                        last_exception = APIError(f"HTTP {response.status}: {error_text}", request.model.id)
                        
                        # 智能延迟策略
                        delay = self._calculate_retry_delay(attempt, response.status)
                        await asyncio.sleep(delay)
                        
                        self._metrics.retry_requests += 1
                        continue
                    
                    else:
                        # 不可重试的错误
                        error_text = await response.text()
                        return APIResponse(
                            request_id=request.request_id,
                            content="",
                            success=False,
                            error_message=f"HTTP {response.status}: {error_text}",
                            execution_time=execution_time
                        )
            
            except asyncio.TimeoutError:
                execution_time = time.time() - start_time
                self._metrics.timeout_requests += 1
                
                if attempt < self.retry_config['max_retries']:
                    last_exception = APIError(f"Request timeout after {execution_time:.1f}s", request.model.id)
                    delay = self._calculate_retry_delay(attempt, 408)  # 408 Request Timeout
                    await asyncio.sleep(delay)
                    continue
                else:
                    return APIResponse(
                        request_id=request.request_id,
                        content="",
                        success=False,
                        error_message=f"Request timeout after {execution_time:.1f}s (max retries exceeded)",
                        execution_time=execution_time
                    )
            
            except Exception as e:
                execution_time = time.time() - start_time
                
                if attempt < self.retry_config['max_retries'] and isinstance(e, self.retry_config['retryable_exceptions']):
                    last_exception = e
                    delay = self._calculate_retry_delay(attempt, 500)
                    await asyncio.sleep(delay)
                    self._metrics.retry_requests += 1
                    continue
                else:
                    return APIResponse(
                        request_id=request.request_id,
                        content="",
                        success=False,
                        error_message=str(e),
                        execution_time=execution_time
                    )
        
        # 所有重试都失败了
        return APIResponse(
            request_id=request.request_id,
            content="",
            success=False,
            error_message=f"Max retries exceeded. Last error: {last_exception}",
            execution_time=0.0
        )
    
    def _calculate_retry_delay(self, attempt: int, status_code: int) -> float:
        """计算智能重试延迟"""
        base_delay = self.retry_config['base_delay']
        backoff_factor = self.retry_config['backoff_factor']
        max_delay = self.retry_config['max_delay']
        
        # 根据状态码调整延迟
        if status_code == 429:  # 速率限制
            delay = min(base_delay * (3 ** attempt), max_delay)
        else:
            delay = min(base_delay * (backoff_factor ** attempt), max_delay)
        
        # 添加抖动以避免雷群效应
        if self.retry_config.get('jitter', True):
            import random
            delay *= (0.5 + random.random() * 0.5)
        
        return delay
    
    def _extract_content_from_response(self, response_data: dict) -> str:
        """从响应数据中提取内容"""
        try:
            if 'choices' in response_data and response_data['choices']:
                choice = response_data['choices'][0]
                if 'message' in choice and 'content' in choice['message']:
                    return choice['message']['content']
                elif 'text' in choice:
                    return choice['text']
            
            # 备用提取方法
            if 'output' in response_data:
                if isinstance(response_data['output'], dict) and 'text' in response_data['output']:
                    return response_data['output']['text']
                elif isinstance(response_data['output'], str):
                    return response_data['output']
            
            return str(response_data)
            
        except Exception as e:
            logger.warning(f"Failed to extract content from response: {e}")
            return str(response_data)
    
    def _update_enhanced_metrics(self, total_time: float):
        """更新增强性能指标"""
        self._metrics.total_time += total_time
        
        if self._metrics.total_requests > 0:
            self._metrics.requests_per_second = self._metrics.total_requests / self._metrics.total_time
            self._metrics.error_rate = self._metrics.failed_requests / self._metrics.total_requests
        
        # 更新内存使用情况
        try:
            import psutil
            import os
            process = psutil.Process(os.getpid())
            self._metrics.memory_usage_mb = process.memory_info().rss / 1024 / 1024
        except ImportError:
            pass
    
    async def _periodic_gc(self):
        """定期垃圾回收"""
        current_time = time.time()
        if current_time - self._last_gc > self._gc_interval:
            gc.collect()
            self._last_gc = current_time
            logger.debug(f"Garbage collection executed for {self.provider.id}")
    
    def get_enhanced_metrics(self) -> EnhancedPerformanceMetrics:
        """获取增强性能指标"""
        return self._metrics
    
    def get_performance_summary(self) -> Dict:
        """获取性能摘要"""
        percentiles = self._metrics.get_percentiles()
        
        return {
            'provider_id': self.provider.id,
            'total_requests': self._metrics.total_requests,
            'successful_requests': self._metrics.successful_requests,
            'failed_requests': self._metrics.failed_requests,
            'timeout_requests': self._metrics.timeout_requests,
            'retry_requests': self._metrics.retry_requests,
            'success_rate': (self._metrics.successful_requests / max(1, self._metrics.total_requests)) * 100,
            'error_rate': self._metrics.error_rate * 100,
            'avg_response_time': self._metrics.avg_response_time,
            'min_response_time': self._metrics.min_response_time if self._metrics.min_response_time != float('inf') else 0,
            'max_response_time': self._metrics.max_response_time,
            'response_time_percentiles': percentiles,
            'requests_per_second': self._metrics.requests_per_second,
            'concurrent_peak': self._metrics.concurrent_peak,
            'current_concurrent': self._metrics.current_concurrent,
            'connection_pool_usage': self._metrics.connection_pool_usage * 100,
            'memory_usage_mb': self._metrics.memory_usage_mb,
            'adaptive_concurrency_limit': self.concurrency_controller.current_limit if self.concurrency_controller else 'disabled'
        }


class EnhancedAPIClientManager:
    """增强版高性能API客户端管理器"""
    
    def __init__(self, 
                 global_max_concurrent: int = 1000,
                 enable_adaptive_concurrency: bool = True,
                 config_manager=None):
        """初始化增强版客户端管理器"""
        self.global_max_concurrent = global_max_concurrent
        self.enable_adaptive_concurrency = enable_adaptive_concurrency
        self.config_manager = config_manager
        self._clients: Dict[str, EnhancedAPIClient] = {}
        self._global_semaphore = asyncio.Semaphore(global_max_concurrent)
        self._performance_stats = defaultdict(list)
        self._client_locks = defaultdict(asyncio.Lock)
        
        logger.info(f"EnhancedAPIClientManager initialized: global_concurrent={global_max_concurrent}, adaptive={enable_adaptive_concurrency}")
    
    def add_client(self, provider: Provider, **kwargs) -> EnhancedAPIClient:
        """添加增强版客户端"""
        # 获取提供商最优并发数
        provider_concurrent = self._get_provider_optimal_concurrent(provider.id)
        
        # 合并配置
        client_config = {
            'initial_concurrent': provider_concurrent,
            'max_concurrent': min(provider_concurrent * 3, 500),  # 最大不超过500
            'enable_adaptive_concurrency': self.enable_adaptive_concurrency,
            **kwargs
        }
        
        client = EnhancedAPIClient(provider, **client_config)
        self._clients[provider.id] = client
        
        logger.info(f"Added enhanced client for {provider.id}: concurrent={provider_concurrent}")
        return client
    
    def _get_provider_optimal_concurrent(self, provider_id: str) -> int:
        """获取提供商最优并发数"""
        if self.config_manager:
            return self.config_manager.get_provider_max_concurrent(provider_id)
        
        # 增强的默认配置
        provider_configs = {
            'openai': 150,
            'zhipu': 120,
            'tongyi': 150,
            'hunyuan': 100,
            'baidu': 100,
            'huoshan': 120,
            'claude': 100,
            'gemini': 120,
            'default': 100
        }
        return provider_configs.get(provider_id, provider_configs['default'])
    
    async def batch_requests_across_providers(self, 
                                            requests_by_provider: Dict[str, List[APIRequest]]) -> Dict[str, List[APIResponse]]:
        """跨提供商的增强批量请求处理"""
        if not requests_by_provider:
            return {}
        
        tasks = {}
        
        # 为每个提供商创建任务
        for provider_id, requests in requests_by_provider.items():
            if provider_id in self._clients and requests:
                client = self._clients[provider_id]
                task = asyncio.create_task(
                    self._process_provider_requests(client, requests, provider_id)
                )
                tasks[provider_id] = task
        
        # 等待所有提供商的请求完成
        results = {}
        completed_tasks = await asyncio.gather(*tasks.values(), return_exceptions=True)
        
        for (provider_id, task), result in zip(tasks.items(), completed_tasks):
            if isinstance(result, Exception):
                logger.error(f"Provider {provider_id} batch request failed: {result}")
                # 创建错误响应
                error_responses = [
                    APIResponse(
                        request_id=f"error_{i}",
                        content="",
                        success=False,
                        error_message=str(result),
                        execution_time=0.0
                    ) for i in range(len(requests_by_provider[provider_id]))
                ]
                results[provider_id] = error_responses
            else:
                results[provider_id] = result
        
        # 记录性能统计
        self._record_performance_stats(results)
        
        return results
    
    async def _process_provider_requests(self, 
                                       client: EnhancedAPIClient, 
                                       requests: List[APIRequest], 
                                       provider_id: str) -> List[APIResponse]:
        """处理单个提供商的请求"""
        async with self._client_locks[provider_id]:
            try:
                return await client.batch_requests_enhanced(requests)
            except Exception as e:
                logger.error(f"Failed to process requests for {provider_id}: {e}")
                raise
    
    def _record_performance_stats(self, results: Dict[str, List[APIResponse]]):
        """记录性能统计"""
        for provider_id, responses in results.items():
            if provider_id in self._clients:
                client = self._clients[provider_id]
                metrics = client.get_enhanced_metrics()
                self._performance_stats[provider_id].append({
                    'timestamp': time.time(),
                    'metrics': metrics
                })
    
    def get_client(self, provider_id: str) -> Optional[EnhancedAPIClient]:
        """获取客户端"""
        return self._clients.get(provider_id)
    
    async def close_all(self):
        """关闭所有客户端"""
        logger.info("Closing all enhanced API clients")
        
        close_tasks = []
        for client in self._clients.values():
            close_tasks.append(asyncio.create_task(client.close()))
        
        if close_tasks:
            await asyncio.gather(*close_tasks, return_exceptions=True)
        
        self._clients.clear()
        self._performance_stats.clear()
        
        # 强制垃圾回收
        gc.collect()
        
        logger.info("All enhanced API clients closed")
    
    def get_global_performance_summary(self) -> Dict:
        """获取全局性能摘要"""
        summary = {
            'total_providers': len(self._clients),
            'global_max_concurrent': self.global_max_concurrent,
            'adaptive_concurrency_enabled': self.enable_adaptive_concurrency,
            'providers': {}
        }
        
        total_requests = 0
        total_successful = 0
        total_failed = 0
        total_response_time = 0.0
        
        for provider_id, client in self._clients.items():
            provider_summary = client.get_performance_summary()
            summary['providers'][provider_id] = provider_summary
            
            total_requests += provider_summary['total_requests']
            total_successful += provider_summary['successful_requests']
            total_failed += provider_summary['failed_requests']
            total_response_time += provider_summary['avg_response_time'] * provider_summary['total_requests']
        
        if total_requests > 0:
            summary['global_stats'] = {
                'total_requests': total_requests,
                'total_successful': total_successful,
                'total_failed': total_failed,
                'global_success_rate': (total_successful / total_requests) * 100,
                'global_error_rate': (total_failed / total_requests) * 100,
                'weighted_avg_response_time': total_response_time / total_requests
            }
        
        return summary
    
    @asynccontextmanager
    async def get_client_context(self, provider_id: str):
        """获取客户端上下文管理器"""
        client = self.get_client(provider_id)
        if client is None:
            raise ValueError(f"No client found for provider: {provider_id}")
        
        try:
            yield client
        finally:
            # 可以在这里添加清理逻辑
            pass