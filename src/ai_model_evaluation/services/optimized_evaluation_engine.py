"""优化的评估引擎
整合所有性能优化组件，提供极致性能的AI模型评估服务
"""

import asyncio
import time
import gc
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from pathlib import Path
import json
from loguru import logger

from ..models.core import Provider, ModelConfig, APIRequest, APIResponse, EvaluationResult
from ..config.manager import ConfigManager
from ..utils.exceptions import EvaluationError, ConfigurationError
from .enhanced_api_client import EnhancedAPIClientManager
from .smart_batch_processor import SmartBatchProcessor, BatchConfig, BatchStrategy, Priority
from .async_file_handler import AsyncFileHandler


@dataclass
class OptimizedEvaluationConfig:
    """优化评估配置"""
    # 并发配置
    global_max_concurrent: int = 1000
    enable_adaptive_concurrency: bool = True
    
    # 批处理配置
    batch_strategy: BatchStrategy = BatchStrategy.HYBRID
    min_batch_size: int = 10
    max_batch_size: int = 100
    max_batch_wait_time: float = 2.0
    
    # 内存配置
    memory_threshold_mb: float = 1000.0
    enable_memory_optimization: bool = True
    gc_interval: float = 60.0
    
    # 文件处理配置
    chunk_size: int = 1000
    enable_streaming: bool = True
    
    # 缓存配置
    enable_request_cache: bool = True
    cache_ttl: int = 3600
    
    # 重试配置
    max_retries: int = 3
    retry_delay: float = 1.0
    
    # 监控配置
    enable_detailed_metrics: bool = True
    metrics_interval: float = 30.0


@dataclass
class EvaluationMetrics:
    """评估指标"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    total_time: float = 0.0
    avg_response_time: float = 0.0
    requests_per_second: float = 0.0
    memory_usage_mb: float = 0.0
    cache_hit_rate: float = 0.0
    batch_efficiency: float = 0.0
    provider_stats: Dict[str, Dict] = None
    
    def __post_init__(self):
        if self.provider_stats is None:
            self.provider_stats = {}


class OptimizedEvaluationEngine:
    """优化的评估引擎"""
    
    def __init__(self, 
                 config_manager: ConfigManager,
                 optimization_config: OptimizedEvaluationConfig = None):
        """初始化优化评估引擎"""
        self.config_manager = config_manager
        self.optimization_config = optimization_config or OptimizedEvaluationConfig()
        
        # 核心组件
        self.api_client_manager: Optional[EnhancedAPIClientManager] = None
        self.batch_processor: Optional[SmartBatchProcessor] = None
        self.file_handler: Optional[AsyncFileHandler] = None
        
        # 缓存
        self._request_cache: Dict[str, APIResponse] = {}
        self._cache_timestamps: Dict[str, float] = {}
        
        # 指标
        self.metrics = EvaluationMetrics()
        self._metrics_task: Optional[asyncio.Task] = None
        
        # 状态
        self._initialized = False
        self._shutdown_event = asyncio.Event()
        
        logger.info("OptimizedEvaluationEngine initialized")
    
    async def initialize(self):
        """初始化评估引擎"""
        if self._initialized:
            return
        
        logger.info("Initializing OptimizedEvaluationEngine...")
        
        try:
            # 初始化API客户端管理器
            await self._initialize_api_clients()
            
            # 初始化批处理器
            await self._initialize_batch_processor()
            
            # 初始化文件处理器
            await self._initialize_file_handler()
            
            # 启动指标监控
            if self.optimization_config.enable_detailed_metrics:
                self._metrics_task = asyncio.create_task(self._metrics_monitor())
            
            self._initialized = True
            logger.info("OptimizedEvaluationEngine initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize OptimizedEvaluationEngine: {e}")
            await self.cleanup()
            raise EvaluationError(f"Initialization failed: {e}")
    
    async def _initialize_api_clients(self):
        """初始化API客户端"""
        self.api_client_manager = EnhancedAPIClientManager(
            global_max_concurrent=self.optimization_config.global_max_concurrent,
            enable_adaptive_concurrency=self.optimization_config.enable_adaptive_concurrency,
            config_manager=self.config_manager
        )
        
        # 添加所有提供商的客户端
        providers = self.config_manager.get_all_providers()
        for provider in providers:
            try:
                self.api_client_manager.add_client(
                    provider,
                    timeout=60,
                    connection_pool_size=200,
                    enable_http2=True,
                    retry_config={
                        'max_retries': self.optimization_config.max_retries,
                        'base_delay': self.optimization_config.retry_delay,
                        'max_delay': 10.0,
                        'backoff_factor': 2.0
                    }
                )
                logger.info(f"Added enhanced client for provider: {provider.id}")
            except Exception as e:
                logger.error(f"Failed to add client for provider {provider.id}: {e}")
    
    async def _initialize_batch_processor(self):
        """初始化批处理器"""
        batch_config = BatchConfig(
            strategy=self.optimization_config.batch_strategy,
            min_batch_size=self.optimization_config.min_batch_size,
            max_batch_size=self.optimization_config.max_batch_size,
            max_wait_time=self.optimization_config.max_batch_wait_time,
            memory_threshold_mb=self.optimization_config.memory_threshold_mb,
            enable_compression=True,
            enable_deduplication=True
        )
        
        self.batch_processor = SmartBatchProcessor(
            config=batch_config,
            api_client_manager=self.api_client_manager
        )
        
        await self.batch_processor.start()
        logger.info("SmartBatchProcessor started")
    
    async def _initialize_file_handler(self):
        """初始化文件处理器"""
        self.file_handler = AsyncFileHandler(
            chunk_size=self.optimization_config.chunk_size,
            enable_streaming=self.optimization_config.enable_streaming,
            memory_threshold_mb=self.optimization_config.memory_threshold_mb
        )
        logger.info("AsyncFileHandler initialized")
    
    async def evaluate_models_optimized(self, 
                                      input_file: str,
                                      output_file: str,
                                      models: List[ModelConfig],
                                      prompt_template: str,
                                      priority: Priority = Priority.NORMAL) -> EvaluationResult:
        """优化的模型评估"""
        if not self._initialized:
            await self.initialize()
        
        start_time = time.time()
        logger.info(f"Starting optimized evaluation: {len(models)} models, input={input_file}")
        
        try:
            # 异步读取输入数据
            input_data = await self.file_handler.read_evaluation_data_async(input_file)
            logger.info(f"Loaded {len(input_data)} evaluation samples")
            
            # 生成所有请求
            all_requests = await self._generate_requests(input_data, models, prompt_template)
            logger.info(f"Generated {len(all_requests)} API requests")
            
            # 按提供商分组请求
            requests_by_provider = self._group_requests_by_provider(all_requests)
            
            # 执行批量评估
            responses_by_provider = await self._execute_batch_evaluation(
                requests_by_provider, priority
            )
            
            # 合并和处理响应
            all_responses = self._merge_responses(responses_by_provider)
            
            # 生成评估结果
            evaluation_results = await self._process_evaluation_results(
                input_data, all_requests, all_responses, models
            )
            
            # 异步写入结果
            await self.file_handler.write_results_async(output_file, evaluation_results)
            
            # 计算最终指标
            total_time = time.time() - start_time
            success_count = sum(1 for r in all_responses if r.success)
            
            result = EvaluationResult(
                total_requests=len(all_requests),
                successful_requests=success_count,
                failed_requests=len(all_requests) - success_count,
                total_time=total_time,
                average_response_time=sum(r.execution_time for r in all_responses) / len(all_responses),
                requests_per_second=len(all_requests) / total_time,
                results_file=output_file
            )
            
            # 更新指标
            self._update_metrics(result)
            
            logger.info(f"Optimized evaluation completed: {success_count}/{len(all_requests)} successful, {total_time:.2f}s")
            return result
            
        except Exception as e:
            logger.error(f"Optimized evaluation failed: {e}")
            raise EvaluationError(f"Evaluation failed: {e}")
    
    async def _generate_requests(self, 
                               input_data: List[Dict],
                               models: List[ModelConfig],
                               prompt_template: str) -> List[APIRequest]:
        """生成API请求"""
        requests = []
        request_id = 0
        
        for sample in input_data:
            for model in models:
                # 格式化提示词
                formatted_prompt = self._format_prompt(prompt_template, sample)
                
                # 检查缓存
                cache_key = self._get_cache_key(model, formatted_prompt)
                if self._is_cache_valid(cache_key):
                    # 使用缓存的响应
                    cached_response = self._request_cache[cache_key]
                    logger.debug(f"Cache hit for model {model.id}")
                    continue
                
                # 创建请求
                request = APIRequest(
                    request_id=f"req_{request_id}",
                    model=model,
                    prompt=formatted_prompt
                )
                requests.append(request)
                request_id += 1
        
        return requests
    
    def _format_prompt(self, template: str, sample: Dict) -> str:
        """格式化提示词"""
        try:
            return template.format(**sample)
        except KeyError as e:
            logger.warning(f"Missing key in sample data: {e}")
            return template
    
    def _get_cache_key(self, model: ModelConfig, prompt: str) -> str:
        """生成缓存键"""
        key_parts = [
            model.id,
            str(model.temperature),
            str(model.max_tokens),
            prompt
        ]
        return hash(tuple(key_parts))
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if not self.optimization_config.enable_request_cache:
            return False
        
        if cache_key not in self._request_cache:
            return False
        
        timestamp = self._cache_timestamps.get(cache_key, 0)
        return (time.time() - timestamp) < self.optimization_config.cache_ttl
    
    def _group_requests_by_provider(self, requests: List[APIRequest]) -> Dict[str, List[APIRequest]]:
        """按提供商分组请求"""
        grouped = {}
        
        for request in requests:
            provider_id = self._get_provider_for_model(request.model.id)
            if provider_id:
                if provider_id not in grouped:
                    grouped[provider_id] = []
                grouped[provider_id].append(request)
        
        return grouped
    
    def _get_provider_for_model(self, model_id: str) -> Optional[str]:
        """获取模型对应的提供商"""
        try:
            model_config = self.config_manager.get_model_config(model_id)
            return model_config.provider_id if model_config else None
        except Exception:
            return None
    
    async def _execute_batch_evaluation(self, 
                                       requests_by_provider: Dict[str, List[APIRequest]],
                                       priority: Priority) -> Dict[str, List[APIResponse]]:
        """执行批量评估"""
        if not self.batch_processor:
            raise EvaluationError("Batch processor not initialized")
        
        # 为每个提供商创建批处理任务
        tasks = {}
        for provider_id, requests in requests_by_provider.items():
            if requests:
                task = asyncio.create_task(
                    self.batch_processor.add_requests(requests, provider_id, priority)
                )
                tasks[provider_id] = task
        
        # 等待所有任务完成
        results = {}
        completed_tasks = await asyncio.gather(*tasks.values(), return_exceptions=True)
        
        for (provider_id, task), result in zip(tasks.items(), completed_tasks):
            if isinstance(result, Exception):
                logger.error(f"Batch evaluation failed for {provider_id}: {result}")
                # 创建错误响应
                error_responses = [
                    APIResponse(
                        request_id=f"error_{i}",
                        content="",
                        success=False,
                        error_message=str(result),
                        execution_time=0.0
                    ) for i in range(len(requests_by_provider[provider_id]))
                ]
                results[provider_id] = error_responses
            else:
                results[provider_id] = result
        
        return results
    
    def _merge_responses(self, responses_by_provider: Dict[str, List[APIResponse]]) -> List[APIResponse]:
        """合并响应"""
        all_responses = []
        for responses in responses_by_provider.values():
            all_responses.extend(responses)
        return all_responses
    
    async def _process_evaluation_results(self, 
                                        input_data: List[Dict],
                                        requests: List[APIRequest],
                                        responses: List[APIResponse],
                                        models: List[ModelConfig]) -> List[Dict]:
        """处理评估结果"""
        results = []
        
        # 创建请求-响应映射
        response_map = {r.request_id: r for r in responses}
        
        sample_index = 0
        for sample in input_data:
            for model in models:
                # 查找对应的响应
                request_id = f"req_{sample_index * len(models) + models.index(model)}"
                response = response_map.get(request_id)
                
                if response:
                    result = {
                        'sample_id': sample.get('id', sample_index),
                        'model_id': model.id,
                        'model_name': model.display_name,
                        'prompt': response.request_id,  # 这里应该是实际的prompt
                        'response': response.content,
                        'success': response.success,
                        'error_message': response.error_message,
                        'execution_time': response.execution_time,
                        'timestamp': time.time()
                    }
                    
                    # 添加原始样本数据
                    result.update({f'input_{k}': v for k, v in sample.items()})
                    
                    results.append(result)
                    
                    # 缓存成功的响应
                    if response.success and self.optimization_config.enable_request_cache:
                        cache_key = self._get_cache_key(model, result.get('prompt', ''))
                        self._request_cache[cache_key] = response
                        self._cache_timestamps[cache_key] = time.time()
            
            sample_index += 1
        
        return results
    
    def _update_metrics(self, result: EvaluationResult):
        """更新指标"""
        self.metrics.total_requests += result.total_requests
        self.metrics.successful_requests += result.successful_requests
        self.metrics.failed_requests += result.failed_requests
        self.metrics.total_time += result.total_time
        
        if self.metrics.total_requests > 0:
            self.metrics.avg_response_time = (
                self.metrics.avg_response_time * (self.metrics.total_requests - result.total_requests) +
                result.average_response_time * result.total_requests
            ) / self.metrics.total_requests
            
            self.metrics.requests_per_second = self.metrics.total_requests / self.metrics.total_time
        
        # 更新缓存命中率
        if self.optimization_config.enable_request_cache:
            total_cache_requests = len(self._request_cache)
            self.metrics.cache_hit_rate = total_cache_requests / max(1, self.metrics.total_requests)
    
    async def _metrics_monitor(self):
        """指标监控任务"""
        while not self._shutdown_event.is_set():
            try:
                await asyncio.sleep(self.optimization_config.metrics_interval)
                
                # 更新内存使用
                try:
                    import psutil
                    import os
                    process = psutil.Process(os.getpid())
                    self.metrics.memory_usage_mb = process.memory_info().rss / 1024 / 1024
                except ImportError:
                    pass
                
                # 更新提供商统计
                if self.api_client_manager:
                    global_summary = self.api_client_manager.get_global_performance_summary()
                    self.metrics.provider_stats = global_summary.get('providers', {})
                
                # 更新批处理效率
                if self.batch_processor:
                    batch_summary = self.batch_processor.get_performance_summary()
                    self.metrics.batch_efficiency = batch_summary.get('throughput_rps', 0)
                
                # 清理过期缓存
                await self._cleanup_expired_cache()
                
                # 定期垃圾回收
                if self.optimization_config.enable_memory_optimization:
                    gc.collect()
                
                logger.debug(f"Metrics updated: RPS={self.metrics.requests_per_second:.1f}, Memory={self.metrics.memory_usage_mb:.1f}MB")
                
            except Exception as e:
                logger.error(f"Metrics monitoring error: {e}")
    
    async def _cleanup_expired_cache(self):
        """清理过期缓存"""
        if not self.optimization_config.enable_request_cache:
            return
        
        current_time = time.time()
        expired_keys = [
            key for key, timestamp in self._cache_timestamps.items()
            if (current_time - timestamp) > self.optimization_config.cache_ttl
        ]
        
        for key in expired_keys:
            self._request_cache.pop(key, None)
            self._cache_timestamps.pop(key, None)
        
        if expired_keys:
            logger.debug(f"Cleaned up {len(expired_keys)} expired cache entries")
    
    def get_performance_summary(self) -> Dict:
        """获取性能摘要"""
        summary = {
            'evaluation_metrics': {
                'total_requests': self.metrics.total_requests,
                'successful_requests': self.metrics.successful_requests,
                'failed_requests': self.metrics.failed_requests,
                'success_rate': (self.metrics.successful_requests / max(1, self.metrics.total_requests)) * 100,
                'avg_response_time': self.metrics.avg_response_time,
                'requests_per_second': self.metrics.requests_per_second,
                'memory_usage_mb': self.metrics.memory_usage_mb,
                'cache_hit_rate': self.metrics.cache_hit_rate * 100,
                'batch_efficiency': self.metrics.batch_efficiency
            },
            'optimization_config': {
                'global_max_concurrent': self.optimization_config.global_max_concurrent,
                'batch_strategy': self.optimization_config.batch_strategy.value,
                'min_batch_size': self.optimization_config.min_batch_size,
                'max_batch_size': self.optimization_config.max_batch_size,
                'memory_threshold_mb': self.optimization_config.memory_threshold_mb,
                'enable_adaptive_concurrency': self.optimization_config.enable_adaptive_concurrency,
                'enable_request_cache': self.optimization_config.enable_request_cache
            }
        }
        
        # 添加API客户端性能
        if self.api_client_manager:
            summary['api_clients'] = self.api_client_manager.get_global_performance_summary()
        
        # 添加批处理性能
        if self.batch_processor:
            summary['batch_processor'] = self.batch_processor.get_performance_summary()
        
        return summary
    
    async def cleanup(self):
        """清理资源"""
        logger.info("Cleaning up OptimizedEvaluationEngine...")
        
        self._shutdown_event.set()
        
        # 停止指标监控
        if self._metrics_task:
            self._metrics_task.cancel()
            try:
                await self._metrics_task
            except asyncio.CancelledError:
                pass
        
        # 停止批处理器
        if self.batch_processor:
            await self.batch_processor.stop()
        
        # 关闭API客户端
        if self.api_client_manager:
            await self.api_client_manager.close_all()
        
        # 清理缓存
        self._request_cache.clear()
        self._cache_timestamps.clear()
        
        # 强制垃圾回收
        gc.collect()
        
        self._initialized = False
        logger.info("OptimizedEvaluationEngine cleanup completed")
    
    async def __aenter__(self):
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.cleanup()


# 便捷函数
async def create_optimized_evaluation_engine(config_path: str = "config.yaml",
                                           optimization_config: OptimizedEvaluationConfig = None) -> OptimizedEvaluationEngine:
    """创建优化的评估引擎"""
    config_manager = ConfigManager(config_path)
    await config_manager.load_config()
    
    engine = OptimizedEvaluationEngine(config_manager, optimization_config)
    await engine.initialize()
    
    return engine