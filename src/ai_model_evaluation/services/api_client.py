"""
用于与OpenAI兼容服务通信的AI API客户端。
"""

import asyncio
import time
from typing import Dict, List, Optional

import aiohttp

from ..models.core import Provider, ModelConfig, APIRequest, APIResponse
from ..utils.exceptions import APIError
from ..utils.decorators import retry_on_failure


class AIAPIClient:
    """使用OpenAI兼容API与AI服务提供商通信的客户端。"""
    
    def __init__(self, provider: Provider, timeout: int = 30, max_concurrent: int = 50):
        """
        初始化API客户端。
        
        参数:
            provider: 提供商配置
            timeout: 请求超时时间（秒）
            max_concurrent: 最大并发请求数
        """
        self.provider = provider
        self.timeout = aiohttp.ClientTimeout(total=timeout)
        self.max_concurrent = max_concurrent
        self._session: Optional[aiohttp.ClientSession] = None
        self._semaphore = asyncio.Semaphore(max_concurrent)
    
    async def __aenter__(self):
        """异步上下文管理器入口。"""
        await self._ensure_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口。"""
        await self.close()
    
    async def _ensure_session(self):
        """确保HTTP会话已创建。"""
        if self._session is None or self._session.closed:
            headers = {
                'Authorization': f'Bearer {self.provider.api_key}',
                'Content-Type': 'application/json',
                'User-Agent': 'AI-Model-Evaluation/1.0'
            }
            
            # 创建SSL上下文，禁用证书验证以避免SSL错误
            import ssl
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE

            self._session = aiohttp.ClientSession(
                headers=headers,
                timeout=self.timeout,
                connector=aiohttp.TCPConnector(
                    limit=self.max_concurrent,
                    ssl=ssl_context
                )
            )
    
    async def close(self):
        """Close the HTTP session."""
        if self._session and not self._session.closed:
            await self._session.close()
            self._session = None
    
    @retry_on_failure(max_retries=3, delay=1.0, backoff_factor=2.0)
    async def send_request(self, model: ModelConfig, prompt: str, **kwargs) -> APIResponse:
        """
        Send a single request to the AI model.
        
        Args:
            model: Model configuration
            prompt: Prompt text to send
            **kwargs: Additional parameters
            
        Returns:
            API response
        """
        request = APIRequest(
            model=model,
            prompt=prompt,
            request_id=kwargs.get('request_id', f"{model.id}_{int(time.time())}")
        )
        
        # Validate request parameters
        validation_errors = request.validate_parameters()
        if validation_errors:
            return APIResponse(
                request_id=request.request_id,
                content="",
                success=False,
                error_message=f"Parameter validation failed: {'; '.join(validation_errors)}",
                execution_time=0.0
            )
        
        return await self._send_single_request(request)
    
    async def batch_requests(self, requests: List[APIRequest]) -> List[APIResponse]:
        """
        Send multiple requests concurrently.
        
        Args:
            requests: List of API requests
            
        Returns:
            List of API responses in the same order as requests
        """
        if not requests:
            return []
        
        await self._ensure_session()
        
        # Create tasks for concurrent execution
        tasks = []
        for request in requests:
            task = asyncio.create_task(self._send_single_request(request))
            tasks.append(task)
        
        # Wait for all tasks to complete
        responses = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Convert exceptions to error responses
        result = []
        for i, response in enumerate(responses):
            if isinstance(response, Exception):
                error_response = APIResponse(
                    request_id=requests[i].request_id,
                    content="",
                    success=False,
                    error_message=str(response),
                    execution_time=0.0
                )
                result.append(error_response)
            else:
                result.append(response)
        
        return result
    
    async def _send_single_request(self, request: APIRequest) -> APIResponse:
        """
        Send a single request with concurrency control.
        
        Args:
            request: API request to send
            
        Returns:
            API response
        """
        async with self._semaphore:  # Limit concurrent requests
            await self._ensure_session()
            
            start_time = time.time()
            
            try:
                # Prepare request data
                request_data = request.to_openai_format()
                url = f"{self.provider.base_url}chat/completions"
                
                # Send request
                async with self._session.post(url, json=request_data) as response:
                    execution_time = time.time() - start_time
                    
                    if response.status == 200:
                        response_data = await response.json()
                        return APIResponse.from_openai_response(
                            request_id=request.request_id,
                            response_data=response_data,
                            execution_time=execution_time
                        )
                    else:
                        # Handle HTTP errors
                        error_text = await response.text()
                        error_msg = f"HTTP {response.status}: {error_text}"
                        
                        # Raise APIError for retry-able errors
                        if response.status in [429, 500, 502, 503, 504]:  # Retry-able errors
                            raise APIError(error_msg, request.model.id)
                        
                        # Return error response for non-retry-able errors
                        return APIResponse(
                            request_id=request.request_id,
                            content="",
                            success=False,
                            error_message=error_msg,
                            execution_time=execution_time
                        )
            
            except asyncio.TimeoutError:
                execution_time = time.time() - start_time
                raise APIError(f"Request timeout after {execution_time:.1f}s", request.model.id)
            
            except aiohttp.ClientError as e:
                execution_time = time.time() - start_time
                raise APIError(f"Network error: {e}", request.model.id)
            
            except Exception as e:
                execution_time = time.time() - start_time
                return APIResponse(
                    request_id=request.request_id,
                    content="",
                    success=False,
                    error_message=f"Unexpected error: {e}",
                    execution_time=execution_time
                )
    
    async def validate_connection(self) -> bool:
        """
        Validate connection to the provider.
        
        Returns:
            True if connection is valid, False otherwise
        """
        try:
            # Create a simple test request
            test_model = ModelConfig(
                id="test",
                provider_id=self.provider.id,
                model_name="gpt-3.5-turbo",  # Use a common model name for testing
                display_name="Test Model",
                temperature=0.1,
                max_tokens=10
            )
            
            response = await self.send_request(test_model, "Hello")
            return response.success
            
        except Exception:
            return False
    
    async def get_connection_info(self) -> Dict[str, any]:
        """
        Get connection information and status.
        
        Returns:
            Dictionary with connection details
        """
        start_time = time.time()
        
        try:
            is_connected = await self.validate_connection()
            response_time = time.time() - start_time
            
            return {
                'provider_id': self.provider.id,
                'provider_name': self.provider.name,
                'base_url': self.provider.base_url,
                'is_connected': is_connected,
                'response_time': response_time,
                'error_message': None
            }
            
        except Exception as e:
            response_time = time.time() - start_time
            
            return {
                'provider_id': self.provider.id,
                'provider_name': self.provider.name,
                'base_url': self.provider.base_url,
                'is_connected': False,
                'response_time': response_time,
                'error_message': str(e)
            }


class APIClientManager:
    """Manages multiple API clients for different providers."""
    
    def __init__(self):
        """Initialize client manager."""
        self._clients: Dict[str, AIAPIClient] = {}
    
    def add_client(self, provider: Provider, **kwargs) -> AIAPIClient:
        """
        Add a client for a provider.
        
        Args:
            provider: Provider configuration
            **kwargs: Additional client parameters
            
        Returns:
            Created API client
        """
        client = AIAPIClient(provider, **kwargs)
        self._clients[provider.id] = client
        return client
    
    def get_client(self, provider_id: str) -> Optional[AIAPIClient]:
        """
        Get client for a provider.
        
        Args:
            provider_id: Provider ID
            
        Returns:
            API client or None if not found
        """
        return self._clients.get(provider_id)
    
    def remove_client(self, provider_id: str) -> bool:
        """
        Remove client for a provider.
        
        Args:
            provider_id: Provider ID
            
        Returns:
            True if client was removed, False if not found
        """
        if provider_id in self._clients:
            del self._clients[provider_id]
            return True
        return False
    
    async def close_all(self):
        """Close all clients."""
        for client in self._clients.values():
            await client.close()
        self._clients.clear()
    
    async def validate_all_connections(self) -> Dict[str, bool]:
        """
        Validate connections for all clients.
        
        Returns:
            Dictionary mapping provider IDs to connection status
        """
        results = {}
        
        for provider_id, client in self._clients.items():
            try:
                results[provider_id] = await client.validate_connection()
            except Exception:
                results[provider_id] = False
        
        return results
    
    async def get_all_connection_info(self) -> Dict[str, Dict[str, any]]:
        """
        Get connection information for all clients.
        
        Returns:
            Dictionary mapping provider IDs to connection info
        """
        results = {}
        
        for provider_id, client in self._clients.items():
            results[provider_id] = await client.get_connection_info()
        
        return results