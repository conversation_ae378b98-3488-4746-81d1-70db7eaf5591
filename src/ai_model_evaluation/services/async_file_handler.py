"""异步文件处理服务，支持高性能IO操作和内存优化。"""

import asyncio
import gc
from pathlib import Path
from typing import List, Optional, Union, AsyncGenerator, Dict, Any
import weakref
from concurrent.futures import ThreadPoolExecutor
import threading

import pandas as pd
import aiofiles
from loguru import logger

from ..models.core import EvaluationRow, ResultRow, ValidationResult
from ..utils.exceptions import FileProcessingError
from ..utils.helpers import get_file_extension, is_csv_file, is_excel_file, validate_file_path


class AsyncFileHandler:
    """异步文件处理器，支持高性能IO和内存优化。"""
    
    # 默认配置
    DEFAULT_CHUNK_SIZE = 10000  # 每批处理的行数
    DEFAULT_BUFFER_SIZE = 8192  # 文件读取缓冲区大小
    DEFAULT_MAX_WORKERS = 4     # 线程池最大工作线程数
    
    # 内存管理配置
    MEMORY_THRESHOLD_MB = 500   # 内存使用阈值（MB）
    GC_FREQUENCY = 1000         # 垃圾回收频率（处理行数）
    
    def __init__(self, 
                 chunk_size: int = DEFAULT_CHUNK_SIZE,
                 buffer_size: int = DEFAULT_BUFFER_SIZE,
                 max_workers: int = DEFAULT_MAX_WORKERS,
                 required_columns: Optional[List[str]] = None):
        """初始化异步文件处理器。
        
        Args:
            chunk_size: 批处理大小
            buffer_size: 文件读取缓冲区大小
            max_workers: 线程池最大工作线程数
            required_columns: 必需的列名列表
        """
        self.chunk_size = chunk_size
        self.buffer_size = buffer_size
        self.max_workers = max_workers
        self.required_columns = required_columns or ['original_prompt', 'variable_a', 'variable_b']
        
        # 线程池用于CPU密集型操作
        self._executor = ThreadPoolExecutor(max_workers=max_workers)
        
        # 内存管理
        self._processed_rows = 0
        self._memory_cache = weakref.WeakValueDictionary()
        self._lock = threading.Lock()
        
        logger.info(f"AsyncFileHandler initialized: chunk_size={chunk_size}, max_workers={max_workers}")
    
    async def read_evaluation_data_async(self, file_path: Union[str, Path]) -> AsyncGenerator[List[EvaluationRow], None]:
        """异步读取评估数据，返回数据块生成器。
        
        Args:
            file_path: 输入文件路径
            
        Yields:
            评估行数据块
            
        Raises:
            FileProcessingError: 文件读取失败
        """
        file_path = Path(file_path)
        
        if not validate_file_path(file_path):
            raise FileProcessingError(f"File not found or not readable: {file_path}")
        
        try:
            logger.info(f"Starting async read of {file_path}")
            
            if is_csv_file(file_path):
                async for chunk in self._read_csv_chunks(file_path):
                    yield chunk
            elif is_excel_file(file_path):
                # Excel文件需要在线程池中处理
                chunks = await self._read_excel_in_executor(file_path)
                for chunk in chunks:
                    yield chunk
            else:
                raise FileProcessingError(f"Unsupported file format: {get_file_extension(file_path)}")
                
        except Exception as e:
            logger.error(f"Failed to read file {file_path}: {e}")
            raise FileProcessingError(f"Failed to read file: {e}")
    
    async def _read_csv_chunks(self, file_path: Path) -> AsyncGenerator[List[EvaluationRow], None]:
        """异步读取CSV文件块。"""
        loop = asyncio.get_event_loop()
        
        # 在线程池中读取CSV块
        def read_csv_chunk(skip_rows: int, nrows: int) -> Optional[pd.DataFrame]:
            try:
                if skip_rows == 0:
                    # 第一次读取，包含header
                    df = pd.read_csv(file_path, nrows=nrows, keep_default_na=False, na_values=[''])
                else:
                    # 后续读取，跳过header
                    df = pd.read_csv(file_path, skiprows=skip_rows, nrows=nrows, 
                                   keep_default_na=False, na_values=[], header=None)
                    # 使用第一次读取的列名
                    if hasattr(self, '_csv_columns'):
                        df.columns = self._csv_columns
                return df if not df.empty else None
            except Exception as e:
                logger.error(f"Error reading CSV chunk: {e}")
                return None
        
        skip_rows = 0
        first_chunk = True
        
        while True:
            df = await loop.run_in_executor(self._executor, read_csv_chunk, skip_rows, self.chunk_size)
            
            if df is None or df.empty:
                break
            
            # 保存列名用于后续块
            if first_chunk:
                self._csv_columns = df.columns.tolist()
                first_chunk = False
            
            # 验证数据
            validation_result = await self._validate_dataframe_async(df)
            if not validation_result.is_valid:
                logger.warning(f"Validation failed for chunk starting at row {skip_rows}: {validation_result.errors}")
            
            # 转换为EvaluationRow对象
            evaluation_rows = await self._dataframe_to_evaluation_rows_async(df)
            
            # 内存管理
            await self._manage_memory()
            
            yield evaluation_rows
            
            skip_rows += self.chunk_size
            
            # 如果读取的行数少于chunk_size，说明已到文件末尾
            if len(df) < self.chunk_size:
                break
    
    async def _read_excel_in_executor(self, file_path: Path) -> List[List[EvaluationRow]]:
        """在线程池中读取Excel文件。"""
        loop = asyncio.get_event_loop()
        
        def read_excel():
            try:
                df = pd.read_excel(file_path)
                # 将大DataFrame分割成块
                chunks = []
                for i in range(0, len(df), self.chunk_size):
                    chunk_df = df.iloc[i:i + self.chunk_size].copy()
                    chunks.append(chunk_df)
                return chunks
            except Exception as e:
                logger.error(f"Error reading Excel file: {e}")
                raise
        
        df_chunks = await loop.run_in_executor(self._executor, read_excel)
        
        result_chunks = []
        for df_chunk in df_chunks:
            # 验证数据
            validation_result = await self._validate_dataframe_async(df_chunk)
            if not validation_result.is_valid:
                logger.warning(f"Validation failed for Excel chunk: {validation_result.errors}")
            
            # 转换为EvaluationRow对象
            evaluation_rows = await self._dataframe_to_evaluation_rows_async(df_chunk)
            result_chunks.append(evaluation_rows)
            
            # 内存管理
            await self._manage_memory()
        
        return result_chunks
    
    async def write_results_async(self, results: List[ResultRow], output_path: Union[str, Path]) -> bool:
        """异步写入评估结果。
        
        Args:
            results: 结果行列表
            output_path: 输出文件路径
            
        Returns:
            写入是否成功
            
        Raises:
            FileProcessingError: 写入失败
        """
        if not results:
            raise FileProcessingError("No results to write")
        
        output_path = Path(output_path)
        
        try:
            logger.info(f"Starting async write to {output_path}")
            
            # 确保输出目录存在
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 在线程池中转换数据和写入文件
            loop = asyncio.get_event_loop()
            
            def write_file():
                # 转换结果为DataFrame
                df = self._result_rows_to_dataframe_sync(results)
                
                # 根据文件扩展名写入
                if is_csv_file(output_path):
                    df.to_csv(output_path, index=False, encoding='utf-8')
                elif is_excel_file(output_path):
                    df.to_excel(output_path, index=False, engine='openpyxl')
                else:
                    # 默认使用CSV格式
                    output_path_csv = output_path.with_suffix('.csv')
                    df.to_csv(output_path_csv, index=False, encoding='utf-8')
                
                return True
            
            success = await loop.run_in_executor(self._executor, write_file)
            
            if success:
                logger.info(f"Successfully wrote {len(results)} results to {output_path}")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to write results to {output_path}: {e}")
            raise FileProcessingError(f"Failed to write results to {output_path}: {e}")
    
    async def write_results_streaming(self, results_generator: AsyncGenerator[List[ResultRow], None], 
                                    output_path: Union[str, Path]) -> bool:
        """流式写入评估结果，适用于大量数据。
        
        Args:
            results_generator: 结果数据生成器
            output_path: 输出文件路径
            
        Returns:
            写入是否成功
        """
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            logger.info(f"Starting streaming write to {output_path}")
            
            first_chunk = True
            total_written = 0
            
            async for result_chunk in results_generator:
                if not result_chunk:
                    continue
                
                # 转换为DataFrame
                loop = asyncio.get_event_loop()
                df = await loop.run_in_executor(self._executor, self._result_rows_to_dataframe_sync, result_chunk)
                
                # 写入文件
                if is_csv_file(output_path):
                    mode = 'w' if first_chunk else 'a'
                    header = first_chunk
                    await loop.run_in_executor(self._executor, 
                                             lambda: df.to_csv(output_path, mode=mode, header=header, index=False, encoding='utf-8'))
                else:
                    # Excel文件不支持流式写入，需要收集所有数据
                    logger.warning("Excel format doesn't support streaming write, collecting all data")
                    all_results = []
                    async for chunk in results_generator:
                        all_results.extend(chunk)
                    return await self.write_results_async(all_results, output_path)
                
                total_written += len(result_chunk)
                first_chunk = False
                
                # 内存管理
                await self._manage_memory()
            
            logger.info(f"Successfully wrote {total_written} results to {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to stream write results: {e}")
            raise FileProcessingError(f"Failed to stream write results: {e}")
    
    async def _validate_dataframe_async(self, df: pd.DataFrame) -> ValidationResult:
        """异步验证DataFrame。"""
        loop = asyncio.get_event_loop()
        
        def validate_sync():
            result = ValidationResult(is_valid=True, row_count=len(df), column_names=df.columns.tolist())
            
            # 检查是否为空
            if df.empty:
                result.errors.append("DataFrame is empty")
                result.is_valid = False
                return result
            
            # 检查必需列
            missing_columns = [col for col in self.required_columns if col not in df.columns]
            if missing_columns:
                result.errors.append(f"Missing required columns: {', '.join(missing_columns)}")
                result.is_valid = False
            
            return result
        
        return await loop.run_in_executor(self._executor, validate_sync)
    
    async def _dataframe_to_evaluation_rows_async(self, df: pd.DataFrame) -> List[EvaluationRow]:
        """异步转换DataFrame为EvaluationRow对象。"""
        loop = asyncio.get_event_loop()
        
        def convert_sync():
            rows = []
            for _, row in df.iterrows():
                # 构建数据字典
                data = {col: str(row.get(col, '')) for col in df.columns}
                
                # 创建EvaluationRow对象
                eval_row = EvaluationRow(
                    original_prompt=data.get('original_prompt', ''),
                    variable_a=data.get('variable_a', ''),
                    variable_b=data.get('variable_b', ''),
                    expected_result=data.get('expected_result', ''),
                    data=data
                )
                rows.append(eval_row)
            
            return rows
        
        return await loop.run_in_executor(self._executor, convert_sync)
    
    def _result_rows_to_dataframe_sync(self, results: List[ResultRow]) -> pd.DataFrame:
        """同步转换ResultRow对象为DataFrame。"""
        if not results:
            return pd.DataFrame()
        
        # 获取所有唯一的模型ID
        all_model_ids = set()
        for result in results:
            all_model_ids.update(result.model_results.keys())
        
        model_ids = sorted(all_model_ids)
        
        # 构建DataFrame数据
        data = []
        for result in results:
            row_data = result.data.copy()
            
            # 添加模型结果
            for model_id in model_ids:
                row_data[f'{model_id}_result'] = result.model_results.get(model_id, '')
                
                # 添加错误信息（如果存在）
                error = result.error_info.get(model_id)
                if error:
                    row_data[f'{model_id}_error'] = error
            
            data.append(row_data)
        
        return pd.DataFrame(data)
    
    async def _manage_memory(self):
        """内存管理和垃圾回收。"""
        self._processed_rows += 1
        
        # 定期执行垃圾回收
        if self._processed_rows % self.GC_FREQUENCY == 0:
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(self._executor, gc.collect)
            logger.debug(f"Garbage collection executed after processing {self._processed_rows} rows")
    
    async def get_file_info_async(self, file_path: Union[str, Path]) -> Dict[str, Any]:
        """异步获取文件信息。"""
        file_path = Path(file_path)
        
        info = {
            'path': str(file_path),
            'exists': file_path.exists(),
            'size': 0,
            'extension': get_file_extension(file_path),
            'is_supported': False,
            'row_count': 0,
            'column_count': 0
        }
        
        if not file_path.exists():
            return info
        
        # 异步获取文件大小
        loop = asyncio.get_event_loop()
        info['size'] = await loop.run_in_executor(self._executor, lambda: file_path.stat().st_size)
        info['is_supported'] = is_csv_file(file_path) or is_excel_file(file_path)
        
        if info['is_supported']:
            try:
                # 异步验证文件格式
                if is_csv_file(file_path):
                    df_sample = await loop.run_in_executor(self._executor, 
                                                         lambda: pd.read_csv(file_path, nrows=5, keep_default_na=False, na_values=['']))
                else:
                    df_sample = await loop.run_in_executor(self._executor, 
                                                         lambda: pd.read_excel(file_path, nrows=5))
                
                # 获取完整行数
                if is_csv_file(file_path):
                    full_df = await loop.run_in_executor(self._executor, 
                                                        lambda: pd.read_csv(file_path, keep_default_na=False, na_values=['']))
                else:
                    full_df = await loop.run_in_executor(self._executor, 
                                                        lambda: pd.read_excel(file_path))
                
                info['row_count'] = len(full_df)
                info['column_count'] = len(df_sample.columns)
                info['columns'] = df_sample.columns.tolist()
                
                # 验证文件格式
                validation_result = await self._validate_dataframe_async(df_sample)
                info['is_valid'] = validation_result.is_valid
                info['errors'] = validation_result.errors
                info['warnings'] = validation_result.warnings
                
            except Exception as e:
                info['error'] = str(e)
        
        return info
    
    async def close(self):
        """关闭异步文件处理器，清理资源。"""
        logger.info("Closing AsyncFileHandler")
        self._executor.shutdown(wait=True)
        self._memory_cache.clear()
        gc.collect()
    
    def __del__(self):
        """析构函数，确保资源清理。"""
        try:
            if hasattr(self, '_executor'):
                self._executor.shutdown(wait=False)
        except Exception:
            pass