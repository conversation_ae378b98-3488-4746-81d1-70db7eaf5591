"""
Analysis engine for evaluation results.
"""

import statistics
from pathlib import Path
from typing import Dict, <PERSON>, Optional, Tuple, Any
from collections import defaultdict

import pandas as pd

from ..models.core import ResultRow, AnalysisResult, ComparisonReport, StatisticsReport
from ..utils.exceptions import FileProcessingError, AnalysisError
from ..utils.helpers import get_file_extension, is_csv_file, is_excel_file
from .file_handler import FileHandler


class AnalysisEngine:
    """Engine for analyzing evaluation results."""
    
    def __init__(self):
        """Initialize analysis engine."""
        self.file_handler = FileHandler()
    
    def load_results(self, file_path: str) -> List[ResultRow]:
        """
        Load results from a file.
        
        Args:
            file_path: Path to results file
            
        Returns:
            List of result rows
            
        Raises:
            FileProcessingError: If file cannot be loaded
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileProcessingError(f"Results file not found: {file_path}")
        
        try:
            # Read file based on extension
            if is_csv_file(file_path):
                df = pd.read_csv(file_path, keep_default_na=False, na_values=[''])
            elif is_excel_file(file_path):
                df = pd.read_excel(file_path)
            else:
                raise FileProcessingError(f"Unsupported file format: {get_file_extension(file_path)}")
            
            return self._dataframe_to_result_rows(df)
            
        except Exception as e:
            raise FileProcessingError(f"Failed to load results file: {e}")
    
    def analyze_results(self, results: List[ResultRow]) -> AnalysisResult:
        """
        Analyze evaluation results.
        
        Args:
            results: List of result rows
            
        Returns:
            Analysis result
        """
        if not results:
            return AnalysisResult(
                total_rows=0,
                model_count=0,
                success_rates={},
                average_execution_times={},
                error_counts={}
            )
        
        # Get all model IDs
        all_model_ids = set()
        for result in results:
            all_model_ids.update(result.model_results.keys())
        
        model_ids = sorted(all_model_ids)
        
        # Calculate metrics for each model
        success_rates = {}
        average_execution_times = {}
        error_counts = {}
        
        for model_id in model_ids:
            total_requests = 0
            successful_requests = 0
            execution_times = []
            errors = 0
            
            for result in results:
                if model_id in result.model_results:
                    total_requests += 1
                    
                    if result.error_info.get(model_id) is None:
                        successful_requests += 1
                    else:
                        errors += 1
                    
                    exec_time = result.execution_time.get(model_id, 0)
                    if exec_time > 0:
                        execution_times.append(exec_time)
            
            success_rates[model_id] = successful_requests / total_requests if total_requests > 0 else 0.0
            average_execution_times[model_id] = statistics.mean(execution_times) if execution_times else 0.0
            error_counts[model_id] = errors
        
        return AnalysisResult(
            total_rows=len(results),
            model_count=len(model_ids),
            success_rates=success_rates,
            average_execution_times=average_execution_times,
            error_counts=error_counts
        )
    
    def compare_models(self, results: List[ResultRow], model_ids: Optional[List[str]] = None) -> ComparisonReport:
        """
        Compare models based on evaluation results.
        
        Args:
            results: List of result rows
            model_ids: Optional list of model IDs to compare (defaults to all)
            
        Returns:
            Comparison report
        """
        if not results:
            return ComparisonReport(models=[], metrics={}, summary="No results to compare")
        
        # Get model IDs to compare
        if model_ids is None:
            all_model_ids = set()
            for result in results:
                all_model_ids.update(result.model_results.keys())
            model_ids = sorted(all_model_ids)
        
        # Calculate comparison metrics
        metrics = {}
        
        for model_id in model_ids:
            model_metrics = self._calculate_model_metrics(results, model_id)
            metrics[model_id] = model_metrics
        
        # Generate summary
        summary = self._generate_comparison_summary(metrics)
        
        return ComparisonReport(
            models=model_ids,
            metrics=metrics,
            summary=summary
        )
    
    def generate_statistics(self, results: List[ResultRow]) -> StatisticsReport:
        """
        Generate detailed statistics for evaluation results.
        
        Args:
            results: List of result rows
            
        Returns:
            Statistics report
        """
        if not results:
            return StatisticsReport(
                total_evaluations=0,
                successful_evaluations=0,
                failed_evaluations=0
            )
        
        # Get all model IDs
        all_model_ids = set()
        for result in results:
            all_model_ids.update(result.model_results.keys())
        
        model_ids = sorted(all_model_ids)
        
        # Calculate overall statistics
        total_evaluations = len(results) * len(model_ids)
        successful_evaluations = 0
        failed_evaluations = 0
        
        # Calculate per-model statistics
        average_response_length = {}
        response_time_stats = {}
        
        for model_id in model_ids:
            response_lengths = []
            execution_times = []
            model_successes = 0
            model_failures = 0
            
            for result in results:
                if model_id in result.model_results:
                    if result.error_info.get(model_id) is None:
                        successful_evaluations += 1
                        model_successes += 1
                        response_lengths.append(len(result.model_results[model_id]))
                    else:
                        failed_evaluations += 1
                        model_failures += 1
                    
                    exec_time = result.execution_time.get(model_id, 0)
                    if exec_time > 0:
                        execution_times.append(exec_time)
            
            # Calculate average response length
            average_response_length[model_id] = statistics.mean(response_lengths) if response_lengths else 0.0
            
            # Calculate response time statistics
            if execution_times:
                response_time_stats[model_id] = {
                    'mean': statistics.mean(execution_times),
                    'median': statistics.median(execution_times),
                    'min': min(execution_times),
                    'max': max(execution_times),
                    'std_dev': statistics.stdev(execution_times) if len(execution_times) > 1 else 0.0
                }
            else:
                response_time_stats[model_id] = {
                    'mean': 0.0, 'median': 0.0, 'min': 0.0, 'max': 0.0, 'std_dev': 0.0
                }
        
        return StatisticsReport(
            total_evaluations=total_evaluations,
            successful_evaluations=successful_evaluations,
            failed_evaluations=failed_evaluations,
            average_response_length=average_response_length,
            response_time_stats=response_time_stats
        )
    
    def find_best_performing_model(self, results: List[ResultRow], 
                                 metric: str = 'success_rate') -> Optional[Tuple[str, float]]:
        """
        Find the best performing model based on a specific metric.
        
        Args:
            results: List of result rows
            metric: Metric to use ('success_rate', 'response_time', 'response_quality')
            
        Returns:
            Tuple of (model_id, metric_value) or None if no results
        """
        if not results:
            return None
        
        analysis = self.analyze_results(results)
        
        if metric == 'success_rate':
            if not analysis.success_rates:
                return None
            best_model = max(analysis.success_rates.items(), key=lambda x: x[1])
            return best_model
        
        elif metric == 'response_time':
            if not analysis.average_execution_times:
                return None
            # For response time, lower is better
            best_model = min(analysis.average_execution_times.items(), key=lambda x: x[1])
            return best_model
        
        else:
            raise ValueError(f"Unsupported metric: {metric}")
    
    def identify_problematic_rows(self, results: List[ResultRow], 
                                threshold: float = 0.5) -> List[Dict[str, Any]]:
        """
        Identify rows where many models failed or performed poorly.
        
        Args:
            results: List of result rows
            threshold: Failure rate threshold (0.0 to 1.0)
            
        Returns:
            List of problematic row information
        """
        problematic_rows = []
        
        for i, result in enumerate(results):
            total_models = len(result.model_results)
            if total_models == 0:
                continue
            
            failed_models = sum(1 for error in result.error_info.values() if error is not None)
            failure_rate = failed_models / total_models
            
            if failure_rate >= threshold:
                problematic_rows.append({
                    'row_index': i + 1,
                    'original_prompt': result.original_prompt,
                    'variable_a': result.variable_a,
                    'variable_b': result.variable_b,
                    'total_models': total_models,
                    'failed_models': failed_models,
                    'failure_rate': failure_rate,
                    'errors': {model_id: error for model_id, error in result.error_info.items() if error is not None}
                })
        
        return problematic_rows
    
    def calculate_model_agreement(self, results: List[ResultRow]) -> Dict[str, float]:
        """
        Calculate agreement between models (how often they give similar responses).
        
        Args:
            results: List of result rows
            
        Returns:
            Dictionary with agreement metrics
        """
        if not results:
            return {}
        
        # Get all model IDs
        all_model_ids = set()
        for result in results:
            all_model_ids.update(result.model_results.keys())
        
        model_ids = sorted(all_model_ids)
        
        if len(model_ids) < 2:
            return {}
        
        # Calculate pairwise agreement
        agreement_scores = {}
        
        for i, model1 in enumerate(model_ids):
            for j, model2 in enumerate(model_ids[i+1:], i+1):
                pair_key = f"{model1}_vs_{model2}"
                
                agreements = 0
                total_comparisons = 0
                
                for result in results:
                    if model1 in result.model_results and model2 in result.model_results:
                        # Only compare if both models succeeded
                        if (result.error_info.get(model1) is None and 
                            result.error_info.get(model2) is None):
                            
                            response1 = result.model_results[model1].strip().lower()
                            response2 = result.model_results[model2].strip().lower()
                            
                            # Simple agreement check (can be enhanced with semantic similarity)
                            if response1 == response2:
                                agreements += 1
                            
                            total_comparisons += 1
                
                if total_comparisons > 0:
                    agreement_scores[pair_key] = agreements / total_comparisons
                else:
                    agreement_scores[pair_key] = 0.0
        
        return agreement_scores
    
    def _dataframe_to_result_rows(self, df: pd.DataFrame) -> List[ResultRow]:
        """Convert DataFrame to list of ResultRow objects."""
        results = []
        
        # Identify model result columns
        base_columns = {'original_prompt', 'variable_a', 'variable_b', 'expected_result'}
        model_columns = {}
        
        for col in df.columns:
            if col not in base_columns:
                if col.endswith('_result'):
                    model_id = col[:-7]  # Remove '_result' suffix
                    if model_id not in model_columns:
                        model_columns[model_id] = {}
                    model_columns[model_id]['result'] = col
                elif col.endswith('_execution_time'):
                    model_id = col[:-15]  # Remove '_execution_time' suffix
                    if model_id not in model_columns:
                        model_columns[model_id] = {}
                    model_columns[model_id]['time'] = col
                elif col.endswith('_error'):
                    model_id = col[:-6]  # Remove '_error' suffix
                    if model_id not in model_columns:
                        model_columns[model_id] = {}
                    model_columns[model_id]['error'] = col
        
        # Convert rows
        for _, row in df.iterrows():
            # Create flexible data dictionary
            data = {}
            for col in df.columns:
                if col not in ['expected_result'] and not col.endswith('_result') and not col.endswith('_time') and not col.endswith('_error'):
                    data[col] = str(row.get(col, ''))

            # Handle expected_result separately
            expected_result = str(row.get('expected_result', '')) if pd.notna(row.get('expected_result')) else None
            if expected_result:
                data['expected_result'] = expected_result

            result_row = ResultRow(data=data)
            
            # Add model results
            for model_id, columns in model_columns.items():
                if 'result' in columns:
                    result_content = str(row.get(columns['result'], ''))
                    execution_time = float(row.get(columns.get('time', ''), 0.0))
                    error_message = row.get(columns.get('error', ''))
                    
                    if pd.isna(error_message) or error_message == '':
                        error_message = None
                    else:
                        error_message = str(error_message)
                    
                    result_row.add_result(model_id, result_content, execution_time, error_message)
            
            results.append(result_row)
        
        return results
    
    def _calculate_model_metrics(self, results: List[ResultRow], model_id: str) -> Dict[str, float]:
        """Calculate comprehensive metrics for a specific model."""
        total_requests = 0
        successful_requests = 0
        execution_times = []
        response_lengths = []
        
        for result in results:
            if model_id in result.model_results:
                total_requests += 1
                
                if result.error_info.get(model_id) is None:
                    successful_requests += 1
                    response_lengths.append(len(result.model_results[model_id]))
                
                exec_time = result.execution_time.get(model_id, 0)
                if exec_time > 0:
                    execution_times.append(exec_time)
        
        metrics = {
            'success_rate': successful_requests / total_requests if total_requests > 0 else 0.0,
            'total_requests': float(total_requests),
            'successful_requests': float(successful_requests),
            'failed_requests': float(total_requests - successful_requests),
            'average_execution_time': statistics.mean(execution_times) if execution_times else 0.0,
            'median_execution_time': statistics.median(execution_times) if execution_times else 0.0,
            'min_execution_time': min(execution_times) if execution_times else 0.0,
            'max_execution_time': max(execution_times) if execution_times else 0.0,
            'average_response_length': statistics.mean(response_lengths) if response_lengths else 0.0,
        }
        
        return metrics
    
    def _generate_comparison_summary(self, metrics: Dict[str, Dict[str, float]]) -> str:
        """Generate a text summary of model comparison."""
        if not metrics:
            return "No models to compare"
        
        model_ids = list(metrics.keys())
        
        # Find best performing models for different metrics
        best_success_rate = max(metrics.items(), key=lambda x: x[1]['success_rate'])
        best_speed = min(metrics.items(), key=lambda x: x[1]['average_execution_time'])
        
        summary_parts = [
            f"Compared {len(model_ids)} models across {int(best_success_rate[1]['total_requests'])} requests.",
            f"Best success rate: {best_success_rate[0]} ({best_success_rate[1]['success_rate']:.1%})",
            f"Fastest model: {best_speed[0]} ({best_speed[1]['average_execution_time']:.2f}s avg)",
        ]
        
        # Add overall performance summary
        overall_success_rate = sum(m['success_rate'] for m in metrics.values()) / len(metrics)
        summary_parts.append(f"Overall success rate: {overall_success_rate:.1%}")
        
        return " | ".join(summary_parts)

    def calculate_classification_metrics(self, results: List[ResultRow]) -> Dict[str, Dict[str, Any]]:
        """
        Calculate classification metrics for models with expected results.

        Args:
            results: List of result rows with expected results

        Returns:
            Dictionary with classification metrics for each model
        """
        if not results:
            return {}

        # Get all model IDs
        all_model_ids = set()
        for result in results:
            all_model_ids.update(result.model_results.keys())

        model_ids = sorted(all_model_ids)
        classification_metrics = {}

        for model_id in model_ids:
            # Extract predictions and true labels
            y_true = []
            y_pred = []

            for result in results:
                if model_id in result.model_results:
                    # Check if we have expected result
                    expected = result.data.get('expected_result')
                    predicted = result.model_results[model_id]

                    # Only include valid comparisons
                    if (expected and expected.strip() and
                        predicted and predicted.strip() and
                        result.error_info.get(model_id) is None):
                        y_true.append(str(expected).strip())
                        y_pred.append(str(predicted).strip())

            if not y_true or not y_pred:
                classification_metrics[model_id] = {
                    "accuracy": 0.0,
                    "precision": 0.0,
                    "recall": 0.0,
                    "f1_score": 0.0,
                    "macro_avg": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0},
                    "weighted_avg": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0},
                    "per_class_metrics": {},
                    "confusion_matrix": {},
                    "total_samples": 0,
                    "note": "No valid samples for classification metrics"
                }
                continue

            # Calculate detailed classification metrics
            metrics = self._calculate_detailed_classification_metrics(y_true, y_pred, model_id)
            classification_metrics[model_id] = metrics

        return classification_metrics

    def _calculate_detailed_classification_metrics(self, y_true: List[str], y_pred: List[str], model_name: str) -> Dict[str, Any]:
        """
        Calculate detailed classification metrics.

        根据 sunfa.md 文档要求，将"none"类别视为负例，其他类别视为正例
        重新定义 TP、FP、FN、TN 的计算方式，专注于正向意图识别能力

        Args:
            y_true: True labels
            y_pred: Predicted labels
            model_name: Model name

        Returns:
            Dictionary with detailed classification metrics
        """
        # Convert to pandas Series for easier calculation
        y_true_series = pd.Series(y_true)
        y_pred_series = pd.Series(y_pred)

        # Get all classes
        all_classes = sorted(set(y_true) | set(y_pred))

        # 识别正向类别（非"none"的类别）
        positive_classes = [label for label in all_classes if label.lower() not in ['none', '', 'null', 'na']]
        none_class = 'none' if 'none' in all_classes else None

        # 如果没有找到明确的"none"类，尝试其他可能的表示
        if none_class is None:
            for label in all_classes:
                if label.lower() in ['', 'null', 'na', 'unknown', '无', '空']:
                    none_class = label
                    positive_classes = [l for l in all_classes if l != label]
                    break

        # Calculate confusion matrix
        confusion_matrix = {}
        for true_class in all_classes:
            confusion_matrix[true_class] = {}
            for pred_class in all_classes:
                count = ((y_true_series == true_class) & (y_pred_series == pred_class)).sum()
                confusion_matrix[true_class][pred_class] = int(count)

        # Calculate overall accuracy
        accuracy = (y_true_series == y_pred_series).mean()

        # 按照 sunfa.md 文档重新计算指标
        # 根据 sunfa.md 文档的定义重新计算总体指标
        # 总 TP：所有正向类别的正确预测之和
        total_tp = 0
        for pos_class in positive_classes:
            total_tp += ((y_true_series == pos_class) & (y_pred_series == pos_class)).sum()

        # 总 FP：被错误预测为正向意图的样本总数
        # 包括：真实是none但被预测为正向类 + 真实是正向类A但被预测为正向类B
        total_fp = 0
        for pos_class in positive_classes:
            # none被误判为该正向类
            if none_class:
                total_fp += ((y_true_series == none_class) & (y_pred_series == pos_class)).sum()
            # 其他正向类被误判为该正向类
            for other_pos_class in positive_classes:
                if other_pos_class != pos_class:
                    total_fp += ((y_true_series == other_pos_class) & (y_pred_series == pos_class)).sum()

        # 总 FN：真实正向意图被误判为none的样本总数
        total_fn = 0
        if none_class:
            for pos_class in positive_classes:
                total_fn += ((y_true_series == pos_class) & (y_pred_series == none_class)).sum()

        # 总 TN：正确预测为none的数量
        total_tn = 0
        if none_class:
            total_tn = ((y_true_series == none_class) & (y_pred_series == none_class)).sum()

        # Calculate per-class metrics
        per_class_metrics = {}

        for class_name in all_classes:
            # True Positive, False Positive, False Negative
            tp = ((y_true_series == class_name) & (y_pred_series == class_name)).sum()
            fp = ((y_true_series != class_name) & (y_pred_series == class_name)).sum()
            fn = ((y_true_series == class_name) & (y_pred_series != class_name)).sum()

            # Precision = TP / (TP + FP)
            precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0

            # Recall = TP / (TP + FN)
            recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0

            # F1 Score = 2 * (precision * recall) / (precision + recall)
            f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0.0

            per_class_metrics[class_name] = {
                "precision": float(precision),
                "recall": float(recall),
                "f1_score": float(f1),
                "support": int((y_true_series == class_name).sum()),
                "tp": int(tp),
                "fp": int(fp),
                "fn": int(fn),
                "is_positive_class": class_name in positive_classes
            }

        # 根据 sunfa.md 文档计算核心指标
        # 召回率：在所有真实存在的正向意图样本中，模型成功识别出了多少
        overall_recall = total_tp / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0.0

        # 精确率：在模型预测为某个正向意图的所有样本中，有多少预测是正确的
        overall_precision = total_tp / (total_tp + total_fp) if (total_tp + total_fp) > 0 else 0.0

        # F1值：精确率和召回率的调和平均数
        overall_f1 = 2 * (overall_precision * overall_recall) / (overall_precision + overall_recall) if (overall_precision + overall_recall) > 0 else 0.0

        # Calculate macro averages (仅针对正向类别)
        if positive_classes:
            macro_precision = sum(per_class_metrics[cls]["precision"] for cls in positive_classes) / len(positive_classes)
            macro_recall = sum(per_class_metrics[cls]["recall"] for cls in positive_classes) / len(positive_classes)
            macro_f1 = sum(per_class_metrics[cls]["f1_score"] for cls in positive_classes) / len(positive_classes)
        else:
            macro_precision = macro_recall = macro_f1 = 0.0

        # Calculate weighted averages (仅针对正向类别)
        total_positive_support = sum(per_class_metrics[cls]["support"] for cls in positive_classes)
        if total_positive_support > 0:
            weighted_precision = sum(
                per_class_metrics[cls]["precision"] * per_class_metrics[cls]["support"]
                for cls in positive_classes
            ) / total_positive_support
            weighted_recall = sum(
                per_class_metrics[cls]["recall"] * per_class_metrics[cls]["support"]
                for cls in positive_classes
            ) / total_positive_support
            weighted_f1 = sum(
                per_class_metrics[cls]["f1_score"] * per_class_metrics[cls]["support"]
                for cls in positive_classes
            ) / total_positive_support
        else:
            weighted_precision = weighted_recall = weighted_f1 = 0.0

        return {
            "model_name": model_name,
            # 传统准确率（添加警告说明）
            "accuracy": float(accuracy),
            "accuracy_warning": "准确率可能受类别不平衡影响，建议重点关注F1、Precision、Recall指标",

            # 核心指标（基于正向意图识别任务）
            "precision": float(overall_precision),
            "recall": float(overall_recall),
            "f1_score": float(overall_f1),

            # 统计信息
            "positive_classes": positive_classes,
            "none_class": none_class,
            "total_positive_samples": int(total_tp + total_fn),

            # 详细的TP/FP/FN/TN统计
            "overall_metrics": {
                "tp": int(total_tp),
                "fp": int(total_fp),
                "fn": int(total_fn),
                "tn": int(total_tn)
            },

            # 平均指标（仅针对正向类别）
            "macro_avg": {
                "precision": float(macro_precision),
                "recall": float(macro_recall),
                "f1_score": float(macro_f1)
            },
            "weighted_avg": {
                "precision": float(weighted_precision),
                "recall": float(weighted_recall),
                "f1_score": float(weighted_f1)
            },
            "per_class_metrics": per_class_metrics,
            "confusion_matrix": confusion_matrix,
            "total_samples": int(len(y_true)),
            "methodology": "根据sunfa.md文档，将'none'视为负例，其他类别视为正例，重点评估正向意图识别能力"
        }