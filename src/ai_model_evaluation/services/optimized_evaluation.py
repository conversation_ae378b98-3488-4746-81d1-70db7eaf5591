"""
高性能优化的评估引擎
专注于提升评估速度和资源利用率
"""

import asyncio
import time
from typing import Callable, Dict, List, Optional, Tuple
from collections import defaultdict
import logging

from ..models.core import (
    EvaluationTask, EvaluationRow, ResultRow, ModelConfig, Provider, 
    PromptTemplate, APIRequest, APIResponse, TaskStatus
)
from ..utils.exceptions import EvaluationError, ConfigurationError
from ..utils.helpers import generate_id
from .optimized_api_client import HighPerformanceAPIClientManager
from .prompt_generator import PromptGenerator
from .file_handler import FileHandler


class OptimizedEvaluationEngine:
    """高性能优化的评估引擎"""
    
    def __init__(self, 
                 config_manager=None,
                 global_max_concurrent: int = None,  # 从配置文件读取
                 batch_size: int = None,  # 从配置文件读取
                 enable_streaming: bool = None,  # 从配置文件读取
                 memory_optimization: bool = None):  # 从配置文件读取
        """
        初始化优化的评估引擎
        
        参数:
            config_manager: 配置管理器实例
            global_max_concurrent: 全局最大并发数（可选，从配置读取）
            batch_size: 批处理大小（可选，从配置读取）
            enable_streaming: 是否启用流式处理（可选，从配置读取）
            memory_optimization: 是否启用内存优化（可选，从配置读取）
        """
        self.config_manager = config_manager
        
        # 从配置文件读取性能设置，如果没有配置管理器则使用默认值
        if config_manager:
            perf_config = config_manager.get_performance_config()
            global_perf = perf_config.get('global_performance', {})
            batch_config = perf_config.get('batch_processing', {})
            
            self.global_max_concurrent = global_max_concurrent or global_perf.get('max_concurrent_requests', 800)
            
            batch_size_config = batch_config.get('batch_size', {})
            self.batch_size = batch_size or batch_size_config.get('default', 150)
            
            self.enable_streaming = enable_streaming if enable_streaming is not None else batch_config.get('enable_streaming', True)
            
            memory_config = batch_config.get('memory_optimization', {})
            self.memory_optimization = memory_optimization if memory_optimization is not None else memory_config.get('enable', True)
        else:
            # 使用提升后的默认值
            self.global_max_concurrent = global_max_concurrent or 800
            self.batch_size = batch_size or 150
            self.enable_streaming = enable_streaming if enable_streaming is not None else True
            self.memory_optimization = memory_optimization if memory_optimization is not None else True
        
        self.prompt_generator = PromptGenerator()
        self.file_handler = FileHandler()
        self.client_manager = HighPerformanceAPIClientManager(self.global_max_concurrent, config_manager)
        
        self._progress_callback: Optional[Callable[[float], None]] = None
        self._current_task: Optional[EvaluationTask] = None
        self._performance_stats = {}
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
    
    async def run_evaluation_optimized(self, 
                                     task: EvaluationTask, 
                                     providers: List[Provider], 
                                     models: List[ModelConfig], 
                                     template: PromptTemplate,
                                     evaluation_data: List[EvaluationRow]) -> List[ResultRow]:
        """
        运行优化的评估任务
        
        参数:
            task: 评估任务配置
            providers: 模型的提供商列表
            models: 要评估的模型列表
            template: 要使用的提示模板
            evaluation_data: 评估数据行
            
        返回:
            结果行列表
        """
        self._current_task = task
        start_time = time.time()
        
        try:
            # 验证输入
            self._validate_evaluation_inputs(providers, models, template, evaluation_data)
            
            # 设置API客户端
            await self._setup_optimized_api_clients(providers)
            
            # 更新任务状态
            task.status = TaskStatus.RUNNING
            
            # 选择处理策略
            if self.enable_streaming and len(evaluation_data) > self.batch_size:
                results = await self._process_streaming(evaluation_data, models, template)
            else:
                results = await self._process_batch_optimized(evaluation_data, models, template)
            
            # 更新任务状态
            task.status = TaskStatus.COMPLETED
            task.completed_at = time.time()
            
            # 记录性能统计
            total_time = time.time() - start_time
            self._record_performance_stats(len(evaluation_data), len(models), total_time)
            
            return results
            
        except Exception as e:
            task.status = TaskStatus.FAILED
            task.error_message = str(e)
            self.logger.error(f"Evaluation failed: {e}")
            raise EvaluationError(f"Evaluation failed: {e}")
        
        finally:
            await self.client_manager.close_all()
            self._current_task = None
    
    async def _process_batch_optimized(self, 
                                     evaluation_data: List[EvaluationRow], 
                                     models: List[ModelConfig], 
                                     template: PromptTemplate) -> List[ResultRow]:
        """
        优化的批处理
        
        参数:
            evaluation_data: 评估数据
            models: 模型列表
            template: 提示模板
            
        返回:
            结果行列表
        """
        if not evaluation_data:
            return []
        
        # 预生成所有提示词
        prompts = self.prompt_generator.generate_prompts_batch(template, evaluation_data)
        
        # 创建结果行
        results = [ResultRow(data=row.data.copy()) for row in evaluation_data]
        
        # 按提供商分组请求
        requests_by_provider = self._group_requests_by_provider(evaluation_data, models, prompts)
        
        # 执行跨提供商的并发请求
        responses_by_provider = await self.client_manager.batch_requests_across_providers(requests_by_provider)
        
        # 处理响应并更新结果
        self._process_responses(responses_by_provider, results, models)
        
        return results
    
    async def _process_streaming(self, 
                               evaluation_data: List[EvaluationRow], 
                               models: List[ModelConfig], 
                               template: PromptTemplate) -> List[ResultRow]:
        """
        流式处理大批量数据
        
        参数:
            evaluation_data: 评估数据
            models: 模型列表
            template: 提示模板
            
        返回:
            结果行列表
        """
        results = []
        total_batches = (len(evaluation_data) + self.batch_size - 1) // self.batch_size
        
        for batch_idx in range(0, len(evaluation_data), self.batch_size):
            batch_end = min(batch_idx + self.batch_size, len(evaluation_data))
            batch_data = evaluation_data[batch_idx:batch_end]
            
            self.logger.info(f"Processing batch {batch_idx // self.batch_size + 1}/{total_batches}")
            
            # 处理当前批次
            batch_results = await self._process_batch_optimized(batch_data, models, template)
            results.extend(batch_results)
            
            # 更新进度
            progress = batch_end / len(evaluation_data)
            self._update_progress(progress)
            
            # 内存优化：强制垃圾回收
            if self.memory_optimization and batch_idx % (self.batch_size * 5) == 0:
                import gc
                gc.collect()
        
        return results
    
    def _group_requests_by_provider(self, 
                                  evaluation_data: List[EvaluationRow], 
                                  models: List[ModelConfig], 
                                  prompts: List[str]) -> Dict[str, List[APIRequest]]:
        """
        按提供商分组请求以实现最优并发
        
        参数:
            evaluation_data: 评估数据
            models: 模型列表
            prompts: 提示词列表
            
        返回:
            按提供商分组的请求字典
        """
        requests_by_provider = defaultdict(list)
        
        for model in models:
            for i, prompt in enumerate(prompts):
                request = APIRequest(
                    model=model,
                    prompt=prompt,
                    request_id=f"{model.id}_{i}_{int(time.time() * 1000)}"
                )
                requests_by_provider[model.provider_id].append(request)
        
        return dict(requests_by_provider)
    
    def _process_responses(self, 
                         responses_by_provider: Dict[str, List[APIResponse]], 
                         results: List[ResultRow], 
                         models: List[ModelConfig]):
        """
        处理响应并更新结果
        
        参数:
            responses_by_provider: 按提供商分组的响应
            results: 结果行列表
            models: 模型列表
        """
        # 创建模型到提供商的映射
        model_to_provider = {model.id: model.provider_id for model in models}
        
        # 按模型重新组织响应
        responses_by_model = defaultdict(list)
        for provider_id, responses in responses_by_provider.items():
            provider_models = [m for m in models if m.provider_id == provider_id]
            
            # 假设响应按模型顺序排列
            responses_per_model = len(responses) // len(provider_models) if provider_models else 0
            
            for model_idx, model in enumerate(provider_models):
                start_idx = model_idx * responses_per_model
                end_idx = start_idx + responses_per_model
                model_responses = responses[start_idx:end_idx]
                responses_by_model[model.id] = model_responses
        
        # 更新结果
        for model in models:
            model_responses = responses_by_model.get(model.id, [])
            
            for i, response in enumerate(model_responses):
                if i < len(results):
                    results[i].add_result(
                        model_id=model.id,
                        result=response.content,
                        execution_time=response.execution_time,
                        error=response.error_message if not response.success else None
                    )
    
    async def _setup_optimized_api_clients(self, providers: List[Provider]):
        """设置优化的API客户端"""
        for provider in providers:
            if self.client_manager.get_client(provider.id) is None:
                self.client_manager.add_client(provider)
    
    def _validate_evaluation_inputs(self, 
                                  providers: List[Provider], 
                                  models: List[ModelConfig],
                                  template: PromptTemplate, 
                                  evaluation_data: List[EvaluationRow]):
        """验证评估输入"""
        if not providers:
            raise ConfigurationError("No providers provided")
        
        if not models:
            raise ConfigurationError("No models provided")
        
        if not evaluation_data:
            raise ConfigurationError("No evaluation data provided")
        
        # 验证模型与提供商的对应关系
        provider_ids = {p.id for p in providers}
        for model in models:
            if model.provider_id not in provider_ids:
                raise ConfigurationError(f"Model {model.id} references unknown provider {model.provider_id}")
        
        # 验证模板兼容性
        errors = self.prompt_generator.validate_template_compatibility(template, evaluation_data)
        if errors:
            raise ConfigurationError(f"Template validation failed: {'; '.join(errors)}")
    
    def _record_performance_stats(self, data_count: int, model_count: int, total_time: float):
        """记录性能统计"""
        total_requests = data_count * model_count
        
        self._performance_stats = {
            'data_count': data_count,
            'model_count': model_count,
            'total_requests': total_requests,
            'total_time': total_time,
            'requests_per_second': total_requests / max(total_time, 0.001),
            'avg_time_per_request': total_time / max(total_requests, 1),
            'client_performance': self.client_manager.get_global_performance_summary()
        }
        
        self.logger.info(f"Performance stats: {self._performance_stats}")
    
    def get_performance_stats(self) -> Dict:
        """获取性能统计"""
        return self._performance_stats
    
    def _update_progress(self, progress: float):
        """更新进度"""
        if self._progress_callback:
            self._progress_callback(min(1.0, max(0.0, progress)))
    
    def track_progress(self, callback: Callable[[float], None]):
        """设置进度跟踪回调"""
        self._progress_callback = callback


class SmartBatchProcessor:
    """智能批处理器"""
    
    def __init__(self, 
                 adaptive_batch_size: bool = True,
                 min_batch_size: int = 10,
                 max_batch_size: int = 200):
        """
        初始化智能批处理器
        
        参数:
            adaptive_batch_size: 是否启用自适应批处理大小
            min_batch_size: 最小批处理大小
            max_batch_size: 最大批处理大小
        """
        self.adaptive_batch_size = adaptive_batch_size
        self.min_batch_size = min_batch_size
        self.max_batch_size = max_batch_size
        self._performance_history = []
    
    def calculate_optimal_batch_size(self, 
                                   data_size: int, 
                                   model_count: int, 
                                   provider_count: int) -> int:
        """
        计算最优批处理大小
        
        参数:
            data_size: 数据大小
            model_count: 模型数量
            provider_count: 提供商数量
            
        返回:
            最优批处理大小
        """
        if not self.adaptive_batch_size:
            return min(self.max_batch_size, max(self.min_batch_size, data_size // 10))
        
        # 基于历史性能调整
        if self._performance_history:
            avg_performance = sum(self._performance_history[-5:]) / len(self._performance_history[-5:])
            if avg_performance > 100:  # 高性能，增加批处理大小
                base_size = min(self.max_batch_size, 150)
            elif avg_performance > 50:  # 中等性能
                base_size = 100
            else:  # 低性能，减少批处理大小
                base_size = max(self.min_batch_size, 50)
        else:
            base_size = 100
        
        # 根据数据规模和模型数量调整
        total_requests = data_size * model_count
        if total_requests > 10000:
            return min(self.max_batch_size, base_size + 50)
        elif total_requests > 1000:
            return base_size
        else:
            return max(self.min_batch_size, base_size // 2)
    
    def record_performance(self, requests_per_second: float):
        """记录性能数据"""
        self._performance_history.append(requests_per_second)
        # 只保留最近的10次记录
        if len(self._performance_history) > 10:
            self._performance_history = self._performance_history[-10:]


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        """初始化性能监控器"""
        self.start_time = None
        self.metrics = {
            'requests_sent': 0,
            'requests_completed': 0,
            'requests_failed': 0,
            'total_response_time': 0.0,
            'peak_concurrent': 0,
            'current_concurrent': 0
        }
    
    def start_monitoring(self):
        """开始监控"""
        self.start_time = time.time()
    
    def record_request_sent(self):
        """记录请求发送"""
        self.metrics['requests_sent'] += 1
        self.metrics['current_concurrent'] += 1
        self.metrics['peak_concurrent'] = max(
            self.metrics['peak_concurrent'], 
            self.metrics['current_concurrent']
        )
    
    def record_request_completed(self, response_time: float, success: bool):
        """记录请求完成"""
        self.metrics['current_concurrent'] -= 1
        self.metrics['total_response_time'] += response_time
        
        if success:
            self.metrics['requests_completed'] += 1
        else:
            self.metrics['requests_failed'] += 1
    
    def get_current_stats(self) -> Dict:
        """获取当前统计"""
        if self.start_time is None:
            return {}
        
        elapsed_time = time.time() - self.start_time
        completed_requests = self.metrics['requests_completed']
        
        return {
            'elapsed_time': elapsed_time,
            'requests_per_second': completed_requests / max(elapsed_time, 0.001),
            'success_rate': completed_requests / max(self.metrics['requests_sent'], 1),
            'avg_response_time': self.metrics['total_response_time'] / max(completed_requests, 1),
            'peak_concurrent': self.metrics['peak_concurrent'],
            'current_concurrent': self.metrics['current_concurrent'],
            **self.metrics
        }