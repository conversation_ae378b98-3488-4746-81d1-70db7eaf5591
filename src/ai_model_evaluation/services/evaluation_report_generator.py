"""
评估报告生成器 - 专门用于auto_evaluate.py和CLI的统一报告生成
从auto_evaluate.py中抽离的分析和报告生成功能
"""

import json
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


class EvaluationReportGenerator:
    """统一的评估报告生成器"""
    
    def __init__(self):
        """初始化报告生成器"""
        pass
    
    def generate_analysis_report(self, result_file: Path, output_dir: Path) -> Dict[str, str]:
        """
        生成分析报告
        
        Args:
            result_file: 结果CSV文件路径
            output_dir: 输出目录
            
        Returns:
            生成的报告文件路径字典
        """
        try:
            print("📈 正在生成分析报告...")
            
            # 读取结果文件
            df = pd.read_csv(result_file)
            
            # 生成分析数据
            analysis_data = self._analyze_results(df)
            
            # 生成JSON报告
            json_file = output_dir / "analysis_report.json"
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(analysis_data, f, ensure_ascii=False, indent=2)
            
            # 生成HTML报告
            html_file = output_dir / "analysis_report.html"
            self._generate_html_report(analysis_data, html_file)
            
            print(f"📊 分析报告已生成:")
            print(f"   - JSON: {json_file}")
            print(f"   - HTML: {html_file}")
            
            return {
                "json": str(json_file),
                "html": str(html_file)
            }
            
        except Exception as e:
            logger.error(f"生成分析报告失败: {e}")
            print(f"❌ 生成分析报告失败: {e}")
            return {}
    
    def generate_classification_metrics_report(self, result_file: Path, output_dir: Path) -> Dict[str, str]:
        """
        生成分类指标报告
        
        Args:
            result_file: 结果CSV文件路径
            output_dir: 输出目录
            
        Returns:
            生成的报告文件路径字典
        """
        try:
            print("📊 正在生成分类指标报告...")
            
            # 读取结果文件
            df = pd.read_csv(result_file)
            
            # 获取所有模型列
            model_columns = [col for col in df.columns if col.endswith('_result')]
            
            # 生成每个模型的分类指标
            all_metrics = {}
            for model_col in model_columns:
                model_name = model_col.replace('_result', '')
                print(f"   分析模型: {model_name}")
                
                metrics = self._calculate_classification_metrics(df, model_col)
                all_metrics[model_name] = metrics
            
            # 生成JSON详细报告
            json_file = output_dir / "classification_metrics_report.json"
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(all_metrics, f, ensure_ascii=False, indent=2)
            
            # 生成HTML可视化报告
            html_file = output_dir / "classification_metrics_report.html"
            self._generate_classification_html_report(all_metrics, html_file)
            
            # 生成文本摘要报告
            summary_file = output_dir / "classification_metrics_summary.txt"
            self._generate_classification_summary(all_metrics, summary_file)
            
            print(f"📊 分类指标报告已生成:")
            print(f"   - JSON详细报告: {json_file}")
            print(f"   - HTML可视化报告: {html_file}")
            print(f"   - 文本摘要报告: {summary_file}")
            
            return {
                "json": str(json_file),
                "html": str(html_file),
                "summary": str(summary_file)
            }
            
        except Exception as e:
            logger.error(f"生成分类指标报告失败: {e}")
            print(f"❌ 生成分类指标报告失败: {e}")
            return {}
    
    def _analyze_results(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析结果数据 - 生成与原版本相同详细程度的分析"""
        analysis = {
            "file_info": {
                "total_rows": len(df),
                "columns": list(df.columns)
            },
            "model_performance": {}
        }

        # 获取模型列（排除expected_result）
        model_columns = [col for col in df.columns if col.endswith('_result') and col != 'expected_result']

        for model_col in model_columns:
            model_name = model_col.replace('_result', '')

            # 计算成功率 - 只要有返回结果就算成功，包括'None'字符串
            predicted_str = df[model_col].astype(str).str.strip()
            valid_results = (
                df[model_col].notna() &  # 不是pandas NaN
                (predicted_str != '') &  # 不是空字符串
                (predicted_str != 'nan')  # 不是字符串'nan'
            )
            success_count = valid_results.sum()
            total_rows = len(df)
            success_rate = success_count / total_rows if total_rows > 0 else 0

            # 获取执行时间信息
            time_col = f"{model_name}_execution_time"
            avg_time = df[time_col].mean() if time_col in df.columns else 0

            # 结果分布
            if success_count > 0:
                result_distribution = df[valid_results][model_col].value_counts().to_dict()
            else:
                result_distribution = {}

            # 计算详细的分类指标
            classification_metrics = self._calculate_detailed_classification_metrics(df, model_col)

            analysis["model_performance"][model_name] = {
                "total_requests": total_rows,
                "successful_requests": int(success_count),
                "success_rate": float(success_rate),
                "average_execution_time": float(avg_time),
                "result_distribution": result_distribution,
                "classification_metrics": classification_metrics
            }

        return analysis
    
    def _calculate_detailed_classification_metrics(self, df: pd.DataFrame, model_col: str) -> Dict[str, Any]:
        """
        计算详细的分类指标

        根据 sunfa.md 文档要求，将"none"类别视为负例，其他类别视为正例
        重新定义 TP、FP、FN、TN 的计算方式，专注于正向意图识别能力
        """
        try:
            if 'expected_result' not in df.columns:
                return {"error": "No expected_result column found"}

            # 获取期望结果和预测结果
            expected = df['expected_result'].fillna('').astype(str)
            predicted = df[model_col].fillna('').astype(str)

            # 计算基本准确率
            correct = (expected == predicted).sum()
            total = len(df)
            accuracy = correct / total if total > 0 else 0

            # 获取所有唯一的类别
            all_labels = sorted(list(set(expected.tolist() + predicted.tolist())))

            # 识别正向类别（非"none"的类别）
            positive_classes = [label for label in all_labels if label.lower() not in ['none', '', 'null', 'na']]
            none_class = 'none' if 'none' in all_labels else None

            # 如果没有找到明确的"none"类，尝试其他可能的表示
            if none_class is None:
                for label in all_labels:
                    if label.lower() in ['', 'null', 'na', 'unknown', '无', '空']:
                        none_class = label
                        positive_classes = [l for l in all_labels if l != label]
                        break

            # 按照 sunfa.md 文档重新计算指标
            # 将任务视为：正例（所有正向意图类别）vs 负例（none类别）

            # 计算总体的 TP, FP, FN, TN（针对正向意图识别任务）
            total_tp = 0  # 正确识别的正向意图总数
            total_fp = 0  # 错误预测为正向意图的总数
            total_fn = 0  # 漏掉的正向意图总数
            total_tn = 0  # 正确识别的"none"数量

            # 计算每个正向类别的指标（用于详细分析）
            per_class_metrics = {}

            for label in all_labels:
                # 计算传统的每类别指标（保留用于详细分析）
                tp = ((expected == label) & (predicted == label)).sum()
                fp = ((expected != label) & (predicted == label)).sum()
                fn = ((expected == label) & (predicted != label)).sum()
                tn = ((expected != label) & (predicted != label)).sum()
                support = (expected == label).sum()

                # 计算传统的 precision, recall, f1
                precision = tp / (tp + fp) if (tp + fp) > 0 else 0
                recall = tp / (tp + fn) if (tp + fn) > 0 else 0
                f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0

                per_class_metrics[label] = {
                    "precision": float(precision),
                    "recall": float(recall),
                    "f1_score": float(f1_score),
                    "support": int(support),
                    "tp": int(tp),
                    "fp": int(fp),
                    "fn": int(fn),
                    "is_positive_class": label in positive_classes
                }

                # 累加到总体指标（仅针对正向类别）
                if label in positive_classes:
                    total_tp += tp
                    # 对于正向类别，FP包括：被误判为该类的其他正向类 + 被误判为该类的none
                    # 这里的fp已经是正确的，因为它计算的是"不是该类但被预测为该类"的数量
                    total_fp += fp
                    # FN是该正向类被误判为其他类（包括none）的数量
                    total_fn += fn
                elif label == none_class:
                    # 对于none类，TN是正确预测为none的数量
                    total_tn += tp  # 这里的tp实际上是none类的正确预测

            # 根据 sunfa.md 文档计算核心指标
            # 召回率：在所有真实存在的正向意图样本中，模型成功识别出了多少
            overall_recall = total_tp / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0

            # 精确率：在模型预测为某个正向意图的所有样本中，有多少预测是正确的
            overall_precision = total_tp / (total_tp + total_fp) if (total_tp + total_fp) > 0 else 0

            # F1值：精确率和召回率的调和平均数
            overall_f1 = 2 * (overall_precision * overall_recall) / (overall_precision + overall_recall) if (overall_precision + overall_recall) > 0 else 0

            # 计算宏平均（仅针对正向类别）
            if positive_classes:
                macro_precision = sum(per_class_metrics[cls]["precision"] for cls in positive_classes) / len(positive_classes)
                macro_recall = sum(per_class_metrics[cls]["recall"] for cls in positive_classes) / len(positive_classes)
                macro_f1 = sum(per_class_metrics[cls]["f1_score"] for cls in positive_classes) / len(positive_classes)
            else:
                macro_precision = macro_recall = macro_f1 = 0

            # 计算加权平均（仅针对正向类别）
            total_positive_support = sum(per_class_metrics[cls]["support"] for cls in positive_classes)
            if total_positive_support > 0:
                weighted_precision = sum(per_class_metrics[cls]["precision"] * per_class_metrics[cls]["support"]
                                       for cls in positive_classes) / total_positive_support
                weighted_recall = sum(per_class_metrics[cls]["recall"] * per_class_metrics[cls]["support"]
                                    for cls in positive_classes) / total_positive_support
                weighted_f1 = sum(per_class_metrics[cls]["f1_score"] * per_class_metrics[cls]["support"]
                                for cls in positive_classes) / total_positive_support
            else:
                weighted_precision = weighted_recall = weighted_f1 = 0

            # 重新定义平均指标结构
            macro_avg = {
                "precision": float(macro_precision),
                "recall": float(macro_recall),
                "f1_score": float(macro_f1)
            }

            weighted_avg = {
                "precision": float(weighted_precision),
                "recall": float(weighted_recall),
                "f1_score": float(weighted_f1)
            }

            # 生成混淆矩阵
            confusion_matrix = self._generate_confusion_matrix(expected, predicted, all_labels)

            # 计算有效预测数
            valid_predictions = (predicted != '').sum()

            return {
                # 传统准确率（添加警告说明）
                "accuracy": float(accuracy),
                "accuracy_warning": "准确率可能受类别不平衡影响，建议重点关注F1、Precision、Recall指标",

                # 核心指标（基于正向意图识别任务）
                "precision": float(overall_precision),
                "recall": float(overall_recall),
                "f1_score": float(overall_f1),

                # 统计信息
                "total_samples": int(total),
                "valid_predictions": int(valid_predictions),
                "positive_classes": positive_classes,
                "none_class": none_class,
                "total_positive_samples": int(total_tp + total_fn),

                # 详细的TP/FP/FN/TN统计
                "overall_metrics": {
                    "tp": int(total_tp),
                    "fp": int(total_fp),
                    "fn": int(total_fn),
                    "tn": int(total_tn)
                },

                # 平均指标（仅针对正向类别）
                "macro_avg": macro_avg,
                "weighted_avg": weighted_avg,

                # 每个类别的详细指标
                "per_class_metrics": per_class_metrics,
                "confusion_matrix": confusion_matrix,

                # 说明信息
                "note": f"基于{total}个样本的正向意图识别分析，其中{len(positive_classes)}个正向类别，{valid_predictions}个有效预测",
                "methodology": "根据sunfa.md文档，将'none'视为负例，其他类别视为正例，重点评估正向意图识别能力"
            }

        except Exception as e:
            return {"error": str(e)}

    def _generate_confusion_matrix(self, expected, predicted, labels):
        """生成混淆矩阵"""
        matrix = {}
        for true_label in labels:
            matrix[true_label] = {}
            for pred_label in labels:
                count = ((expected == true_label) & (predicted == pred_label)).sum()
                matrix[true_label][pred_label] = int(count)
        return matrix

    def _calculate_classification_metrics(self, df: pd.DataFrame, model_col: str) -> Dict[str, Any]:
        """计算分类指标 - 用于分类指标报告"""
        if 'expected_result' not in df.columns:
            return {"error": "No expected_result column found"}

        expected = df['expected_result'].fillna('').astype(str)
        predicted = df[model_col].fillna('').astype(str)

        # 基本统计
        total_samples = len(df)
        valid_samples = total_samples  # 假设所有样本都有效
        filtered_samples = 0

        # 详细调试信息
        debug_info = {
            "data_stats": {
                "total_samples": total_samples,
                "valid_samples": valid_samples,
                "filtered_samples": filtered_samples
            },
            "sample_analysis": [],
            "valid_sample_comparison": [],
            "accuracy_calculation": {}
        }

        # 逐样本分析
        correct_count = 0
        for i, (exp, pred) in enumerate(zip(expected, predicted)):
            is_correct = exp == pred
            if is_correct:
                correct_count += 1

            debug_info["sample_analysis"].append({
                "sample_index": i,
                "expected": exp,
                "predicted": pred,
                "is_correct": is_correct,
                "status": "✅" if is_correct else "❌"
            })

            debug_info["valid_sample_comparison"].append({
                "sample_index": i,
                "expected": exp,
                "predicted": pred,
                "is_correct": is_correct,
                "status": "✅" if is_correct else "❌"
            })

        # 准确率计算
        accuracy = correct_count / valid_samples if valid_samples > 0 else 0
        debug_info["accuracy_calculation"] = {
            "correct_predictions": correct_count,
            "total_valid_samples": valid_samples,
            "accuracy": accuracy,
            "accuracy_percentage": f"{accuracy * 100:.2f}%"
        }

        return debug_info
    
    def _generate_html_report(self, analysis_data: Dict[str, Any], html_file: Path):
        """生成HTML分析报告 - 与原版本相同的详细程度"""
        file_info = analysis_data.get('file_info', {})

        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI模型评估分析报告</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        .header {{
            text-align: center;
            color: #333;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }}
        .model-section {{
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }}
        .metric {{
            display: inline-block;
            margin: 10px 15px;
            padding: 10px;
            background-color: #e8f5e8;
            border-radius: 5px;
            min-width: 150px;
            text-align: center;
        }}
        .metric-value {{
            font-size: 24px;
            font-weight: bold;
            color: #2e7d32;
        }}
        .metric-label {{
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }}
        .distribution {{
            margin-top: 15px;
        }}
        .distribution-item {{
            display: inline-block;
            margin: 5px;
            padding: 5px 10px;
            background-color: #fff3e0;
            border-radius: 3px;
            border: 1px solid #ffcc02;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }}
        th, td {{
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }}
        th {{
            background-color: #4CAF50;
            color: white;
        }}
        tr:nth-child(even) {{
            background-color: #f2f2f2;
        }}
        .comparison-table {{
            margin: 20px 0;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        .comparison-table th {{
            background-color: #2196F3;
            color: white;
            font-weight: bold;
            text-align: center;
        }}
        .comparison-table td {{
            text-align: center;
            padding: 12px;
        }}
        .comparison-table td:first-child {{
            text-align: left;
            font-weight: bold;
        }}
        .status-excellent {{
            color: #4CAF50;
            font-weight: bold;
        }}
        .status-good {{
            color: #FF9800;
            font-weight: bold;
        }}
        .status-poor {{
            color: #F44336;
            font-weight: bold;
        }}
        .section {{
            margin: 30px 0;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 AI模型评估分析报告</h1>
            <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p>总样本数: {file_info.get('total_rows', 0)}</p>
        </div>

        <div class="section">
            <h2>📊 模型表现对比</h2>
            <table class="comparison-table">
                <tr>
                    <th>模型名称</th>
                    <th>准确率</th>
                    <th>精确率</th>
                    <th>召回率</th>
                    <th>F1分数</th>
                    <th>状态</th>
                </tr>
"""

        # 生成模型对比表格数据
        model_performance = analysis_data.get('model_performance', {})

        # 按准确率排序模型
        sorted_models = sorted(
            model_performance.items(),
            key=lambda x: x[1].get('classification_metrics', {}).get('accuracy', 0),
            reverse=True
        )

        for model_name, performance in sorted_models:
            classification = performance.get('classification_metrics', {})
            accuracy = classification.get('accuracy', 0) * 100

            # 获取加权平均指标
            weighted_avg = classification.get('weighted_avg', {})
            precision = weighted_avg.get('precision', 0) * 100
            recall = weighted_avg.get('recall', 0) * 100
            f1_score = weighted_avg.get('f1_score', 0) * 100

            # 确定状态
            if accuracy >= 80:
                status = '<span class="status-excellent">优秀</span>'
            elif accuracy >= 60:
                status = '<span class="status-good">良好</span>'
            else:
                status = '<span class="status-poor">待改进</span>'

            html_content += f"""
                <tr>
                    <td><strong>{model_name}</strong></td>
                    <td>{accuracy:.2f}%</td>
                    <td>{precision:.2f}%</td>
                    <td>{recall:.2f}%</td>
                    <td>{f1_score:.2f}%</td>
                    <td>{status}</td>
                </tr>
"""

        html_content += """
            </table>
        </div>
"""

        # 添加模型性能部分
        for model_name, performance in analysis_data.get('model_performance', {}).items():
            classification = performance.get('classification_metrics', {})
            accuracy_percent = classification.get('accuracy', 0) * 100

            # 获取加权平均指标用于详细显示
            weighted_avg = classification.get('weighted_avg', {})
            precision_percent = weighted_avg.get('precision', 0) * 100
            recall_percent = weighted_avg.get('recall', 0) * 100
            f1_percent = weighted_avg.get('f1_score', 0) * 100

            html_content += f"""
        <div class="model-section">
            <h2>📊 {model_name}</h2>

            <div class="metric">
                <div class="metric-value">{accuracy_percent:.1f}%</div>
                <div class="metric-label">准确率</div>
            </div>

            <div class="metric">
                <div class="metric-value">{precision_percent:.1f}%</div>
                <div class="metric-label">精确率</div>
            </div>

            <div class="metric">
                <div class="metric-value">{recall_percent:.1f}%</div>
                <div class="metric-label">召回率</div>
            </div>

            <div class="metric">
                <div class="metric-value">{f1_percent:.1f}%</div>
                <div class="metric-label">F1分数</div>
            </div>

            <div class="metric">
                <div class="metric-value">{performance.get('average_execution_time', 0):.2f}s</div>
                <div class="metric-label">平均执行时间</div>
            </div>
"""

            # 添加结果分布
            result_distribution = performance.get('result_distribution', {})
            if result_distribution:
                html_content += """
            <div class="distribution">
                <h3>结果分布:</h3>
"""
                for result, count in result_distribution.items():
                    html_content += f"""
                <div class="distribution-item">
                    <strong>{result}</strong>: {count}次
                </div>
"""
                html_content += """
            </div>
"""

            # 添加分类指标表格
            if 'per_class_metrics' in classification:
                html_content += """
            <h3>分类指标详情:</h3>
            <table>
                <tr>
                    <th>类别</th>
                    <th>精确率</th>
                    <th>召回率</th>
                    <th>F1分数</th>
                    <th>支持数</th>
                </tr>
"""
                for class_name, metrics in classification['per_class_metrics'].items():
                    html_content += f"""
                <tr>
                    <td>{class_name}</td>
                    <td>{metrics.get('precision', 0):.3f}</td>
                    <td>{metrics.get('recall', 0):.3f}</td>
                    <td>{metrics.get('f1_score', 0):.3f}</td>
                    <td>{metrics.get('support', 0)}</td>
                </tr>
"""

                # 添加平均指标
                macro_avg = classification.get('macro_avg', {})
                weighted_avg = classification.get('weighted_avg', {})

                html_content += f"""
                <tr style="background-color: #e8f5e8;">
                    <td><strong>宏平均</strong></td>
                    <td>{macro_avg.get('precision', 0):.3f}</td>
                    <td>{macro_avg.get('recall', 0):.3f}</td>
                    <td>{macro_avg.get('f1_score', 0):.3f}</td>
                    <td>-</td>
                </tr>
                <tr style="background-color: #e8f5e8;">
                    <td><strong>加权平均</strong></td>
                    <td>{weighted_avg.get('precision', 0):.3f}</td>
                    <td>{weighted_avg.get('recall', 0):.3f}</td>
                    <td>{weighted_avg.get('f1_score', 0):.3f}</td>
                    <td>-</td>
                </tr>
"""
                html_content += """
            </table>
"""

            html_content += """
        </div>
"""

        html_content += """
    </div>
</body>
</html>
"""

        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(html_content)

    def _generate_classification_html_report(self, metrics_data: Dict[str, Any], html_file: Path):
        """生成分类指标HTML报告"""
        html_content = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分类指标详细报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .model-section { margin: 30px 0; border: 1px solid #ddd; padding: 20px; border-radius: 8px; }
        .accuracy { font-size: 20px; font-weight: bold; color: #2e8b57; }
        .sample-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 10px; }
        .sample-item { padding: 5px; border-radius: 3px; }
        .correct { background-color: #d4edda; }
        .incorrect { background-color: #f8d7da; }
    </style>
</head>
<body>
    <h1>🔍 分类指标详细报告</h1>
"""

        for model_name, metrics in metrics_data.items():
            if 'error' in metrics:
                continue

            accuracy_calc = metrics.get('accuracy_calculation', {})
            html_content += f"""
    <div class="model-section">
        <h2>📊 {model_name}</h2>
        <div class="accuracy">准确率: {accuracy_calc.get('accuracy_percentage', '0%')}</div>
        <p>正确预测: {accuracy_calc.get('correct_predictions', 0)} / {accuracy_calc.get('total_valid_samples', 0)}</p>

        <h3>样本分析</h3>
        <div class="sample-grid">
"""

            # 添加样本分析
            for sample in metrics.get('sample_analysis', [])[:20]:  # 只显示前20个样本
                css_class = "correct" if sample.get('is_correct') else "incorrect"
                html_content += f"""
            <div class="sample-item {css_class}">
                {sample.get('status', '')} 样本{sample.get('sample_index', 0)}:
                期望='{sample.get('expected', '')}' | 预测='{sample.get('predicted', '')}'
            </div>
"""

            html_content += """
        </div>
    </div>
"""

        html_content += """
</body>
</html>
"""

        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(html_content)

    def _generate_classification_summary(self, metrics_data: Dict[str, Any], summary_file: Path):
        """生成分类指标文本摘要"""
        summary_lines = [
            "🔍 分类指标摘要报告",
            "=" * 50,
            f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            ""
        ]

        # 按准确率排序模型
        model_accuracies = []
        for model_name, metrics in metrics_data.items():
            if 'error' not in metrics:
                accuracy_calc = metrics.get('accuracy_calculation', {})
                accuracy = accuracy_calc.get('accuracy', 0)
                model_accuracies.append((model_name, accuracy, accuracy_calc))

        model_accuracies.sort(key=lambda x: x[1], reverse=True)

        summary_lines.append("📊 模型性能排名:")
        for i, (model_name, accuracy, accuracy_calc) in enumerate(model_accuracies, 1):
            medal = "🥇" if i == 1 else "🥈" if i == 2 else "🥉" if i == 3 else f"{i}."
            summary_lines.append(
                f"{medal} {model_name}: {accuracy_calc.get('accuracy_percentage', '0%')} "
                f"({accuracy_calc.get('correct_predictions', 0)}/{accuracy_calc.get('total_valid_samples', 0)})"
            )

        summary_lines.extend([
            "",
            "详细分析请查看HTML和JSON报告文件。"
        ])

        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(summary_lines))
