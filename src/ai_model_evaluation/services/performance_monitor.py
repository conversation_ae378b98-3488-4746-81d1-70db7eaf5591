"""性能监控和指标收集系统
实时监控系统性能，收集详细指标，生成性能报告
"""

import asyncio
import time
import psutil
import threading
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from collections import defaultdict, deque
from datetime import datetime, timedelta
import json
from pathlib import Path
from loguru import logger


@dataclass
class MetricPoint:
    """指标数据点"""
    timestamp: float
    value: float
    tags: Dict[str, str] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'timestamp': self.timestamp,
            'value': self.value,
            'tags': self.tags
        }


@dataclass
class PerformanceMetrics:
    """性能指标"""
    # 请求指标
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    requests_per_second: float = 0.0
    avg_response_time: float = 0.0
    p95_response_time: float = 0.0
    p99_response_time: float = 0.0
    
    # 并发指标
    current_concurrent: int = 0
    max_concurrent: int = 0
    avg_concurrent: float = 0.0
    
    # 系统资源指标
    cpu_usage: float = 0.0
    memory_usage_mb: float = 0.0
    memory_usage_percent: float = 0.0
    disk_io_read_mb: float = 0.0
    disk_io_write_mb: float = 0.0
    network_io_sent_mb: float = 0.0
    network_io_recv_mb: float = 0.0
    
    # 缓存指标
    cache_hit_rate: float = 0.0
    cache_size: int = 0
    cache_memory_mb: float = 0.0
    
    # 批处理指标
    total_batches: int = 0
    avg_batch_size: float = 0.0
    batch_efficiency: float = 0.0
    avg_batch_wait_time: float = 0.0
    avg_batch_processing_time: float = 0.0
    
    # 错误指标
    error_rate: float = 0.0
    timeout_rate: float = 0.0
    retry_rate: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'requests': {
                'total': self.total_requests,
                'successful': self.successful_requests,
                'failed': self.failed_requests,
                'rps': self.requests_per_second,
                'avg_response_time': self.avg_response_time,
                'p95_response_time': self.p95_response_time,
                'p99_response_time': self.p99_response_time
            },
            'concurrency': {
                'current': self.current_concurrent,
                'max': self.max_concurrent,
                'avg': self.avg_concurrent
            },
            'system': {
                'cpu_usage': self.cpu_usage,
                'memory_usage_mb': self.memory_usage_mb,
                'memory_usage_percent': self.memory_usage_percent,
                'disk_io_read_mb': self.disk_io_read_mb,
                'disk_io_write_mb': self.disk_io_write_mb,
                'network_io_sent_mb': self.network_io_sent_mb,
                'network_io_recv_mb': self.network_io_recv_mb
            },
            'cache': {
                'hit_rate': self.cache_hit_rate,
                'size': self.cache_size,
                'memory_mb': self.cache_memory_mb
            },
            'batch': {
                'total_batches': self.total_batches,
                'avg_batch_size': self.avg_batch_size,
                'efficiency': self.batch_efficiency,
                'avg_wait_time': self.avg_batch_wait_time,
                'avg_processing_time': self.avg_batch_processing_time
            },
            'errors': {
                'error_rate': self.error_rate,
                'timeout_rate': self.timeout_rate,
                'retry_rate': self.retry_rate
            }
        }


class MetricsCollector:
    """指标收集器"""
    
    def __init__(self, max_points: int = 10000):
        self.max_points = max_points
        self.metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=max_points))
        self._lock = threading.Lock()
    
    def add_metric(self, name: str, value: float, tags: Optional[Dict[str, str]] = None):
        """添加指标数据点"""
        point = MetricPoint(
            timestamp=time.time(),
            value=value,
            tags=tags or {}
        )
        
        with self._lock:
            self.metrics[name].append(point)
    
    def get_metrics(self, name: str, since: Optional[float] = None) -> List[MetricPoint]:
        """获取指标数据"""
        with self._lock:
            points = list(self.metrics[name])
        
        if since is not None:
            points = [p for p in points if p.timestamp >= since]
        
        return points
    
    def get_latest_value(self, name: str) -> Optional[float]:
        """获取最新值"""
        with self._lock:
            if self.metrics[name]:
                return self.metrics[name][-1].value
        return None
    
    def get_average(self, name: str, since: Optional[float] = None) -> float:
        """获取平均值"""
        points = self.get_metrics(name, since)
        if not points:
            return 0.0
        return sum(p.value for p in points) / len(points)
    
    def get_percentile(self, name: str, percentile: float, since: Optional[float] = None) -> float:
        """获取百分位数"""
        points = self.get_metrics(name, since)
        if not points:
            return 0.0
        
        values = sorted([p.value for p in points])
        index = int(len(values) * percentile / 100)
        return values[min(index, len(values) - 1)]
    
    def clear(self):
        """清除所有指标"""
        with self._lock:
            self.metrics.clear()
    
    def export_to_dict(self) -> Dict[str, List[Dict[str, Any]]]:
        """导出为字典"""
        with self._lock:
            return {
                name: [point.to_dict() for point in points]
                for name, points in self.metrics.items()
            }


class SystemMonitor:
    """系统资源监控器"""
    
    def __init__(self, collector: MetricsCollector, interval: float = 1.0):
        self.collector = collector
        self.interval = interval
        self._running = False
        self._task: Optional[asyncio.Task] = None
        self._process = psutil.Process()
        
        # 初始化网络和磁盘IO计数器
        self._last_net_io = psutil.net_io_counters()
        self._last_disk_io = psutil.disk_io_counters()
        self._last_check_time = time.time()
    
    async def start(self):
        """开始监控"""
        if self._running:
            return
        
        self._running = True
        self._task = asyncio.create_task(self._monitor_loop())
        logger.info("系统监控已启动")
    
    async def stop(self):
        """停止监控"""
        if not self._running:
            return
        
        self._running = False
        if self._task:
            self._task.cancel()
            try:
                await self._task
            except asyncio.CancelledError:
                pass
        
        logger.info("系统监控已停止")
    
    async def _monitor_loop(self):
        """监控循环"""
        try:
            while self._running:
                await self._collect_system_metrics()
                await asyncio.sleep(self.interval)
        except asyncio.CancelledError:
            pass
        except Exception as e:
            logger.error(f"系统监控错误: {e}")
    
    async def _collect_system_metrics(self):
        """收集系统指标"""
        try:
            current_time = time.time()
            time_delta = current_time - self._last_check_time
            
            # CPU使用率
            cpu_percent = psutil.cpu_percent()
            self.collector.add_metric('system.cpu_usage', cpu_percent)
            
            # 内存使用
            memory = psutil.virtual_memory()
            process_memory = self._process.memory_info()
            
            self.collector.add_metric('system.memory_usage_percent', memory.percent)
            self.collector.add_metric('system.memory_usage_mb', memory.used / 1024 / 1024)
            self.collector.add_metric('process.memory_usage_mb', process_memory.rss / 1024 / 1024)
            
            # 网络IO
            current_net_io = psutil.net_io_counters()
            if self._last_net_io and time_delta > 0:
                sent_rate = (current_net_io.bytes_sent - self._last_net_io.bytes_sent) / time_delta / 1024 / 1024
                recv_rate = (current_net_io.bytes_recv - self._last_net_io.bytes_recv) / time_delta / 1024 / 1024
                
                self.collector.add_metric('system.network_sent_mbps', sent_rate)
                self.collector.add_metric('system.network_recv_mbps', recv_rate)
            
            self._last_net_io = current_net_io
            
            # 磁盘IO
            current_disk_io = psutil.disk_io_counters()
            if self._last_disk_io and time_delta > 0:
                read_rate = (current_disk_io.read_bytes - self._last_disk_io.read_bytes) / time_delta / 1024 / 1024
                write_rate = (current_disk_io.write_bytes - self._last_disk_io.write_bytes) / time_delta / 1024 / 1024
                
                self.collector.add_metric('system.disk_read_mbps', read_rate)
                self.collector.add_metric('system.disk_write_mbps', write_rate)
            
            self._last_disk_io = current_disk_io
            
            # 进程信息
            process_cpu = self._process.cpu_percent()
            process_threads = self._process.num_threads()
            
            self.collector.add_metric('process.cpu_usage', process_cpu)
            self.collector.add_metric('process.thread_count', process_threads)
            
            self._last_check_time = current_time
            
        except Exception as e:
            logger.error(f"收集系统指标失败: {e}")


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, 
                 enable_system_monitoring: bool = True,
                 system_monitor_interval: float = 1.0,
                 metrics_retention_hours: int = 24):
        self.collector = MetricsCollector()
        self.enable_system_monitoring = enable_system_monitoring
        self.metrics_retention_hours = metrics_retention_hours
        
        # 系统监控器
        self.system_monitor = SystemMonitor(self.collector, system_monitor_interval) if enable_system_monitoring else None
        
        # 性能指标
        self._start_time = time.time()
        self._request_times: deque = deque(maxlen=10000)
        self._concurrent_counts: deque = deque(maxlen=1000)
        
        # 回调函数
        self._alert_callbacks: List[Callable[[str, Dict[str, Any]], None]] = []
        
        # 阈值配置
        self.alert_thresholds = {
            'cpu_usage': 80.0,
            'memory_usage_percent': 85.0,
            'error_rate': 5.0,
            'avg_response_time': 10.0,
            'requests_per_second': 1000.0
        }
    
    async def start(self):
        """启动监控"""
        if self.system_monitor:
            await self.system_monitor.start()
        
        logger.info("性能监控已启动")
    
    async def stop(self):
        """停止监控"""
        if self.system_monitor:
            await self.system_monitor.stop()
        
        logger.info("性能监控已停止")
    
    def record_request(self, response_time: float, success: bool, provider_id: str = None):
        """记录请求"""
        timestamp = time.time()
        
        # 记录响应时间
        self._request_times.append((timestamp, response_time))
        self.collector.add_metric('requests.response_time', response_time, 
                                {'provider': provider_id} if provider_id else {})
        
        # 记录成功/失败
        if success:
            self.collector.add_metric('requests.successful', 1, 
                                    {'provider': provider_id} if provider_id else {})
        else:
            self.collector.add_metric('requests.failed', 1, 
                                    {'provider': provider_id} if provider_id else {})
        
        # 记录总请求数
        self.collector.add_metric('requests.total', 1, 
                                {'provider': provider_id} if provider_id else {})
    
    def record_concurrent_requests(self, count: int):
        """记录并发请求数"""
        timestamp = time.time()
        self._concurrent_counts.append((timestamp, count))
        self.collector.add_metric('requests.concurrent', count)
    
    def record_batch_metrics(self, batch_size: int, wait_time: float, processing_time: float):
        """记录批处理指标"""
        self.collector.add_metric('batch.size', batch_size)
        self.collector.add_metric('batch.wait_time', wait_time)
        self.collector.add_metric('batch.processing_time', processing_time)
        self.collector.add_metric('batch.total', 1)
    
    def record_cache_metrics(self, hit: bool, cache_size: int, memory_usage_mb: float):
        """记录缓存指标"""
        self.collector.add_metric('cache.hit' if hit else 'cache.miss', 1)
        self.collector.add_metric('cache.size', cache_size)
        self.collector.add_metric('cache.memory_mb', memory_usage_mb)
    
    def record_error(self, error_type: str, provider_id: str = None):
        """记录错误"""
        tags = {'error_type': error_type}
        if provider_id:
            tags['provider'] = provider_id
        
        self.collector.add_metric('errors.total', 1, tags)
    
    def get_current_metrics(self) -> PerformanceMetrics:
        """获取当前性能指标"""
        current_time = time.time()
        window_start = current_time - 60  # 最近1分钟
        
        metrics = PerformanceMetrics()
        
        # 请求指标
        total_requests = len(self.collector.get_metrics('requests.total', window_start))
        successful_requests = len(self.collector.get_metrics('requests.successful', window_start))
        failed_requests = len(self.collector.get_metrics('requests.failed', window_start))
        
        metrics.total_requests = total_requests
        metrics.successful_requests = successful_requests
        metrics.failed_requests = failed_requests
        
        if total_requests > 0:
            metrics.requests_per_second = total_requests / 60
            metrics.error_rate = (failed_requests / total_requests) * 100
        
        # 响应时间指标
        response_times = [p.value for p in self.collector.get_metrics('requests.response_time', window_start)]
        if response_times:
            metrics.avg_response_time = sum(response_times) / len(response_times)
            sorted_times = sorted(response_times)
            metrics.p95_response_time = sorted_times[int(len(sorted_times) * 0.95)] if sorted_times else 0
            metrics.p99_response_time = sorted_times[int(len(sorted_times) * 0.99)] if sorted_times else 0
        
        # 并发指标
        concurrent_counts = [p.value for p in self.collector.get_metrics('requests.concurrent', window_start)]
        if concurrent_counts:
            metrics.current_concurrent = int(concurrent_counts[-1])
            metrics.max_concurrent = int(max(concurrent_counts))
            metrics.avg_concurrent = sum(concurrent_counts) / len(concurrent_counts)
        
        # 系统资源指标
        metrics.cpu_usage = self.collector.get_latest_value('system.cpu_usage') or 0
        metrics.memory_usage_mb = self.collector.get_latest_value('system.memory_usage_mb') or 0
        metrics.memory_usage_percent = self.collector.get_latest_value('system.memory_usage_percent') or 0
        metrics.disk_io_read_mb = self.collector.get_latest_value('system.disk_read_mbps') or 0
        metrics.disk_io_write_mb = self.collector.get_latest_value('system.disk_write_mbps') or 0
        metrics.network_io_sent_mb = self.collector.get_latest_value('system.network_sent_mbps') or 0
        metrics.network_io_recv_mb = self.collector.get_latest_value('system.network_recv_mbps') or 0
        
        # 缓存指标
        cache_hits = len(self.collector.get_metrics('cache.hit', window_start))
        cache_misses = len(self.collector.get_metrics('cache.miss', window_start))
        total_cache_requests = cache_hits + cache_misses
        
        if total_cache_requests > 0:
            metrics.cache_hit_rate = (cache_hits / total_cache_requests) * 100
        
        metrics.cache_size = int(self.collector.get_latest_value('cache.size') or 0)
        metrics.cache_memory_mb = self.collector.get_latest_value('cache.memory_mb') or 0
        
        # 批处理指标
        batch_sizes = [p.value for p in self.collector.get_metrics('batch.size', window_start)]
        batch_wait_times = [p.value for p in self.collector.get_metrics('batch.wait_time', window_start)]
        batch_processing_times = [p.value for p in self.collector.get_metrics('batch.processing_time', window_start)]
        
        metrics.total_batches = len(self.collector.get_metrics('batch.total', window_start))
        
        if batch_sizes:
            metrics.avg_batch_size = sum(batch_sizes) / len(batch_sizes)
        
        if batch_wait_times:
            metrics.avg_batch_wait_time = sum(batch_wait_times) / len(batch_wait_times)
        
        if batch_processing_times:
            metrics.avg_batch_processing_time = sum(batch_processing_times) / len(batch_processing_times)
        
        if metrics.total_batches > 0 and metrics.avg_batch_processing_time > 0:
            metrics.batch_efficiency = metrics.avg_batch_size / metrics.avg_batch_processing_time
        
        return metrics
    
    def add_alert_callback(self, callback: Callable[[str, Dict[str, Any]], None]):
        """添加告警回调函数"""
        self._alert_callbacks.append(callback)
    
    def check_alerts(self):
        """检查告警条件"""
        metrics = self.get_current_metrics()
        
        # 检查各项指标是否超过阈值
        alerts = []
        
        if metrics.cpu_usage > self.alert_thresholds['cpu_usage']:
            alerts.append(('high_cpu_usage', {
                'current': metrics.cpu_usage,
                'threshold': self.alert_thresholds['cpu_usage']
            }))
        
        if metrics.memory_usage_percent > self.alert_thresholds['memory_usage_percent']:
            alerts.append(('high_memory_usage', {
                'current': metrics.memory_usage_percent,
                'threshold': self.alert_thresholds['memory_usage_percent']
            }))
        
        if metrics.error_rate > self.alert_thresholds['error_rate']:
            alerts.append(('high_error_rate', {
                'current': metrics.error_rate,
                'threshold': self.alert_thresholds['error_rate']
            }))
        
        if metrics.avg_response_time > self.alert_thresholds['avg_response_time']:
            alerts.append(('high_response_time', {
                'current': metrics.avg_response_time,
                'threshold': self.alert_thresholds['avg_response_time']
            }))
        
        if metrics.requests_per_second > self.alert_thresholds['requests_per_second']:
            alerts.append(('high_request_rate', {
                'current': metrics.requests_per_second,
                'threshold': self.alert_thresholds['requests_per_second']
            }))
        
        # 触发告警回调
        for alert_type, alert_data in alerts:
            for callback in self._alert_callbacks:
                try:
                    callback(alert_type, alert_data)
                except Exception as e:
                    logger.error(f"告警回调执行失败: {e}")
    
    def generate_report(self, hours: int = 1) -> Dict[str, Any]:
        """生成性能报告"""
        current_time = time.time()
        window_start = current_time - (hours * 3600)
        
        # 获取时间窗口内的指标
        report = {
            'report_time': datetime.fromtimestamp(current_time).isoformat(),
            'window_hours': hours,
            'window_start': datetime.fromtimestamp(window_start).isoformat(),
            'summary': {},
            'trends': {},
            'alerts': []
        }
        
        # 汇总指标
        total_requests = len(self.collector.get_metrics('requests.total', window_start))
        successful_requests = len(self.collector.get_metrics('requests.successful', window_start))
        failed_requests = len(self.collector.get_metrics('requests.failed', window_start))
        
        report['summary'] = {
            'requests': {
                'total': total_requests,
                'successful': successful_requests,
                'failed': failed_requests,
                'success_rate': (successful_requests / max(1, total_requests)) * 100,
                'rps': total_requests / (hours * 3600)
            },
            'response_time': {
                'avg': self.collector.get_average('requests.response_time', window_start),
                'p95': self.collector.get_percentile('requests.response_time', 95, window_start),
                'p99': self.collector.get_percentile('requests.response_time', 99, window_start)
            },
            'system': {
                'avg_cpu': self.collector.get_average('system.cpu_usage', window_start),
                'avg_memory': self.collector.get_average('system.memory_usage_percent', window_start),
                'max_memory_mb': max([p.value for p in self.collector.get_metrics('system.memory_usage_mb', window_start)] or [0])
            },
            'cache': {
                'hit_rate': self._calculate_cache_hit_rate(window_start),
                'avg_size': self.collector.get_average('cache.size', window_start)
            }
        }
        
        # 趋势分析
        report['trends'] = self._analyze_trends(window_start)
        
        return report
    
    def _calculate_cache_hit_rate(self, since: float) -> float:
        """计算缓存命中率"""
        hits = len(self.collector.get_metrics('cache.hit', since))
        misses = len(self.collector.get_metrics('cache.miss', since))
        total = hits + misses
        return (hits / max(1, total)) * 100
    
    def _analyze_trends(self, since: float) -> Dict[str, Any]:
        """分析趋势"""
        trends = {}
        
        # 分析响应时间趋势
        response_times = self.collector.get_metrics('requests.response_time', since)
        if len(response_times) >= 2:
            first_half = response_times[:len(response_times)//2]
            second_half = response_times[len(response_times)//2:]
            
            first_avg = sum(p.value for p in first_half) / len(first_half)
            second_avg = sum(p.value for p in second_half) / len(second_half)
            
            trends['response_time'] = {
                'direction': 'increasing' if second_avg > first_avg else 'decreasing',
                'change_percent': ((second_avg - first_avg) / first_avg) * 100 if first_avg > 0 else 0
            }
        
        # 分析CPU使用率趋势
        cpu_usage = self.collector.get_metrics('system.cpu_usage', since)
        if len(cpu_usage) >= 2:
            first_half = cpu_usage[:len(cpu_usage)//2]
            second_half = cpu_usage[len(cpu_usage)//2:]
            
            first_avg = sum(p.value for p in first_half) / len(first_half)
            second_avg = sum(p.value for p in second_half) / len(second_half)
            
            trends['cpu_usage'] = {
                'direction': 'increasing' if second_avg > first_avg else 'decreasing',
                'change_percent': ((second_avg - first_avg) / first_avg) * 100 if first_avg > 0 else 0
            }
        
        return trends
    
    def export_metrics(self, file_path: str):
        """导出指标数据"""
        data = {
            'export_time': datetime.now().isoformat(),
            'metrics': self.collector.export_to_dict(),
            'current_metrics': self.get_current_metrics().to_dict()
        }
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"指标数据已导出到: {file_path}")
    
    def cleanup_old_metrics(self):
        """清理过期指标"""
        cutoff_time = time.time() - (self.metrics_retention_hours * 3600)
        
        # 这里可以实现更复杂的清理逻辑
        # 当前的deque已经有maxlen限制，会自动清理旧数据
        pass


# 全局性能监控实例
_global_monitor: Optional[PerformanceMonitor] = None


def get_global_monitor() -> Optional[PerformanceMonitor]:
    """获取全局性能监控实例"""
    return _global_monitor


def set_global_monitor(monitor: PerformanceMonitor):
    """设置全局性能监控实例"""
    global _global_monitor
    _global_monitor = monitor


def create_performance_monitor(**kwargs) -> PerformanceMonitor:
    """创建性能监控器"""
    monitor = PerformanceMonitor(**kwargs)
    set_global_monitor(monitor)
    return monitor