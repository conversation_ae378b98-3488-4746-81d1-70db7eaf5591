"""
用于协调AI模型评估的评估引擎。
"""

import asyncio
import time
from typing import Callable, Dict, List, Optional

from ..models.core import (
    EvaluationTask, EvaluationRow, ResultRow, ModelConfig, Provider, 
    PromptTemplate, APIRequest, APIResponse, TaskStatus
)
from ..utils.exceptions import EvaluationError, ConfigurationError
from ..utils.helpers import generate_id
from .api_client import AIAPIClient, APIClientManager
from .prompt_generator import PromptGenerator
from .file_handler import FileHandler


class EvaluationEngine:
    """用于协调AI模型评估的引擎。"""
    
    def __init__(self, max_concurrent_requests: int = 100):
        """
        初始化评估引擎。
        
        参数:
            max_concurrent_requests: 最大并发API请求数
        """
        self.max_concurrent_requests = max_concurrent_requests
        self.prompt_generator = PromptGenerator()
        self.file_handler = FileHandler()
        self.client_manager = APIClientManager()
        self._progress_callback: Optional[Callable[[float], None]] = None
        self._current_task: Optional[EvaluationTask] = None
    
    async def run_evaluation(self, task: EvaluationTask, providers: List[Provider], 
                           models: List[ModelConfig], template: PromptTemplate,
                           evaluation_data: List[EvaluationRow]) -> List[ResultRow]:
        """
        运行完整的评估任务。
        
        参数:
            task: 评估任务配置
            providers: 模型的提供商列表
            models: 要评估的模型列表
            template: 要使用的提示模板
            evaluation_data: 评估数据行
            
        返回:
            List of result rows
            
        Raises:
            EvaluationError: If evaluation fails
        """
        self._current_task = task
        
        try:
            # Validate inputs
            self._validate_evaluation_inputs(providers, models, template, evaluation_data)
            
            # Set up API clients
            await self._setup_api_clients(providers)
            
            # Update task status
            task.status = TaskStatus.RUNNING
            
            # Run evaluation
            results = await self.process_batch(evaluation_data, models, template)
            
            # Update task status
            task.status = TaskStatus.COMPLETED
            task.completed_at = time.time()
            
            return results
            
        except Exception as e:
            task.status = TaskStatus.FAILED
            task.error_message = str(e)
            raise EvaluationError(f"Evaluation failed: {e}")
        
        finally:
            # Clean up
            await self.client_manager.close_all()
            self._current_task = None
    
    async def process_batch(self, evaluation_data: List[EvaluationRow], 
                          models: List[ModelConfig], template: PromptTemplate) -> List[ResultRow]:
        """
        Process a batch of evaluation data with multiple models.
        
        Args:
            evaluation_data: List of evaluation rows
            models: List of models to evaluate
            template: Prompt template to use
            
        Returns:
            List of result rows
        """
        if not evaluation_data:
            return []
        
        # Generate prompts for all evaluation rows
        prompts = self.prompt_generator.generate_prompts_batch(template, evaluation_data)
        
        # Create result rows
        results = []
        for i, (row, prompt) in enumerate(zip(evaluation_data, prompts)):
            result_row = ResultRow(data=row.data.copy())
            results.append(result_row)
        
        # Process each model concurrently
        total_requests = len(evaluation_data) * len(models)
        completed_requests = 0
        progress_lock = asyncio.Lock()

        async def update_progress_safe(count: int):
            """Thread-safe progress update."""
            nonlocal completed_requests
            async with progress_lock:
                completed_requests += count
                self._update_progress(completed_requests / total_requests)

        async def process_model(model: ModelConfig) -> None:
            """Process a single model concurrently."""
            # Get API client for this model's provider
            client = self.client_manager.get_client(model.provider_id)
            if client is None:
                # Add error for all rows for this model
                for result_row in results:
                    result_row.add_result(
                        model_id=model.id,
                        result="",
                        execution_time=0.0,
                        error=f"No API client available for provider {model.provider_id}"
                    )
                await update_progress_safe(len(evaluation_data))
                return

            # Create API requests for this model
            api_requests = []
            for i, prompt in enumerate(prompts):
                request = APIRequest(
                    model=model,
                    prompt=prompt,
                    request_id=f"{model.id}_{i}_{int(time.time())}"
                )
                api_requests.append(request)

            # Send batch requests
            try:
                responses = await client.batch_requests(api_requests)

                # Process responses
                for i, response in enumerate(responses):
                    result_row = results[i]
                    result_row.add_result(
                        model_id=model.id,
                        result=response.content,
                        execution_time=response.execution_time,
                        error=response.error_message if not response.success else None
                    )

                await update_progress_safe(len(evaluation_data))

            except Exception as e:
                # Add error for all rows for this model
                for result_row in results:
                    result_row.add_result(
                        model_id=model.id,
                        result="",
                        execution_time=0.0,
                        error=f"Batch request failed: {e}"
                    )
                await update_progress_safe(len(evaluation_data))

        # Process all models concurrently
        model_tasks = [process_model(model) for model in models]
        await asyncio.gather(*model_tasks, return_exceptions=True)

        return results
    
    def generate_prompt(self, template: PromptTemplate, variables: Dict[str, str]) -> str:
        """
        Generate a prompt from template and variables.
        
        Args:
            template: Prompt template
            variables: Variable values
            
        Returns:
            Generated prompt
        """
        return self.prompt_generator.template_engine.render_template(template, variables)
    
    def track_progress(self, callback: Callable[[float], None]) -> None:
        """
        Set progress tracking callback.
        
        Args:
            callback: Function to call with progress (0.0 to 1.0)
        """
        self._progress_callback = callback
    
    def get_current_task_status(self) -> Optional[Dict[str, any]]:
        """
        Get current task status information.
        
        Returns:
            Task status dictionary or None if no task running
        """
        if self._current_task is None:
            return None
        
        return {
            'task_id': self._current_task.id,
            'task_name': self._current_task.name,
            'status': self._current_task.status.value,
            'created_at': self._current_task.created_at,
            'error_message': self._current_task.error_message
        }
    
    async def validate_models_connectivity(self, providers: List[Provider], 
                                         models: List[ModelConfig]) -> Dict[str, bool]:
        """
        Validate connectivity to all model providers.
        
        Args:
            providers: List of providers
            models: List of models to validate
            
        Returns:
            Dictionary mapping model IDs to connectivity status
        """
        # Set up API clients
        await self._setup_api_clients(providers)
        
        # Test connectivity for each model
        connectivity_results = {}
        
        for model in models:
            client = self.client_manager.get_client(model.provider_id)
            if client is None:
                connectivity_results[model.id] = False
                continue
            
            try:
                is_connected = await client.validate_connection()
                connectivity_results[model.id] = is_connected
            except Exception:
                connectivity_results[model.id] = False
        
        return connectivity_results
    
    def _validate_evaluation_inputs(self, providers: List[Provider], models: List[ModelConfig],
                                  template: PromptTemplate, evaluation_data: List[EvaluationRow]) -> None:
        """Validate evaluation inputs."""
        if not providers:
            raise ConfigurationError("No providers provided")
        
        if not models:
            raise ConfigurationError("No models provided")
        
        if not evaluation_data:
            raise ConfigurationError("No evaluation data provided")
        
        # Validate that all models have corresponding providers
        provider_ids = {p.id for p in providers}
        for model in models:
            if model.provider_id not in provider_ids:
                raise ConfigurationError(f"Model {model.id} references unknown provider {model.provider_id}")
        
        # Validate template compatibility
        errors = self.prompt_generator.validate_template_compatibility(template, evaluation_data)
        if errors:
            raise ConfigurationError(f"Template validation failed: {'; '.join(errors)}")
    
    async def _setup_api_clients(self, providers: List[Provider]) -> None:
        """Set up API clients for providers."""
        for provider in providers:
            if self.client_manager.get_client(provider.id) is None:
                self.client_manager.add_client(
                    provider, 
                    max_concurrent=self.max_concurrent_requests
                )
    
    def _update_progress(self, progress: float) -> None:
        """Update progress if callback is set."""
        if self._progress_callback:
            self._progress_callback(min(1.0, max(0.0, progress)))


class EvaluationTaskManager:
    """Manages evaluation tasks and their execution."""
    
    def __init__(self):
        """Initialize task manager."""
        self.tasks: Dict[str, EvaluationTask] = {}
        self.engine = EvaluationEngine()
    
    def create_task(self, name: str, prompt_template_id: str, selected_models: List[str],
                   input_file_path: str, output_directory: str) -> EvaluationTask:
        """
        Create a new evaluation task.
        
        Args:
            name: Task name
            prompt_template_id: ID of prompt template to use
            selected_models: List of model IDs to evaluate
            input_file_path: Path to input data file
            output_directory: Directory for output files
            
        Returns:
            Created evaluation task
        """
        task = EvaluationTask(
            id=generate_id(),
            name=name,
            prompt_template_id=prompt_template_id,
            selected_models=selected_models,
            input_file_path=input_file_path,
            output_directory=output_directory,
            status=TaskStatus.PENDING
        )
        
        self.tasks[task.id] = task
        return task
    
    def get_task(self, task_id: str) -> Optional[EvaluationTask]:
        """Get task by ID."""
        return self.tasks.get(task_id)
    
    def list_tasks(self) -> List[EvaluationTask]:
        """List all tasks."""
        return list(self.tasks.values())
    
    def get_tasks_by_status(self, status: TaskStatus) -> List[EvaluationTask]:
        """Get tasks by status."""
        return [task for task in self.tasks.values() if task.status == status]
    
    async def execute_task(self, task_id: str, providers: List[Provider],
                          models: List[ModelConfig], template: PromptTemplate,
                          evaluation_data: List[EvaluationRow]) -> List[ResultRow]:
        """
        Execute an evaluation task.
        
        Args:
            task_id: Task ID to execute
            providers: List of providers
            models: List of models to evaluate
            template: Prompt template
            evaluation_data: Evaluation data
            
        Returns:
            List of result rows
            
        Raises:
            EvaluationError: If task not found or execution fails
        """
        task = self.get_task(task_id)
        if task is None:
            raise EvaluationError(f"Task not found: {task_id}")
        
        if task.status != TaskStatus.PENDING:
            raise EvaluationError(f"Task {task_id} is not in pending status")
        
        return await self.engine.run_evaluation(task, providers, models, template, evaluation_data)
    
    def cancel_task(self, task_id: str) -> bool:
        """
        Cancel a task.
        
        Args:
            task_id: Task ID to cancel
            
        Returns:
            True if task was cancelled, False if not found or not cancellable
        """
        task = self.get_task(task_id)
        if task is None:
            return False
        
        if task.status in [TaskStatus.PENDING, TaskStatus.RUNNING]:
            task.status = TaskStatus.CANCELLED
            return True
        
        return False
    
    def delete_task(self, task_id: str) -> bool:
        """
        Delete a task.
        
        Args:
            task_id: Task ID to delete
            
        Returns:
            True if task was deleted, False if not found
        """
        if task_id in self.tasks:
            del self.tasks[task_id]
            return True
        return False
    
    def get_task_statistics(self) -> Dict[str, int]:
        """
        Get task statistics.
        
        Returns:
            Dictionary with task counts by status
        """
        stats = {status.value: 0 for status in TaskStatus}
        
        for task in self.tasks.values():
            stats[task.status.value] += 1
        
        stats['total'] = len(self.tasks)
        return stats


class UnifiedEvaluationEngine:
    """
    统一的评估引擎，整合原版和优化版功能
    支持CLI和auto_evaluate.py使用相同的核心引擎
    """

    def __init__(self, use_optimized: bool = False, config_manager=None):
        """
        初始化统一评估引擎

        Args:
            use_optimized: 是否使用优化版本
            config_manager: 配置管理器实例
        """
        self.use_optimized = use_optimized
        self.config_manager = config_manager

        # 根据优化选项选择引擎
        if use_optimized and config_manager:
            try:
                from .optimized_evaluation import OptimizedEvaluationEngine
                self.engine = OptimizedEvaluationEngine(config_manager=config_manager)
                print("🚀 使用高性能优化引擎")
            except ImportError:
                print("⚠️ 优化引擎不可用，使用标准引擎")
                self.engine = EvaluationEngine()
                self.use_optimized = False
        else:
            self.engine = EvaluationEngine()
            if use_optimized:
                print("⚠️ 需要配置管理器才能使用优化引擎，使用标准引擎")
                self.use_optimized = False

    async def run_evaluation(self, task: EvaluationTask, providers: List[Provider],
                           models: List[ModelConfig], template: PromptTemplate,
                           evaluation_data: List[EvaluationRow]) -> List[ResultRow]:
        """
        运行评估任务（统一接口）

        Args:
            task: 评估任务
            providers: 提供商列表
            models: 模型列表
            template: 提示模板
            evaluation_data: 评估数据

        Returns:
            评估结果列表
        """
        if self.use_optimized and hasattr(self.engine, 'run_evaluation_optimized'):
            return await self.engine.run_evaluation_optimized(
                task, providers, models, template, evaluation_data
            )
        else:
            return await self.engine.run_evaluation(
                task, providers, models, template, evaluation_data
            )

    def set_progress_callback(self, callback: Callable[[float], None]):
        """设置进度回调函数"""
        if hasattr(self.engine, 'set_progress_callback'):
            self.engine.set_progress_callback(callback)

    def get_performance_stats(self) -> Dict:
        """获取性能统计信息"""
        if hasattr(self.engine, '_performance_stats'):
            return getattr(self.engine, '_performance_stats', {})
        return {}

    @property
    def max_concurrent_requests(self) -> int:
        """获取最大并发请求数"""
        return getattr(self.engine, 'max_concurrent_requests', 100)

    async def close(self):
        """清理资源"""
        if hasattr(self.engine, 'client_manager'):
            await self.engine.client_manager.close_all()