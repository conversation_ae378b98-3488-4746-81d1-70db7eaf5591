"""
Configuration file loader with environment variable support.
"""

import os
import re
from pathlib import Path
from typing import Any, Dict, Optional, Union

import yaml

from ..models.config import ConfigData, ConfigValidationResult
from ..models.core import Provider, ModelConfig, PromptTemplate
from ..utils.exceptions import ConfigurationError


class ConfigLoader:
    """Loads and processes YAML configuration files."""
    
    def __init__(self, config_path: Optional[Union[str, Path]] = None):
        """
        Initialize config loader.
        
        Args:
            config_path: Path to configuration file. If None, looks for config.yaml in current directory.
        """
        if config_path is None:
            config_path = Path.cwd() / "config.yaml"
        
        self.config_path = Path(config_path)
        self._raw_config: Optional[Dict[str, Any]] = None
        self._processed_config: Optional[ConfigData] = None
    
    def load(self, validate: bool = True) -> ConfigData:
        """
        Load configuration from file.
        
        Args:
            validate: Whether to validate the configuration
            
        Returns:
            Loaded configuration data
            
        Raises:
            ConfigurationError: If configuration is invalid or file cannot be read
        """
        try:
            self._load_raw_config()
            self._process_environment_variables()
            self._processed_config = self._parse_config()
            
            if validate:
                validation_result = self.validate()
                if not validation_result.is_valid:
                    error_msg = "Configuration validation failed:\n" + "\n".join(validation_result.errors)
                    raise ConfigurationError(error_msg)
            
            return self._processed_config
            
        except yaml.YAMLError as e:
            raise ConfigurationError(f"Invalid YAML syntax in {self.config_path}: {e}")
        except FileNotFoundError:
            raise ConfigurationError(f"Configuration file not found: {self.config_path}")
        except Exception as e:
            raise ConfigurationError(f"Failed to load configuration: {e}")
    
    def save(self, config_data: ConfigData, backup: bool = True) -> None:
        """
        Save configuration to file.
        
        Args:
            config_data: Configuration data to save
            backup: Whether to create a backup of existing file
        """
        if backup and self.config_path.exists():
            backup_path = self.config_path.with_suffix(f"{self.config_path.suffix}.backup")
            self.config_path.rename(backup_path)
        
        # Convert config data to dictionary format
        config_dict = self._config_to_dict(config_data)
        
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                yaml.dump(config_dict, f, default_flow_style=False, allow_unicode=True, indent=2)
        except Exception as e:
            raise ConfigurationError(f"Failed to save configuration: {e}")
    
    def validate(self) -> ConfigValidationResult:
        """
        Validate the loaded configuration.
        
        Returns:
            Validation result with errors and warnings
        """
        result = ConfigValidationResult(is_valid=True)
        
        if self._processed_config is None:
            result.add_error("No configuration loaded")
            return result
        
        # Validate providers
        if not self._processed_config.providers:
            result.add_warning("No providers configured")
        else:
            provider_ids = set()
            for provider in self._processed_config.providers:
                if provider.id in provider_ids:
                    result.add_error(f"Duplicate provider ID: {provider.id}")
                provider_ids.add(provider.id)
                
                # Validate API key is not empty (after env var substitution)
                if not provider.api_key or provider.api_key.startswith("${"):
                    result.add_error(f"Provider {provider.id} has invalid or missing API key")
        
        # Validate models
        if not self._processed_config.models:
            result.add_warning("No models configured")
        else:
            model_ids = set()
            for model in self._processed_config.models:
                if model.id in model_ids:
                    result.add_error(f"Duplicate model ID: {model.id}")
                model_ids.add(model.id)
                
                # Check if provider exists
                if not self._processed_config.get_provider(model.provider_id):
                    result.add_error(f"Model {model.id} references unknown provider: {model.provider_id}")
        
        # Validate prompt templates
        if not self._processed_config.prompt_templates:
            result.add_warning("No prompt templates configured")
        else:
            template_ids = set()
            for template in self._processed_config.prompt_templates:
                if template.id in template_ids:
                    result.add_error(f"Duplicate template ID: {template.id}")
                template_ids.add(template.id)
        
        return result
    
    def _load_raw_config(self) -> None:
        """Load raw configuration from YAML file."""
        with open(self.config_path, 'r', encoding='utf-8') as f:
            self._raw_config = yaml.safe_load(f) or {}
    
    def _process_environment_variables(self) -> None:
        """Process environment variable substitutions in configuration."""
        if self._raw_config is None:
            return
        
        self._raw_config = self._substitute_env_vars(self._raw_config)
    
    def _substitute_env_vars(self, obj: Any) -> Any:
        """
        Recursively substitute environment variables in configuration.
        
        Supports ${VAR_NAME} and ${VAR_NAME:default_value} syntax.
        """
        if isinstance(obj, dict):
            return {key: self._substitute_env_vars(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._substitute_env_vars(item) for item in obj]
        elif isinstance(obj, str):
            return self._substitute_env_vars_in_string(obj)
        else:
            return obj
    
    def _substitute_env_vars_in_string(self, text: str) -> str:
        """Substitute environment variables in a string."""
        def replace_var(match):
            var_expr = match.group(1)
            if ':' in var_expr:
                var_name, default_value = var_expr.split(':', 1)
                return os.getenv(var_name, default_value)
            else:
                var_value = os.getenv(var_expr)
                if var_value is None:
                    # Keep original if environment variable is not set
                    return match.group(0)
                return var_value
        
        # Pattern matches ${VAR_NAME} or ${VAR_NAME:default}
        pattern = r'\$\{([^}]+)\}'
        return re.sub(pattern, replace_var, text)
    
    def _parse_config(self) -> ConfigData:
        """Parse raw configuration into structured data."""
        if self._raw_config is None:
            raise ConfigurationError("No configuration data to parse")
        
        providers = []
        models = []
        prompt_templates = []
        
        # Parse providers
        for provider_data in self._raw_config.get('providers', []):
            try:
                provider = Provider(
                    id=provider_data['id'],
                    name=provider_data['name'],
                    base_url=provider_data['base_url'],
                    api_key=provider_data['api_key']
                )
                providers.append(provider)
            except KeyError as e:
                raise ConfigurationError(f"Missing required field in provider: {e}")
            except ValueError as e:
                raise ConfigurationError(f"Invalid provider configuration: {e}")
        
        # Parse models
        for model_data in self._raw_config.get('models', []):
            try:
                model = ModelConfig(
                    id=model_data['id'],
                    provider_id=model_data['provider_id'],
                    model_name=model_data['model_name'],
                    display_name=model_data['display_name'],
                    temperature=model_data.get('temperature', 0.7),
                    thinking_enabled=model_data.get('thinking_enabled', False),
                    max_tokens=model_data.get('max_tokens'),
                    other_params=model_data.get('other_params', {})
                )
                models.append(model)
            except KeyError as e:
                raise ConfigurationError(f"Missing required field in model: {e}")
            except ValueError as e:
                raise ConfigurationError(f"Invalid model configuration: {e}")
        
        # Parse prompt templates
        for template_data in self._raw_config.get('prompt_templates', []):
            try:
                template = PromptTemplate(
                    id=template_data['id'],
                    name=template_data['name'],
                    template=template_data['template'],
                    variables=template_data['variables'],
                    description=template_data.get('description', '')
                )
                prompt_templates.append(template)
            except KeyError as e:
                raise ConfigurationError(f"Missing required field in prompt template: {e}")
            except ValueError as e:
                raise ConfigurationError(f"Invalid prompt template configuration: {e}")
        
        # Parse performance configuration
        performance_config = self._raw_config.get('performance', {})
        
        config_data = ConfigData(
            providers=providers,
            models=models,
            prompt_templates=prompt_templates,
            performance=performance_config
        )
        
        return config_data
    
    def _config_to_dict(self, config_data: ConfigData) -> Dict[str, Any]:
        """Convert ConfigData back to dictionary format for saving."""
        return {
            'providers': [
                {
                    'id': p.id,
                    'name': p.name,
                    'base_url': p.base_url,
                    'api_key': p.api_key
                }
                for p in config_data.providers
            ],
            'models': [
                {
                    'id': m.id,
                    'provider_id': m.provider_id,
                    'model_name': m.model_name,
                    'display_name': m.display_name,
                    'temperature': m.temperature,
                    'thinking_enabled': m.thinking_enabled,
                    'max_tokens': m.max_tokens,
                    'other_params': m.other_params
                }
                for m in config_data.models
            ],
            'prompt_templates': [
                {
                    'id': t.id,
                    'name': t.name,
                    'template': t.template,
                    'variables': t.variables,
                    'description': t.description
                }
                for t in config_data.prompt_templates
            ]
        }