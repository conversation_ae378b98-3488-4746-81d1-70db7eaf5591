"""
AI模型评估系统的核心数据模型。
"""

from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, validator


class TaskStatus(str, Enum):
    """评估任务状态。"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class Provider:
    """AI服务提供商配置。"""
    id: str
    name: str
    base_url: str
    api_key: str
    created_at: datetime = field(default_factory=datetime.now)
    is_active: bool = True

    def __post_init__(self):
        """验证提供商配置。"""
        if not self.base_url.endswith('/'):
            self.base_url += '/'
        
        if not self.base_url.startswith(('http://', 'https://')):
            raise ValueError(f"无效的base_url: {self.base_url}")


@dataclass
class ModelConfig:
    """特定提供商的模型配置。"""
    id: str
    provider_id: str
    model_name: str
    display_name: str
    temperature: float = 0.7
    thinking_enabled: bool = False
    max_tokens: Optional[int] = None
    other_params: Dict[str, Any] = field(default_factory=dict)

    def __post_init__(self):
        """验证模型配置。"""
        if not 0.0 <= self.temperature <= 2.0:
            raise ValueError(f"温度值必须在0.0到2.0之间，得到 {self.temperature}")
        
        if self.max_tokens is not None and self.max_tokens <= 0:
            raise ValueError(f"max_tokens必须为正数，得到 {self.max_tokens}")


@dataclass
class PromptTemplate:
    """带有变量占位符的提示模板。"""
    id: str
    name: str
    template: str
    variables: List[str]
    description: str
    created_at: datetime = field(default_factory=datetime.now)

    def __post_init__(self):
        """验证模板变量。"""
        import re
        
        # Extract variables from template
        template_vars = set(re.findall(r'\{(\w+)\}', self.template))
        declared_vars = set(self.variables)
        
        if template_vars != declared_vars:
            missing = template_vars - declared_vars
            extra = declared_vars - template_vars
            error_msg = []
            if missing:
                error_msg.append(f"声明中缺少变量: {missing}")
            if extra:
                error_msg.append(f"声明中多余的变量: {extra}")
            raise ValueError("; ".join(error_msg))


@dataclass
class EvaluationRow:
    """单行评估数据。"""
    data: Dict[str, str] = field(default_factory=dict)

    def __init__(self, data: Optional[Dict[str, str]] = None,
                 original_prompt: Optional[str] = None,
                 variable_a: Optional[str] = None,
                 variable_b: Optional[str] = None,
                 expected_result: Optional[str] = None):
        """
        Initialize EvaluationRow with flexible parameters.

        Args:
            data: Dictionary containing all data (new format)
            original_prompt: Original prompt (backward compatibility)
            variable_a: Variable A (backward compatibility)
            variable_b: Variable B (backward compatibility)
            expected_result: Expected result (backward compatibility)
        """
        if data is not None:
            self.data = data.copy()
        else:
            # Backward compatibility: construct data from individual parameters
            self.data = {}
            if original_prompt is not None:
                self.data['original_prompt'] = original_prompt
            if variable_a is not None:
                self.data['variable_a'] = variable_a
            if variable_b is not None:
                self.data['variable_b'] = variable_b
            if expected_result is not None:
                self.data['expected_result'] = expected_result

    # Backward compatibility properties
    @property
    def original_prompt(self) -> str:
        return self.data.get('original_prompt', '')

    @property
    def variable_a(self) -> str:
        return self.data.get('variable_a', '')

    @property
    def variable_b(self) -> str:
        return self.data.get('variable_b', '')

    @property
    def expected_result(self) -> Optional[str]:
        return self.data.get('expected_result')

    def to_dict(self) -> Dict[str, str]:
        """转换为字典用于模板替换。"""
        # Return all data except expected_result for template replacement
        result = {k: v for k, v in self.data.items() if k != 'expected_result' and v is not None}
        return result

    def get_value(self, key: str, default: str = '') -> str:
        """获取指定键的值。"""
        return self.data.get(key, default)


@dataclass
class EvaluationTask:
    """评估任务配置。"""
    id: str
    name: str
    prompt_template_id: str
    selected_models: List[str]
    input_file_path: str
    output_directory: str
    created_at: datetime = field(default_factory=datetime.now)
    status: TaskStatus = TaskStatus.PENDING
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None


@dataclass
class ResultRow:
    """单行评估结果。"""
    data: Dict[str, str] = field(default_factory=dict)
    model_results: Dict[str, str] = field(default_factory=dict)  # model_id -> result
    execution_time: Dict[str, float] = field(default_factory=dict)  # model_id -> time_taken
    error_info: Dict[str, Optional[str]] = field(default_factory=dict)  # model_id -> error_message

    def __init__(self, data: Optional[Dict[str, str]] = None,
                 original_prompt: Optional[str] = None,
                 variable_a: Optional[str] = None,
                 variable_b: Optional[str] = None,
                 expected_result: Optional[str] = None,
                 model_results: Optional[Dict[str, str]] = None,
                 execution_time: Optional[Dict[str, float]] = None,
                 error_info: Optional[Dict[str, Optional[str]]] = None):
        """
        Initialize ResultRow with flexible parameters.

        Args:
            data: Dictionary containing all data (new format)
            original_prompt: Original prompt (backward compatibility)
            variable_a: Variable A (backward compatibility)
            variable_b: Variable B (backward compatibility)
            expected_result: Expected result (backward compatibility)
            model_results: Model results dictionary
            execution_time: Execution time dictionary
            error_info: Error info dictionary
        """
        if data is not None:
            self.data = data.copy()
        else:
            # Backward compatibility: construct data from individual parameters
            self.data = {}
            if original_prompt is not None:
                self.data['original_prompt'] = original_prompt
            if variable_a is not None:
                self.data['variable_a'] = variable_a
            if variable_b is not None:
                self.data['variable_b'] = variable_b
            if expected_result is not None:
                self.data['expected_result'] = expected_result

        self.model_results = model_results or {}
        self.execution_time = execution_time or {}
        self.error_info = error_info or {}

    # Backward compatibility properties
    @property
    def original_prompt(self) -> str:
        return self.data.get('original_prompt', '')

    @property
    def variable_a(self) -> str:
        return self.data.get('variable_a', '')

    @property
    def variable_b(self) -> str:
        return self.data.get('variable_b', '')

    @property
    def expected_result(self) -> Optional[str]:
        return self.data.get('expected_result')

    def add_result(self, model_id: str, result: str, execution_time: float, error: Optional[str] = None):
        """为特定模型添加结果。"""
        self.model_results[model_id] = result
        self.execution_time[model_id] = execution_time
        self.error_info[model_id] = error


@dataclass
class APIRequest:
    """API请求数据。"""
    model: ModelConfig
    prompt: str
    request_id: str
    
    def to_openai_format(self) -> Dict[str, Any]:
        """转换为OpenAI兼容的请求格式。"""
        request_data = {
            "model": self.model.model_name,
            "messages": [
                {"role": "user", "content": self.prompt}
            ],
            "temperature": self.model.temperature,
        }
        
        if self.model.max_tokens:
            request_data["max_tokens"] = self.model.max_tokens
            
        # 如果启用，添加思考模式（适用于兼容的模型）
        if self.model.thinking_enabled:
            request_data["thinking"] = {"type": "enabled"}
            
        # 添加其他自定义参数
        # 这些可以包括：top_p、frequency_penalty、presence_penalty等
        for key, value in self.model.other_params.items():
            if value is not None:  # 只添加非None值
                request_data[key] = value
        
        return request_data
    
    def validate_parameters(self) -> List[str]:
        """
        验证请求参数。
        
        返回:
            验证错误列表（如果有效则为空）
        """
        errors = []
        
        if not self.prompt.strip():
            errors.append("提示不能为空")
        
        if not self.model.model_name.strip():
            errors.append("模型名称不能为空")
        
        # 验证温度值
        if not 0.0 <= self.model.temperature <= 2.0:
            errors.append(f"温度值必须在0.0到2.0之间，得到 {self.model.temperature}")
        
        # 验证max_tokens
        if self.model.max_tokens is not None and self.model.max_tokens <= 0:
            errors.append(f"max_tokens必须为正数，得到 {self.model.max_tokens}")
        
        # 验证other_params中的常见OpenAI参数
        if 'top_p' in self.model.other_params:
            top_p = self.model.other_params['top_p']
            if not isinstance(top_p, (int, float)) or not 0.0 <= top_p <= 1.0:
                errors.append(f"top_p必须在0.0到1.0之间，得到 {top_p}")
        
        if 'frequency_penalty' in self.model.other_params:
            freq_penalty = self.model.other_params['frequency_penalty']
            if not isinstance(freq_penalty, (int, float)) or not -2.0 <= freq_penalty <= 2.0:
                errors.append(f"frequency_penalty必须在-2.0到2.0之间，得到 {freq_penalty}")
        
        if 'presence_penalty' in self.model.other_params:
            pres_penalty = self.model.other_params['presence_penalty']
            if not isinstance(pres_penalty, (int, float)) or not -2.0 <= pres_penalty <= 2.0:
                errors.append(f"presence_penalty必须在-2.0到2.0之间，得到 {pres_penalty}")
        
        return errors


@dataclass
class APIResponse:
    """API响应数据。"""
    request_id: str
    content: str
    success: bool
    error_message: Optional[str] = None
    execution_time: float = 0.0
    
    @classmethod
    def from_openai_response(cls, request_id: str, response_data: Dict[str, Any], execution_time: float) -> 'APIResponse':
        """从OpenAI兼容响应创建APIResponse。"""
        try:
            content = response_data["choices"][0]["message"]["content"]
            return cls(
                request_id=request_id,
                content=content,
                success=True,
                execution_time=execution_time
            )
        except (KeyError, IndexError) as e:
            return cls(
                request_id=request_id,
                content="",
                success=False,
                error_message=f"无效的响应格式: {e}",
                execution_time=execution_time
            )


class ValidationResult(BaseModel):
    """文件验证结果。"""
    is_valid: bool
    errors: List[str] = Field(default_factory=list)
    warnings: List[str] = Field(default_factory=list)
    row_count: int = 0
    column_names: List[str] = Field(default_factory=list)


class AnalysisResult(BaseModel):
    """分析结果数据。"""
    total_rows: int
    model_count: int
    success_rates: Dict[str, float] = Field(default_factory=dict)
    average_execution_times: Dict[str, float] = Field(default_factory=dict)
    error_counts: Dict[str, int] = Field(default_factory=dict)
    created_at: datetime = Field(default_factory=datetime.now)


class ComparisonReport(BaseModel):
    """模型比较报告。"""
    models: List[str]
    metrics: Dict[str, Dict[str, float]] = Field(default_factory=dict)
    summary: str = ""
    created_at: datetime = Field(default_factory=datetime.now)


class StatisticsReport(BaseModel):
    """统计分析报告。"""
    total_evaluations: int
    successful_evaluations: int
    failed_evaluations: int
    average_response_length: Dict[str, float] = Field(default_factory=dict)
    response_time_stats: Dict[str, Dict[str, float]] = Field(default_factory=dict)
    created_at: datetime = Field(default_factory=datetime.now)