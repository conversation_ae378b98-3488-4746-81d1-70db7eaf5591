"""
Configuration-specific data models.
"""

from typing import Dict, List, Optional
from pydantic import BaseModel, Field, validator

from .core import Provider, ModelConfig, PromptTemplate


class ConfigData(BaseModel):
    """Complete configuration data structure."""
    providers: List[Provider] = Field(default_factory=list)
    models: List[ModelConfig] = Field(default_factory=list)
    prompt_templates: List[PromptTemplate] = Field(default_factory=list)
    performance: Optional[Dict] = Field(default_factory=dict, description="Performance configuration")
    
    @validator('models')
    def validate_models_have_valid_providers(cls, models, values):
        """Ensure all models reference valid providers."""
        if 'providers' not in values:
            return models
            
        provider_ids = {p.id for p in values['providers']}
        for model in models:
            if model.provider_id not in provider_ids:
                raise ValueError(f"Model {model.id} references unknown provider {model.provider_id}")
        return models
    
    def get_provider(self, provider_id: str) -> Optional[Provider]:
        """Get provider by ID."""
        for provider in self.providers:
            if provider.id == provider_id:
                return provider
        return None
    
    def get_model(self, model_id: str) -> Optional[ModelConfig]:
        """Get model by ID."""
        for model in self.models:
            if model.id == model_id:
                return model
        return None
    
    def get_models_by_provider(self, provider_id: str) -> List[ModelConfig]:
        """Get all models for a specific provider."""
        return [model for model in self.models if model.provider_id == provider_id]
    
    def get_prompt_template(self, template_id: str) -> Optional[PromptTemplate]:
        """Get prompt template by ID."""
        for template in self.prompt_templates:
            if template.id == template_id:
                return template
        return None
    
    def add_provider(self, provider: Provider) -> None:
        """Add a new provider."""
        # Check for duplicate IDs
        if any(p.id == provider.id for p in self.providers):
            raise ValueError(f"Provider with ID {provider.id} already exists")
        self.providers.append(provider)
    
    def add_model(self, model: ModelConfig) -> None:
        """Add a new model."""
        # Check for duplicate IDs
        if any(m.id == model.id for m in self.models):
            raise ValueError(f"Model with ID {model.id} already exists")
        
        # Check if provider exists
        if not self.get_provider(model.provider_id):
            raise ValueError(f"Provider {model.provider_id} does not exist")
        
        self.models.append(model)
    
    def add_prompt_template(self, template: PromptTemplate) -> None:
        """Add a new prompt template."""
        # Check for duplicate IDs
        if any(t.id == template.id for t in self.prompt_templates):
            raise ValueError(f"Template with ID {template.id} already exists")
        self.prompt_templates.append(template)
    
    def remove_provider(self, provider_id: str) -> bool:
        """Remove a provider and all its models."""
        # Remove associated models first
        self.models = [m for m in self.models if m.provider_id != provider_id]
        
        # Remove provider
        original_count = len(self.providers)
        self.providers = [p for p in self.providers if p.id != provider_id]
        return len(self.providers) < original_count
    
    def remove_model(self, model_id: str) -> bool:
        """Remove a model."""
        original_count = len(self.models)
        self.models = [m for m in self.models if m.id != model_id]
        return len(self.models) < original_count
    
    def remove_prompt_template(self, template_id: str) -> bool:
        """Remove a prompt template."""
        original_count = len(self.prompt_templates)
        self.prompt_templates = [t for t in self.prompt_templates if t.id != template_id]
        return len(self.prompt_templates) < original_count


class ConfigValidationResult(BaseModel):
    """Result of configuration validation."""
    is_valid: bool
    errors: List[str] = Field(default_factory=list)
    warnings: List[str] = Field(default_factory=list)
    
    def add_error(self, error: str) -> None:
        """Add an error message."""
        self.errors.append(error)
        self.is_valid = False
    
    def add_warning(self, warning: str) -> None:
        """Add a warning message."""
        self.warnings.append(warning)
    
    @property
    def has_errors(self) -> bool:
        """Check if there are any errors."""
        return len(self.errors) > 0
    
    @property
    def has_warnings(self) -> bool:
        """Check if there are any warnings."""
        return len(self.warnings) > 0


class ProviderConnectionStatus(BaseModel):
    """Provider connection status information."""
    provider_id: str
    is_connected: bool
    response_time: Optional[float] = None
    error_message: Optional[str] = None
    last_checked: Optional[str] = None  # ISO format datetime string
    
    @property
    def status_text(self) -> str:
        """Get human-readable status text."""
        if self.is_connected:
            if self.response_time:
                return f"Connected ({self.response_time:.2f}s)"
            return "Connected"
        else:
            return f"Failed: {self.error_message}" if self.error_message else "Failed"