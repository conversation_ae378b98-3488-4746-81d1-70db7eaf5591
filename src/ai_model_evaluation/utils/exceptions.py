"""
Custom exceptions for the AI Model Evaluation System.
"""


class EvaluationError(Exception):
    """Base exception for evaluation system errors."""
    pass


class ConfigurationError(EvaluationError):
    """Configuration-related errors."""
    pass


class FileProcessingError(EvaluationError):
    """File processing errors."""
    pass


class APIError(EvaluationError):
    """API call errors."""
    
    def __init__(self, message: str, model_id: str = "", retry_count: int = 0):
        super().__init__(message)
        self.model_id = model_id
        self.retry_count = retry_count


class ValidationError(EvaluationError):
    """Data validation errors."""
    pass


class TemplateError(EvaluationError):
    """Template processing errors."""
    pass


class HistoryError(EvaluationError):
    """History management errors."""
    pass


class AnalysisError(EvaluationError):
    """Analysis processing errors."""
    pass