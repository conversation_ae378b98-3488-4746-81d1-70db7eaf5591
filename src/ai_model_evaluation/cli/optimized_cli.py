"""优化的命令行接口
整合所有性能优化组件，提供更高效的用户体验
"""

import asyncio
import time
import sys
import os
from typing import List, Dict, Optional, Any
from pathlib import Path
import click
from loguru import logger

from ..config.manager import ConfigManager
from ..services.optimized_evaluation_engine import (
    OptimizedEvaluationEngine, 
    OptimizedEvaluationConfig,
    create_optimized_evaluation_engine,
    BatchStrategy,
    Priority
)
from ..models.core import ModelConfig


# 配置日志
logger.remove()
logger.add(
    sys.stderr,
    format="<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
    level="INFO"
)


@click.group()
def optimized_cli():
    """优化版AI模型评估工具 - 高性能模式"""
    pass


@optimized_cli.command("evaluate")
@click.option("--config", "-c", default="config.yaml", help="配置文件路径")
@click.option("--input", "-i", required=True, help="输入数据文件路径 (CSV/Excel)")
@click.option("--output", "-o", required=True, help="输出结果文件路径")
@click.option("--models", "-m", multiple=True, help="要评估的模型ID (可指定多个)")
@click.option("--template", "-t", help="提示词模板名称")
@click.option("--batch-size", type=int, default=20, help="批处理大小")
@click.option("--concurrent", type=int, default=100, help="最大并发请求数")
@click.option("--memory-limit", type=int, default=1000, help="内存限制 (MB)")
@click.option("--strategy", type=click.Choice(['fixed', 'adaptive', 'memory', 'hybrid']), default='hybrid', help="批处理策略")
@click.option("--priority", type=click.Choice(['low', 'normal', 'high', 'urgent']), default='normal', help="请求优先级")
@click.option("--verbose", "-v", is_flag=True, help="显示详细日志")
@click.option("--cache/--no-cache", default=True, help="启用/禁用请求缓存")
@click.option("--adaptive/--no-adaptive", default=True, help="启用/禁用自适应并发")
@click.option("--memory-opt/--no-memory-opt", default=True, help="启用/禁用内存优化")
@click.option("--streaming/--no-streaming", default=True, help="启用/禁用流式处理")
def evaluate(config, input, output, models, template, batch_size, concurrent, memory_limit, 
           strategy, priority, verbose, cache, adaptive, memory_opt, streaming):
    """执行优化的模型评估"""
    if verbose:
        logger.remove()
        logger.add(sys.stderr, level="DEBUG")
    
    # 映射策略
    strategy_map = {
        'fixed': BatchStrategy.FIXED_SIZE,
        'adaptive': BatchStrategy.ADAPTIVE_SIZE,
        'memory': BatchStrategy.MEMORY_AWARE,
        'hybrid': BatchStrategy.HYBRID
    }
    
    # 映射优先级
    priority_map = {
        'low': Priority.LOW,
        'normal': Priority.NORMAL,
        'high': Priority.HIGH,
        'urgent': Priority.URGENT
    }
    
    # 创建优化配置
    optimization_config = OptimizedEvaluationConfig(
        global_max_concurrent=concurrent,
        enable_adaptive_concurrency=adaptive,
        batch_strategy=strategy_map[strategy],
        min_batch_size=max(5, batch_size // 2),
        max_batch_size=batch_size,
        memory_threshold_mb=memory_limit,
        enable_memory_optimization=memory_opt,
        enable_request_cache=cache,
        enable_streaming=streaming
    )
    
    # 运行评估
    asyncio.run(run_evaluation(
        config_path=config,
        input_file=input,
        output_file=output,
        model_ids=models,
        template_name=template,
        optimization_config=optimization_config,
        priority=priority_map[priority]
    ))


@optimized_cli.command("benchmark")
@click.option("--config", "-c", default="config.yaml", help="配置文件路径")
@click.option("--input", "-i", required=True, help="输入数据文件路径 (CSV/Excel)")
@click.option("--output", "-o", required=True, help="输出结果文件路径")
@click.option("--models", "-m", multiple=True, help="要评估的模型ID (可指定多个)")
@click.option("--template", "-t", help="提示词模板名称")
@click.option("--duration", type=int, default=60, help="基准测试持续时间 (秒)")
@click.option("--concurrent-levels", type=str, default="50,100,200,300", help="并发级别列表 (逗号分隔)")
@click.option("--batch-levels", type=str, default="10,20,50,100", help="批处理大小列表 (逗号分隔)")
@click.option("--verbose", "-v", is_flag=True, help="显示详细日志")
def benchmark(config, input, output, models, template, duration, concurrent_levels, batch_levels, verbose):
    """执行性能基准测试"""
    if verbose:
        logger.remove()
        logger.add(sys.stderr, level="DEBUG")
    
    # 解析并发级别和批处理大小
    concurrent_levels = [int(level) for level in concurrent_levels.split(',')]
    batch_levels = [int(level) for level in batch_levels.split(',')]
    
    # 运行基准测试
    asyncio.run(run_benchmark(
        config_path=config,
        input_file=input,
        output_file=output,
        model_ids=models,
        template_name=template,
        duration=duration,
        concurrent_levels=concurrent_levels,
        batch_levels=batch_levels
    ))


@optimized_cli.command("monitor")
@click.option("--config", "-c", default="config.yaml", help="配置文件路径")
@click.option("--interval", type=int, default=5, help="监控间隔 (秒)")
@click.option("--duration", type=int, default=0, help="监控持续时间 (秒), 0表示持续运行")
def monitor(config, interval, duration):
    """监控系统性能"""
    asyncio.run(run_monitor(config_path=config, interval=interval, duration=duration))


@optimized_cli.command("cache")
@click.option("--config", "-c", default="config.yaml", help="配置文件路径")
@click.option("--clear", is_flag=True, help="清除缓存")
@click.option("--stats", is_flag=True, help="显示缓存统计")
@click.option("--export", help="导出缓存到文件")
@click.option("--import-file", help="从文件导入缓存")
def cache(config, clear, stats, export, import_file):
    """管理请求缓存"""
    asyncio.run(manage_cache(
        config_path=config,
        clear=clear,
        stats=stats,
        export_path=export,
        import_path=import_file
    ))


async def run_evaluation(config_path: str,
                        input_file: str,
                        output_file: str,
                        model_ids: List[str],
                        template_name: str,
                        optimization_config: OptimizedEvaluationConfig,
                        priority: Priority):
    """运行优化的模型评估"""
    start_time = time.time()
    
    try:
        # 创建配置管理器
        config_manager = ConfigManager(config_path)
        await config_manager.load_config()
        
        # 获取模型配置
        models = []
        if model_ids:
            for model_id in model_ids:
                model_config = config_manager.get_model_config(model_id)
                if model_config:
                    models.append(model_config)
                else:
                    logger.warning(f"未找到模型配置: {model_id}")
        else:
            # 使用所有模型
            models = config_manager.get_all_models()
        
        if not models:
            logger.error("没有可用的模型配置")
            return
        
        logger.info(f"将评估 {len(models)} 个模型: {', '.join(m.id for m in models)}")
        
        # 获取提示词模板
        if not template_name:
            template_name = config_manager.get_default_template_name()
        
        template = config_manager.get_prompt_template(template_name)
        if not template:
            logger.error(f"未找到提示词模板: {template_name}")
            return
        
        logger.info(f"使用提示词模板: {template_name}")
        
        # 创建优化的评估引擎
        async with OptimizedEvaluationEngine(config_manager, optimization_config) as engine:
            # 执行评估
            result = await engine.evaluate_models_optimized(
                input_file=input_file,
                output_file=output_file,
                models=models,
                prompt_template=template,
                priority=priority
            )
            
            # 显示结果
            total_time = time.time() - start_time
            click.echo(f"\n评估完成! 总耗时: {total_time:.2f}秒")
            click.echo(f"总请求数: {result.total_requests}")
            click.echo(f"成功请求: {result.successful_requests} ({result.successful_requests/result.total_requests*100:.1f}%)")
            click.echo(f"失败请求: {result.failed_requests}")
            click.echo(f"平均响应时间: {result.average_response_time:.2f}秒")
            click.echo(f"每秒请求数 (RPS): {result.requests_per_second:.2f}")
            click.echo(f"结果已保存到: {output_file}")
            
            # 显示性能摘要
            summary = engine.get_performance_summary()
            click.echo("\n性能摘要:")
            click.echo(f"全局最大并发: {summary['optimization_config']['global_max_concurrent']}")
            click.echo(f"批处理策略: {summary['optimization_config']['batch_strategy']}")
            click.echo(f"批处理大小: {summary['optimization_config']['min_batch_size']}-{summary['optimization_config']['max_batch_size']}")
            click.echo(f"内存使用: {summary['evaluation_metrics']['memory_usage_mb']:.1f} MB")
            click.echo(f"缓存命中率: {summary['evaluation_metrics']['cache_hit_rate']:.1f}%")
            click.echo(f"批处理效率: {summary['evaluation_metrics']['batch_efficiency']:.2f} RPS")
    
    except Exception as e:
        logger.error(f"评估失败: {e}")
        raise


async def run_benchmark(config_path: str,
                       input_file: str,
                       output_file: str,
                       model_ids: List[str],
                       template_name: str,
                       duration: int,
                       concurrent_levels: List[int],
                       batch_levels: List[int]):
    """运行性能基准测试"""
    try:
        # 创建配置管理器
        config_manager = ConfigManager(config_path)
        await config_manager.load_config()
        
        # 获取模型配置
        models = []
        if model_ids:
            for model_id in model_ids:
                model_config = config_manager.get_model_config(model_id)
                if model_config:
                    models.append(model_config)
                else:
                    logger.warning(f"未找到模型配置: {model_id}")
        else:
            # 使用第一个模型
            all_models = config_manager.get_all_models()
            if all_models:
                models = [all_models[0]]
        
        if not models:
            logger.error("没有可用的模型配置")
            return
        
        # 获取提示词模板
        if not template_name:
            template_name = config_manager.get_default_template_name()
        
        template = config_manager.get_prompt_template(template_name)
        if not template:
            logger.error(f"未找到提示词模板: {template_name}")
            return
        
        # 准备基准测试结果
        benchmark_results = []
        
        # 对每个并发级别和批处理大小组合进行测试
        for concurrent in concurrent_levels:
            for batch_size in batch_levels:
                click.echo(f"\n测试配置: 并发={concurrent}, 批处理大小={batch_size}")
                
                # 创建优化配置
                optimization_config = OptimizedEvaluationConfig(
                    global_max_concurrent=concurrent,
                    enable_adaptive_concurrency=False,  # 基准测试禁用自适应
                    batch_strategy=BatchStrategy.FIXED_SIZE,  # 固定大小
                    min_batch_size=batch_size,
                    max_batch_size=batch_size,
                    memory_threshold_mb=2000,  # 较高的内存阈值
                    enable_memory_optimization=True,
                    enable_request_cache=False,  # 基准测试禁用缓存
                    enable_streaming=True
                )
                
                # 创建评估引擎
                async with OptimizedEvaluationEngine(config_manager, optimization_config) as engine:
                    # 执行基准测试
                    start_time = time.time()
                    end_time = start_time + duration
                    
                    total_requests = 0
                    successful_requests = 0
                    failed_requests = 0
                    total_response_time = 0.0
                    
                    # 循环执行评估直到达到持续时间
                    while time.time() < end_time:
                        # 执行一次评估
                        result = await engine.evaluate_models_optimized(
                            input_file=input_file,
                            output_file=f"{output_file}.tmp",  # 临时文件
                            models=models,
                            prompt_template=template,
                            priority=Priority.NORMAL
                        )
                        
                        # 累计结果
                        total_requests += result.total_requests
                        successful_requests += result.successful_requests
                        failed_requests += result.failed_requests
                        total_response_time += result.total_time
                        
                        # 显示进度
                        elapsed = time.time() - start_time
                        remaining = max(0, duration - elapsed)
                        click.echo(f"进度: {elapsed:.1f}/{duration}秒, RPS: {total_requests/max(1,elapsed):.2f}, 剩余: {remaining:.1f}秒")
                    
                    # 计算最终结果
                    actual_duration = time.time() - start_time
                    rps = total_requests / actual_duration
                    avg_response_time = total_response_time / max(1, total_requests)
                    success_rate = successful_requests / max(1, total_requests) * 100
                    
                    # 记录结果
                    benchmark_result = {
                        'concurrent': concurrent,
                        'batch_size': batch_size,
                        'duration': actual_duration,
                        'total_requests': total_requests,
                        'successful_requests': successful_requests,
                        'failed_requests': failed_requests,
                        'requests_per_second': rps,
                        'avg_response_time': avg_response_time,
                        'success_rate': success_rate,
                        'timestamp': time.time()
                    }
                    benchmark_results.append(benchmark_result)
                    
                    # 显示结果
                    click.echo(f"\n基准测试结果 (并发={concurrent}, 批处理={batch_size}):")
                    click.echo(f"总请求数: {total_requests}")
                    click.echo(f"成功率: {success_rate:.1f}%")
                    click.echo(f"每秒请求数 (RPS): {rps:.2f}")
                    click.echo(f"平均响应时间: {avg_response_time:.2f}秒")
        
        # 保存基准测试结果
        import json
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(benchmark_results, f, indent=2, ensure_ascii=False)
        
        # 显示最佳配置
        best_result = max(benchmark_results, key=lambda x: x['requests_per_second'])
        click.echo(f"\n最佳性能配置:")
        click.echo(f"并发数: {best_result['concurrent']}")
        click.echo(f"批处理大小: {best_result['batch_size']}")
        click.echo(f"每秒请求数 (RPS): {best_result['requests_per_second']:.2f}")
        click.echo(f"平均响应时间: {best_result['avg_response_time']:.2f}秒")
        click.echo(f"成功率: {best_result['success_rate']:.1f}%")
        click.echo(f"\n基准测试结果已保存到: {output_file}")
    
    except Exception as e:
        logger.error(f"基准测试失败: {e}")
        raise
    finally:
        # 清理临时文件
        tmp_file = f"{output_file}.tmp"
        if os.path.exists(tmp_file):
            os.remove(tmp_file)


async def run_monitor(config_path: str, interval: int, duration: int):
    """运行系统监控"""
    try:
        # 创建配置管理器
        config_manager = ConfigManager(config_path)
        await config_manager.load_config()
        
        # 创建评估引擎
        optimization_config = OptimizedEvaluationConfig(
            enable_detailed_metrics=True,
            metrics_interval=interval
        )
        
        async with OptimizedEvaluationEngine(config_manager, optimization_config) as engine:
            start_time = time.time()
            end_time = start_time + duration if duration > 0 else float('inf')
            
            click.echo("开始监控系统性能...")
            click.echo("按 Ctrl+C 停止")
            
            try:
                while time.time() < end_time:
                    # 获取性能摘要
                    summary = engine.get_performance_summary()
                    
                    # 清屏
                    click.clear()
                    
                    # 显示监控信息
                    click.echo(f"系统性能监控 (更新间隔: {interval}秒)")
                    click.echo(f"运行时间: {time.time() - start_time:.1f}秒")
                    click.echo("\n全局指标:")
                    click.echo(f"总请求数: {summary['evaluation_metrics']['total_requests']}")
                    click.echo(f"成功率: {summary['evaluation_metrics']['success_rate']:.1f}%")
                    click.echo(f"每秒请求数 (RPS): {summary['evaluation_metrics']['requests_per_second']:.2f}")
                    click.echo(f"平均响应时间: {summary['evaluation_metrics']['avg_response_time']:.2f}秒")
                    click.echo(f"内存使用: {summary['evaluation_metrics']['memory_usage_mb']:.1f} MB")
                    click.echo(f"缓存命中率: {summary['evaluation_metrics']['cache_hit_rate']:.1f}%")
                    
                    # 显示提供商指标
                    if 'api_clients' in summary and 'providers' in summary['api_clients']:
                        click.echo("\n提供商指标:")
                        for provider_id, provider_stats in summary['api_clients']['providers'].items():
                            click.echo(f"  {provider_id}:")
                            click.echo(f"    请求数: {provider_stats['total_requests']}")
                            click.echo(f"    成功率: {provider_stats['success_rate']:.1f}%")
                            click.echo(f"    平均响应时间: {provider_stats['avg_response_time']:.2f}秒")
                            click.echo(f"    并发峰值: {provider_stats['concurrent_peak']}")
                    
                    # 显示批处理指标
                    if 'batch_processor' in summary:
                        click.echo("\n批处理指标:")
                        click.echo(f"  策略: {summary['batch_processor']['strategy']}")
                        click.echo(f"  总批次数: {summary['batch_processor']['total_batches']}")
                        click.echo(f"  平均批次大小: {summary['batch_processor']['avg_batch_size']:.1f}")
                        click.echo(f"  平均等待时间: {summary['batch_processor']['avg_wait_time']:.2f}秒")
                        click.echo(f"  平均处理时间: {summary['batch_processor']['avg_processing_time']:.2f}秒")
                        click.echo(f"  吞吐量: {summary['batch_processor']['throughput_rps']:.2f} RPS")
                    
                    # 等待下一次更新
                    await asyncio.sleep(interval)
            
            except KeyboardInterrupt:
                click.echo("\n监控已停止")
    
    except Exception as e:
        logger.error(f"监控失败: {e}")
        raise


async def manage_cache(config_path: str, clear: bool, stats: bool, export_path: str, import_path: str):
    """管理请求缓存"""
    try:
        # 创建配置管理器
        config_manager = ConfigManager(config_path)
        await config_manager.load_config()
        
        # 创建评估引擎
        optimization_config = OptimizedEvaluationConfig(
            enable_request_cache=True
        )
        
        async with OptimizedEvaluationEngine(config_manager, optimization_config) as engine:
            # 清除缓存
            if clear:
                engine._request_cache.clear()
                engine._cache_timestamps.clear()
                click.echo("缓存已清除")
            
            # 显示缓存统计
            if stats:
                cache_size = len(engine._request_cache)
                memory_usage = sum(len(r.content) for r in engine._request_cache.values()) / 1024 / 1024
                
                click.echo(f"缓存条目数: {cache_size}")
                click.echo(f"缓存内存使用: {memory_usage:.2f} MB")
                
                # 按提供商分组统计
                provider_stats = {}
                for cache_key, response in engine._request_cache.items():
                    model_id = cache_key.split('_')[0] if '_' in cache_key else 'unknown'
                    provider_id = engine._get_provider_for_model(model_id) or 'unknown'
                    
                    if provider_id not in provider_stats:
                        provider_stats[provider_id] = 0
                    provider_stats[provider_id] += 1
                
                click.echo("\n按提供商分布:")
                for provider_id, count in provider_stats.items():
                    click.echo(f"  {provider_id}: {count} 条目 ({count/max(1,cache_size)*100:.1f}%)")
            
            # 导出缓存
            if export_path:
                cache_data = []
                for cache_key, response in engine._request_cache.items():
                    timestamp = engine._cache_timestamps.get(cache_key, 0)
                    cache_data.append({
                        'key': str(cache_key),
                        'response': {
                            'request_id': response.request_id,
                            'content': response.content,
                            'success': response.success,
                            'error_message': response.error_message,
                            'execution_time': response.execution_time
                        },
                        'timestamp': timestamp
                    })
                
                import json
                with open(export_path, 'w', encoding='utf-8') as f:
                    json.dump(cache_data, f, indent=2, ensure_ascii=False)
                
                click.echo(f"缓存已导出到: {export_path} ({len(cache_data)} 条目)")
            
            # 导入缓存
            if import_path:
                import json
                try:
                    with open(import_path, 'r', encoding='utf-8') as f:
                        cache_data = json.load(f)
                    
                    count = 0
                    for item in cache_data:
                        key = eval(item['key'])  # 转换回原始类型
                        response_data = item['response']
                        timestamp = item['timestamp']
                        
                        from ..models.core import APIResponse
                        response = APIResponse(
                            request_id=response_data['request_id'],
                            content=response_data['content'],
                            success=response_data['success'],
                            error_message=response_data['error_message'],
                            execution_time=response_data['execution_time']
                        )
                        
                        engine._request_cache[key] = response
                        engine._cache_timestamps[key] = timestamp
                        count += 1
                    
                    click.echo(f"已从 {import_path} 导入 {count} 条缓存条目")
                
                except Exception as e:
                    logger.error(f"导入缓存失败: {e}")
    
    except Exception as e:
        logger.error(f"缓存管理失败: {e}")
        raise


if __name__ == "__main__":
    optimized_cli()