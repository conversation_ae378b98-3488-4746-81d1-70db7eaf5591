"""
AI模型评估系统的主要CLI入口点。
"""

import asyncio
import sys
from pathlib import Path
from typing import List, Optional

import click
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.panel import Panel

from ..config.manager import ConfigManager
from ..services.evaluation import EvaluationEngine, EvaluationTaskManager
from ..services.file_handler import FileHandler
from ..services.report_generator import ReportGenerator
from ..services.history_manager import HistoryManager
from ..models.core import Provider, ModelConfig, PromptTemplate, TaskStatus
from ..utils.exceptions import ConfigurationError, EvaluationError, FileProcessingError, HistoryError

console = Console()


def validate_file_path(file_path: str) -> bool:
    """Validate if file path exists and is readable."""
    try:
        path = Path(file_path)
        return path.exists() and path.is_file() and path.stat().st_size > 0
    except Exception:
        return False


@click.group()
@click.option('--config', '-c', default='config.yaml', help='配置文件路径')
@click.pass_context
def cli(ctx, config):
    """AI模型评估系统 - 评估和比较来自不同提供商的AI模型。"""
    ctx.ensure_object(dict)
    ctx.obj['config_path'] = config
    
    # 显示欢迎消息
    if ctx.invoked_subcommand is None:
        console.print(Panel.fit(
            "[bold blue]AI模型评估系统[/bold blue]\n"
            "评估和比较来自不同提供商的AI模型。\n\n"
            "使用 --help 查看可用命令。",
            title="欢迎"
        ))


@cli.group()
@click.pass_context
def config(ctx):
    """管理配置（提供商、模型、模板）。"""
    pass


@config.command('show')
@click.pass_context
def config_show(ctx):
    """显示当前配置。"""
    try:
        config_manager = ConfigManager(ctx.obj['config_path'])
        config_data = config_manager.load_config()
        
        # 显示提供商
        if config_data.providers:
            console.print("\n[bold green]提供商:[/bold green]")
            provider_table = Table()
            provider_table.add_column("ID", style="cyan")
            provider_table.add_column("Name", style="green")
            provider_table.add_column("Base URL", style="blue")
            provider_table.add_column("状态", style="yellow")
            
            for provider in config_data.providers:
                status = "✓ 活跃" if provider.is_active else "✗ 非活跃"
                provider_table.add_row(
                    provider.id,
                    provider.name,
                    provider.base_url,
                    status
                )
            console.print(provider_table)
        else:
            console.print("[yellow]未配置提供商[/yellow]")
        
        # 显示模型
        if config_data.models:
            console.print("\n[bold green]模型:[/bold green]")
            model_table = Table()
            model_table.add_column("ID", style="cyan")
            model_table.add_column("Provider", style="magenta")
            model_table.add_column("Model Name", style="green")
            model_table.add_column("Display Name", style="blue")
            model_table.add_column("Temperature", style="yellow")
            
            for model in config_data.models:
                model_table.add_row(
                    model.id,
                    model.provider_id,
                    model.model_name,
                    model.display_name,
                    str(model.temperature)
                )
            console.print(model_table)
        else:
            console.print("[yellow]No models configured[/yellow]")
        
        # Show templates
        if config_data.prompt_templates:
            console.print("\n[bold green]Prompt Templates:[/bold green]")
            template_table = Table()
            template_table.add_column("ID", style="cyan")
            template_table.add_column("Name", style="green")
            template_table.add_column("Variables", style="blue")
            template_table.add_column("Description", style="yellow")
            
            for template in config_data.prompt_templates:
                variables_str = ", ".join(template.variables)
                template_table.add_row(
                    template.id,
                    template.name,
                    variables_str,
                    template.description[:50] + "..." if len(template.description) > 50 else template.description
                )
            console.print(template_table)
        else:
            console.print("[yellow]No prompt templates configured[/yellow]")
            
    except Exception as e:
        console.print(f"[red]Error loading configuration: {e}[/red]")
        sys.exit(1)


@config.command('validate')
@click.pass_context
def config_validate(ctx):
    """Validate configuration file."""
    try:
        config_manager = ConfigManager(ctx.obj['config_path'])
        
        # First try to load the configuration
        try:
            config_data = config_manager.load_config(validate=False)
            console.print("[green]✓ Configuration loaded successfully[/green]")
        except Exception as e:
            console.print(f"[red]✗ Configuration validation failed:[/red]")
            console.print(f"  [red]• {e}[/red]")
            sys.exit(1)
        
        # Then validate it
        validation_result = config_manager.validate_config()
        
        if validation_result.is_valid:
            console.print("[green]✓ Configuration is valid[/green]")
        else:
            console.print("[red]✗ Configuration validation failed:[/red]")
            for error in validation_result.errors:
                console.print(f"  [red]• {error}[/red]")
        
        if validation_result.warnings:
            console.print("\n[yellow]Warnings:[/yellow]")
            for warning in validation_result.warnings:
                console.print(f"  [yellow]• {warning}[/yellow]")
                
    except Exception as e:
        console.print(f"[red]Error validating configuration: {e}[/red]")
        sys.exit(1)


@config.command('add-provider')
@click.option('--name', required=True, help='Provider name')
@click.option('--base-url', required=True, help='Provider base URL')
@click.option('--api-key', required=True, help='Provider API key')
@click.option('--provider-id', help='Custom provider ID (optional)')
@click.pass_context
def config_add_provider(ctx, name, base_url, api_key, provider_id):
    """Add a new provider."""
    try:
        config_manager = ConfigManager(ctx.obj['config_path'])
        config_manager.load_config()
        
        provider = config_manager.add_provider(name, base_url, api_key, provider_id)
        config_manager.save_config()
        
        console.print(f"[green]✓ Added provider '{provider.name}' with ID '{provider.id}'[/green]")
        
    except Exception as e:
        console.print(f"[red]Error adding provider: {e}[/red]")
        sys.exit(1)


@config.command('add-model')
@click.option('--provider-id', required=True, help='Provider ID')
@click.option('--model-name', required=True, help='Model name (e.g., gpt-4)')
@click.option('--display-name', required=True, help='Display name for the model')
@click.option('--model-id', help='Custom model ID (optional)')
@click.option('--temperature', type=float, default=0.7, help='Model temperature (default: 0.7)')
@click.option('--thinking/--no-thinking', default=False, help='Enable thinking mode')
@click.option('--max-tokens', type=int, help='Maximum tokens')
@click.pass_context
def config_add_model(ctx, provider_id, model_name, display_name, model_id, temperature, thinking, max_tokens):
    """Add a new model."""
    try:
        config_manager = ConfigManager(ctx.obj['config_path'])
        config_manager.load_config()
        
        if model_id is None:
            model_id = f"{provider_id}_{model_name.replace('-', '_')}"
        
        model = ModelConfig(
            id=model_id,
            provider_id=provider_id,
            model_name=model_name,
            display_name=display_name,
            temperature=temperature,
            thinking_enabled=thinking,
            max_tokens=max_tokens
        )
        
        config_manager.add_model(provider_id, model)
        config_manager.save_config()
        
        console.print(f"[green]✓ Added model '{display_name}' with ID '{model_id}'[/green]")
        
    except Exception as e:
        console.print(f"[red]Error adding model: {e}[/red]")
        sys.exit(1)


@config.command('list-providers')
@click.pass_context
def config_list_providers(ctx):
    """List all providers."""
    try:
        config_manager = ConfigManager(ctx.obj['config_path'])
        config_data = config_manager.load_config()
        
        if not config_data.providers:
            console.print("[yellow]No providers configured[/yellow]")
            return
        
        table = Table(title="Configured Providers")
        table.add_column("ID", style="cyan")
        table.add_column("Name", style="green")
        table.add_column("Base URL", style="blue")
        table.add_column("Status", style="yellow")
        
        for provider in config_data.providers:
            status = "✓ Active" if provider.is_active else "✗ Inactive"
            table.add_row(provider.id, provider.name, provider.base_url, status)
        
        console.print(table)
        
    except Exception as e:
        console.print(f"[red]Error listing providers: {e}[/red]")
        sys.exit(1)


@config.command('list-models')
@click.option('--provider-id', help='Filter by provider ID')
@click.pass_context
def config_list_models(ctx, provider_id):
    """List all models."""
    try:
        config_manager = ConfigManager(ctx.obj['config_path'])
        config_data = config_manager.load_config()
        
        models = config_manager.list_models(provider_id)
        
        if not models:
            if provider_id:
                console.print(f"[yellow]No models configured for provider '{provider_id}'[/yellow]")
            else:
                console.print("[yellow]No models configured[/yellow]")
            return
        
        title = f"Models for Provider '{provider_id}'" if provider_id else "All Configured Models"
        table = Table(title=title)
        table.add_column("ID", style="cyan")
        table.add_column("Provider", style="magenta")
        table.add_column("Model Name", style="green")
        table.add_column("Display Name", style="blue")
        table.add_column("Temperature", style="yellow")
        table.add_column("Thinking", style="red")
        
        for model in models:
            thinking_status = "✓" if model.thinking_enabled else "✗"
            table.add_row(
                model.id,
                model.provider_id,
                model.model_name,
                model.display_name,
                str(model.temperature),
                thinking_status
            )
        
        console.print(table)
        
    except Exception as e:
        console.print(f"[red]Error listing models: {e}[/red]")
        sys.exit(1)


@config.command('test-connection')
@click.option('--provider-id', help='Test specific provider (optional)')
@click.pass_context
def config_test_connection(ctx, provider_id):
    """Test connection to providers."""
    async def test_connections():
        try:
            config_manager = ConfigManager(ctx.obj['config_path'])
            config_data = config_manager.load_config()
            
            providers_to_test = []
            if provider_id:
                provider = config_data.get_provider(provider_id)
                if provider is None:
                    console.print(f"[red]Provider '{provider_id}' not found[/red]")
                    return
                providers_to_test = [provider]
            else:
                providers_to_test = config_data.providers
            
            if not providers_to_test:
                console.print("[yellow]No providers to test[/yellow]")
                return
            
            from ..services.api_client import APIClientManager
            
            client_manager = APIClientManager()
            
            # Set up clients
            for provider in providers_to_test:
                client_manager.add_client(provider)
            
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console
            ) as progress:
                task = progress.add_task("Testing connections...", total=len(providers_to_test))
                
                # Test connections
                connection_info = await client_manager.get_all_connection_info()
                
                progress.update(task, completed=len(providers_to_test))
            
            # Display results
            table = Table(title="Connection Test Results")
            table.add_column("Provider ID", style="cyan")
            table.add_column("Provider Name", style="green")
            table.add_column("Status", style="yellow")
            table.add_column("Response Time", style="blue")
            table.add_column("Error", style="red")
            
            for provider in providers_to_test:
                info = connection_info.get(provider.id, {})
                status = "✓ Connected" if info.get('is_connected', False) else "✗ Failed"
                response_time = f"{info.get('response_time', 0):.2f}s" if info.get('response_time') else "N/A"
                error = info.get('error_message', '') or ''
                
                table.add_row(
                    provider.id,
                    provider.name,
                    status,
                    response_time,
                    error[:50] + "..." if len(error) > 50 else error
                )
            
            console.print(table)
            
            # Clean up
            await client_manager.close_all()
            
        except Exception as e:
            console.print(f"[red]Error testing connections: {e}[/red]")
            sys.exit(1)
    
    # Run async function
    asyncio.run(test_connections())


@cli.command('evaluate')
@click.option('--input', '-i', 'input_file', required=True, help='Input data file (CSV/Excel)')
@click.option('--template', '-t', required=True, help='Prompt template ID')
@click.option('--models', '-m', required=True, help='Comma-separated list of model IDs')
@click.option('--output', '-o', required=True, help='Output directory')
@click.option('--concurrent', '-c', type=int, default=50, help='Max concurrent requests (default: 50)')
@click.option('--name', help='Evaluation task name (optional)')
@click.pass_context
def evaluate(ctx, input_file, template, models, output, concurrent, name):
    """Run evaluation on specified models with input data."""
    async def run_evaluation():
        try:
            # Load configuration
            config_manager = ConfigManager(ctx.obj['config_path'])
            config_data = config_manager.load_config()
            
            # Validate input file
            if not validate_file_path(input_file):
                console.print(f"[red]Input file not found or not readable: {input_file}[/red]")
                sys.exit(1)
            
            # Parse model IDs
            model_ids = [m.strip() for m in models.split(',')]
            
            # Get template
            prompt_template = config_data.get_prompt_template(template)
            if prompt_template is None:
                console.print(f"[red]Template '{template}' not found[/red]")
                sys.exit(1)
            
            # Get models and providers
            evaluation_models = []
            providers_dict = {}
            
            for model_id in model_ids:
                model = config_data.get_model(model_id)
                if model is None:
                    console.print(f"[red]Model '{model_id}' not found[/red]")
                    sys.exit(1)
                
                evaluation_models.append(model)
                
                # Get provider for this model
                provider = config_data.get_provider(model.provider_id)
                if provider is None:
                    console.print(f"[red]Provider '{model.provider_id}' not found for model '{model_id}'[/red]")
                    sys.exit(1)
                
                providers_dict[provider.id] = provider
            
            providers = list(providers_dict.values())
            
            # Load evaluation data with template-specific column requirements
            from ..services.file_handler import FileHandler
            file_handler = FileHandler(required_columns=prompt_template.variables)

            console.print(f"[blue]Loading evaluation data from {input_file}...[/blue]")
            evaluation_data = file_handler.read_evaluation_data(input_file)
            console.print(f"[green]✓ Loaded {len(evaluation_data)} evaluation rows[/green]")
            
            # Validate template compatibility
            from ..services.prompt_generator import PromptGenerator
            prompt_generator = PromptGenerator()
            
            validation_errors = prompt_generator.validate_template_compatibility(prompt_template, evaluation_data)
            if validation_errors:
                console.print("[red]Template validation failed:[/red]")
                for error in validation_errors:
                    console.print(f"  [red]• {error}[/red]")
                sys.exit(1)
            
            # Create evaluation task
            from ..services.evaluation import EvaluationTaskManager
            from ..utils.helpers import generate_id
            
            task_manager = EvaluationTaskManager()
            task_name = name or f"Evaluation_{generate_id()[:8]}"
            
            task = task_manager.create_task(
                name=task_name,
                prompt_template_id=template,
                selected_models=model_ids,
                input_file_path=input_file,
                output_directory=output
            )
            
            console.print(f"[blue]Starting evaluation task: {task_name}[/blue]")
            console.print(f"[blue]Task ID: {task.id}[/blue]")
            console.print(f"[blue]Models: {', '.join(model_ids)}[/blue]")
            console.print(f"[blue]Template: {template}[/blue]")
            console.print(f"[blue]Data rows: {len(evaluation_data)}[/blue]")
            console.print(f"[blue]Total requests: {len(evaluation_data) * len(model_ids)}[/blue]")
            
            # Set up progress tracking
            total_requests = len(evaluation_data) * len(model_ids)
            completed_requests = 0
            
            def progress_callback(progress):
                nonlocal completed_requests
                completed_requests = int(progress * total_requests)
            
            # Run evaluation with progress bar
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console
            ) as progress:
                progress_task = progress.add_task(
                    f"Evaluating {len(model_ids)} models on {len(evaluation_data)} rows...",
                    total=total_requests
                )
                
                # Configure evaluation engine
                task_manager.engine.max_concurrent_requests = concurrent
                task_manager.engine.track_progress(lambda p: progress.update(progress_task, completed=int(p * total_requests)))
                
                # Execute evaluation
                results = await task_manager.execute_task(
                    task.id, providers, evaluation_models, prompt_template, evaluation_data
                )
                
                progress.update(progress_task, completed=total_requests)
            
            console.print(f"[green]✓ Evaluation completed successfully![/green]")
            
            # Save results
            from pathlib import Path
            output_dir = Path(output)
            output_dir.mkdir(parents=True, exist_ok=True)
            
            output_file = output_dir / f"{task_name}_results.csv"
            file_handler.write_results(results, output_file)
            
            console.print(f"[green]✓ Results saved to: {output_file}[/green]")
            
            # Generate summary
            from ..services.result_collector import ResultCollector
            collector = ResultCollector()
            
            # Add results to collector for summary
            for result in results:
                for model_id in model_ids:
                    if model_id in result.model_results:
                        from ..models.core import APIResponse
                        api_response = APIResponse(
                            request_id=f"{model_id}_summary",
                            content=result.model_results[model_id],
                            success=result.error_info.get(model_id) is None,
                            error_message=result.error_info.get(model_id),
                            execution_time=result.execution_time.get(model_id, 0.0)
                        )
                        
                        from ..models.core import EvaluationRow
                        eval_row = EvaluationRow(
                            original_prompt=result.original_prompt,
                            variable_a=result.variable_a,
                            variable_b=result.variable_b,
                            expected_result=result.expected_result
                        )
                        
                        collector.add_result(eval_row, model_id, api_response)
            
            # Display summary
            summary = collector.get_results_summary()
            model_stats = collector.get_model_statistics()
            
            console.print("\n[bold green]Evaluation Summary:[/bold green]")
            
            summary_table = Table()
            summary_table.add_column("Metric", style="cyan")
            summary_table.add_column("Value", style="green")
            
            summary_table.add_row("Total Rows", str(summary['total_rows']))
            summary_table.add_row("Total Models", str(summary['total_models']))
            summary_table.add_row("Total Requests", str(summary['total_requests']))
            summary_table.add_row("Successful Requests", str(summary['successful_requests']))
            summary_table.add_row("Failed Requests", str(summary['failed_requests']))
            summary_table.add_row("Success Rate", f"{summary['success_rate']:.1%}")
            summary_table.add_row("Average Response Time", f"{summary['average_execution_time']:.2f}s")
            
            console.print(summary_table)
            
            # Display model statistics
            if model_stats:
                console.print("\n[bold green]Model Performance:[/bold green]")
                
                model_table = Table()
                model_table.add_column("Model ID", style="cyan")
                model_table.add_column("Success Rate", style="green")
                model_table.add_column("Avg Response Time", style="blue")
                model_table.add_column("Failed Requests", style="red")
                
                for model_id, stats in model_stats.items():
                    model_table.add_row(
                        model_id,
                        f"{stats['success_rate']:.1%}",
                        f"{stats['average_execution_time']:.2f}s",
                        str(stats['failed_requests'])
                    )
                
                console.print(model_table)
            
            # Show failed requests if any
            failed_requests = collector.get_failed_requests()
            if failed_requests:
                console.print(f"\n[yellow]Warning: {len(failed_requests)} requests failed[/yellow]")
                console.print("[yellow]Use 'ai-eval analyze' command to see detailed failure analysis[/yellow]")
            
        except Exception as e:
            console.print(f"[red]Evaluation failed: {e}[/red]")
            sys.exit(1)
    
    # Run async evaluation
    asyncio.run(run_evaluation())


@cli.command('preview')
@click.option('--input', '-i', 'input_file', required=True, help='Input data file (CSV/Excel)')
@click.option('--template', '-t', required=True, help='Prompt template ID')
@click.option('--rows', '-r', type=int, default=3, help='Number of rows to preview (default: 3)')
@click.pass_context
def preview(ctx, input_file, template, rows):
    """Preview how prompts will be generated from template and data."""
    try:
        # Load configuration
        config_manager = ConfigManager(ctx.obj['config_path'])
        config_data = config_manager.load_config()
        
        # Validate input file
        if not validate_file_path(input_file):
            console.print(f"[red]Input file not found or not readable: {input_file}[/red]")
            sys.exit(1)
        
        # Get template
        prompt_template = config_data.get_prompt_template(template)
        if prompt_template is None:
            console.print(f"[red]Template '{template}' not found[/red]")
            sys.exit(1)
        
        # Load evaluation data with template-specific column requirements
        from ..services.file_handler import FileHandler
        file_handler = FileHandler(required_columns=prompt_template.variables)

        console.print(f"[blue]Loading data from {input_file}...[/blue]")
        evaluation_data = file_handler.read_evaluation_data(input_file)
        console.print(f"[green]✓ Loaded {len(evaluation_data)} rows[/green]")
        
        # Generate previews
        from ..services.prompt_generator import PromptGenerator
        prompt_generator = PromptGenerator()
        
        previews = prompt_generator.preview_prompts(prompt_template, evaluation_data, max_previews=rows)
        
        console.print(f"\n[bold green]Prompt Preview (Template: {template}):[/bold green]")
        
        for i, preview in enumerate(previews):
            console.print(f"\n[bold cyan]Row {preview['row_index']}:[/bold cyan]")
            
            # Show input data
            console.print("[yellow]Input Data:[/yellow]")
            # Show all template variables dynamically
            for var_name in prompt_template.variables:
                if var_name in preview:
                    console.print(f"  {var_name}: {preview[var_name]}")
            if preview.get('expected_result'):
                console.print(f"  Expected Result: {preview['expected_result']}")
            
            # Show generated prompt
            console.print("[yellow]Generated Prompt:[/yellow]")
            if 'error' in preview:
                console.print(f"  [red]Error: {preview['error']}[/red]")
            else:
                # Display prompt in a panel for better readability
                console.print(Panel(
                    preview['generated_prompt'],
                    title=f"Generated Prompt for Row {preview['row_index']}",
                    border_style="blue"
                ))
        
        # Show template statistics
        stats = prompt_generator.get_template_usage_stats(prompt_template, evaluation_data)
        
        console.print(f"\n[bold green]Template Statistics:[/bold green]")
        stats_table = Table()
        stats_table.add_column("Metric", style="cyan")
        stats_table.add_column("Value", style="green")
        
        stats_table.add_row("Total Rows", str(stats['total_rows']))
        stats_table.add_row("Compatible Rows", str(stats['compatible_rows']))
        stats_table.add_row("Compatibility Rate", f"{stats['compatibility_rate']:.1%}")
        stats_table.add_row("Average Prompt Length", f"{stats['average_prompt_length']:.0f} chars")
        
        console.print(stats_table)
        
        # Show validation errors if any
        validation_errors = prompt_generator.validate_template_compatibility(prompt_template, evaluation_data)
        if validation_errors:
            console.print("\n[red]Template Validation Issues:[/red]")
            for error in validation_errors:
                console.print(f"  [red]• {error}[/red]")
        
    except Exception as e:
        console.print(f"[red]Preview failed: {e}[/red]")
        sys.exit(1)


@cli.command('analyze')
@click.option('--input', '-i', 'input_file', required=True, help='Results file to analyze (CSV/Excel)')
@click.option('--output', '-o', help='Output directory for analysis report (optional)')
@click.option('--models', '-m', help='Comma-separated list of model IDs to compare (optional)')
@click.option('--format', 'output_format', type=click.Choice(['text', 'csv', 'excel']), default='text', help='Output format')
@click.pass_context
def analyze(ctx, input_file, output, models, output_format):
    """Analyze evaluation results and generate reports."""
    try:
        # Validate input file
        if not validate_file_path(input_file):
            console.print(f"[red]Results file not found or not readable: {input_file}[/red]")
            sys.exit(1)
        
        from ..services.analysis import AnalysisEngine
        
        console.print(f"[blue]Loading results from {input_file}...[/blue]")
        
        analysis_engine = AnalysisEngine()
        results = analysis_engine.load_results(input_file)
        
        console.print(f"[green]✓ Loaded {len(results)} result rows[/green]")
        
        if not results:
            console.print("[yellow]No results to analyze[/yellow]")
            return
        
        # Parse model IDs if provided
        model_ids = None
        if models:
            model_ids = [m.strip() for m in models.split(',')]
        
        # Generate analysis
        console.print("[blue]Generating analysis...[/blue]")
        
        analysis_result = analysis_engine.analyze_results(results)
        comparison_report = analysis_engine.compare_models(results, model_ids)
        statistics_report = analysis_engine.generate_statistics(results)
        
        # Display analysis results
        console.print("\n[bold green]Analysis Results:[/bold green]")
        
        # Basic statistics
        basic_table = Table(title="Basic Statistics")
        basic_table.add_column("Metric", style="cyan")
        basic_table.add_column("Value", style="green")
        
        basic_table.add_row("Total Rows", str(analysis_result.total_rows))
        basic_table.add_row("Total Models", str(analysis_result.model_count))
        basic_table.add_row("Total Evaluations", str(statistics_report.total_evaluations))
        basic_table.add_row("Successful Evaluations", str(statistics_report.successful_evaluations))
        basic_table.add_row("Failed Evaluations", str(statistics_report.failed_evaluations))
        
        overall_success_rate = (statistics_report.successful_evaluations / 
                              statistics_report.total_evaluations if statistics_report.total_evaluations > 0 else 0)
        basic_table.add_row("Overall Success Rate", f"{overall_success_rate:.1%}")
        
        console.print(basic_table)
        
        # Model comparison
        if comparison_report.models:
            console.print("\n[bold green]Model Performance Comparison:[/bold green]")
            
            model_table = Table()
            model_table.add_column("Model ID", style="cyan")
            model_table.add_column("Success Rate", style="green")
            model_table.add_column("Avg Response Time", style="blue")
            model_table.add_column("Total Requests", style="yellow")
            model_table.add_column("Failed Requests", style="red")
            model_table.add_column("Avg Response Length", style="magenta")
            
            for model_id in comparison_report.models:
                metrics = comparison_report.metrics[model_id]
                model_table.add_row(
                    model_id,
                    f"{metrics['success_rate']:.1%}",
                    f"{metrics['average_execution_time']:.2f}s",
                    str(int(metrics['total_requests'])),
                    str(int(metrics['failed_requests'])),
                    f"{metrics['average_response_length']:.0f} chars"
                )
            
            console.print(model_table)
            
            # Show comparison summary
            console.print(f"\n[bold blue]Summary:[/bold blue] {comparison_report.summary}")
        
        # Find best performing models
        best_success = analysis_engine.find_best_performing_model(results, 'success_rate')
        best_speed = analysis_engine.find_best_performing_model(results, 'response_time')
        
        if best_success or best_speed:
            console.print("\n[bold green]Best Performing Models:[/bold green]")
            
            if best_success:
                console.print(f"[green]• Highest Success Rate:[/green] {best_success[0]} ({best_success[1]:.1%})")
            
            if best_speed:
                console.print(f"[blue]• Fastest Response Time:[/blue] {best_speed[0]} ({best_speed[1]:.2f}s)")
        
        # Identify problematic rows
        problematic_rows = analysis_engine.identify_problematic_rows(results, threshold=0.3)
        
        if problematic_rows:
            console.print(f"\n[yellow]Problematic Rows (>30% failure rate): {len(problematic_rows)}[/yellow]")
            
            if len(problematic_rows) <= 5:  # Show details for first 5
                problem_table = Table()
                problem_table.add_column("Row", style="cyan")
                problem_table.add_column("Prompt", style="green")
                problem_table.add_column("Failure Rate", style="red")
                problem_table.add_column("Errors", style="yellow")
                
                for row in problematic_rows:
                    prompt_preview = row['original_prompt'][:50] + "..." if len(row['original_prompt']) > 50 else row['original_prompt']
                    error_models = list(row['errors'].keys())
                    
                    problem_table.add_row(
                        str(row['row_index']),
                        prompt_preview,
                        f"{row['failure_rate']:.1%}",
                        ", ".join(error_models)
                    )
                
                console.print(problem_table)
            else:
                console.print(f"[yellow]Use --output option to save detailed problematic rows analysis[/yellow]")
        
        # Model agreement analysis
        agreement_scores = analysis_engine.calculate_model_agreement(results)
        
        if agreement_scores:
            console.print("\n[bold green]Model Agreement Analysis:[/bold green]")
            
            agreement_table = Table()
            agreement_table.add_column("Model Pair", style="cyan")
            agreement_table.add_column("Agreement Rate", style="green")
            
            for pair, score in agreement_scores.items():
                agreement_table.add_row(pair.replace('_vs_', ' vs '), f"{score:.1%}")
            
            console.print(agreement_table)
        
        # Save detailed report if output specified
        if output:
            from pathlib import Path
            
            output_dir = Path(output)
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # Prepare detailed analysis data
            detailed_analysis = {
                'summary': {
                    'total_rows': analysis_result.total_rows,
                    'model_count': analysis_result.model_count,
                    'total_evaluations': statistics_report.total_evaluations,
                    'successful_evaluations': statistics_report.successful_evaluations,
                    'failed_evaluations': statistics_report.failed_evaluations,
                    'overall_success_rate': overall_success_rate
                },
                'model_metrics': comparison_report.metrics,
                'detailed_results': [
                    {
                        'row_index': i + 1,
                        'original_prompt': result.original_prompt,
                        'variable_a': result.variable_a,
                        'variable_b': result.variable_b,
                        'expected_result': result.expected_result,
                        **{f'{model_id}_result': result.model_results.get(model_id, '') for model_id in analysis_result.success_rates.keys()},
                        **{f'{model_id}_execution_time': result.execution_time.get(model_id, 0.0) for model_id in analysis_result.success_rates.keys()},
                        **{f'{model_id}_error': result.error_info.get(model_id, '') for model_id in analysis_result.success_rates.keys()}
                    }
                    for i, result in enumerate(results)
                ],
                'problematic_rows': problematic_rows,
                'model_agreement': agreement_scores
            }
            
            # Save report
            if output_format == 'csv':
                output_file = output_dir / "analysis_report.csv"
                analysis_engine.file_handler.export_analysis_report(detailed_analysis, output_file)
            elif output_format == 'excel':
                output_file = output_dir / "analysis_report.xlsx"
                analysis_engine.file_handler.export_analysis_report(detailed_analysis, output_file)
            else:  # text format
                output_file = output_dir / "analysis_report.txt"
                
                # Generate text report
                report_text = f"""AI Model Evaluation Analysis Report
Generated: {statistics_report.created_at}

SUMMARY
=======
Total Rows: {analysis_result.total_rows}
Total Models: {analysis_result.model_count}
Total Evaluations: {statistics_report.total_evaluations}
Successful Evaluations: {statistics_report.successful_evaluations}
Failed Evaluations: {statistics_report.failed_evaluations}
Overall Success Rate: {overall_success_rate:.1%}

MODEL PERFORMANCE
================
{comparison_report.summary}

"""
                
                # Add detailed model metrics
                for model_id in comparison_report.models:
                    metrics = comparison_report.metrics[model_id]
                    report_text += f"""
{model_id.upper()}:
  Success Rate: {metrics['success_rate']:.1%}
  Average Response Time: {metrics['average_execution_time']:.2f}s
  Total Requests: {int(metrics['total_requests'])}
  Failed Requests: {int(metrics['failed_requests'])}
  Average Response Length: {metrics['average_response_length']:.0f} characters
"""
                
                # Add problematic rows
                if problematic_rows:
                    report_text += f"\nPROBLEMATIC ROWS ({len(problematic_rows)} rows with >30% failure rate)\n"
                    report_text += "=" * 50 + "\n"
                    
                    for row in problematic_rows:
                        report_text += f"""
Row {row['row_index']}: {row['failure_rate']:.1%} failure rate
Prompt: {row['original_prompt']}
Failed Models: {', '.join(row['errors'].keys())}
"""
                
                # Add model agreement
                if agreement_scores:
                    report_text += "\nMODEL AGREEMENT\n"
                    report_text += "=" * 15 + "\n"
                    
                    for pair, score in agreement_scores.items():
                        report_text += f"{pair.replace('_vs_', ' vs ')}: {score:.1%}\n"
                
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(report_text)
            
            console.print(f"[green]✓ Detailed analysis report saved to: {output_file}[/green]")
        
        console.print("\n[green]✓ Analysis completed successfully![/green]")
        
    except Exception as e:
        console.print(f"[red]Analysis failed: {e}[/red]")
        sys.exit(1)


@cli.command('info')
@click.option('--file', '-f', help='Show information about a data file')
@click.pass_context
def info(ctx, file):
    """Show system information or file information."""
    if file:
        # Show file information
        try:
            from ..services.file_handler import FileHandler
            
            if not validate_file_path(file):
                console.print(f"[red]File not found or not readable: {file}[/red]")
                sys.exit(1)
            
            file_handler = FileHandler()
            file_info = file_handler.get_file_info(file)
            
            console.print(f"\n[bold green]File Information: {file}[/bold green]")
            
            info_table = Table()
            info_table.add_column("Property", style="cyan")
            info_table.add_column("Value", style="green")
            
            info_table.add_row("Path", str(file_info['path']))
            info_table.add_row("Size", f"{file_info['size']} bytes")
            info_table.add_row("Extension", file_info['extension'])
            info_table.add_row("Supported", "✓ Yes" if file_info['is_supported'] else "✗ No")
            
            if file_info['is_supported']:
                info_table.add_row("Rows", str(file_info['row_count']))
                info_table.add_row("Columns", str(file_info['column_count']))
                info_table.add_row("Valid", "✓ Yes" if file_info.get('is_valid', False) else "✗ No")
                
                if 'columns' in file_info:
                    columns_str = ", ".join(file_info['columns'])
                    info_table.add_row("Column Names", columns_str)
            
            console.print(info_table)
            
            # Show validation errors/warnings if any
            if 'errors' in file_info and file_info['errors']:
                console.print("\n[red]Validation Errors:[/red]")
                for error in file_info['errors']:
                    console.print(f"  [red]• {error}[/red]")
            
            if 'warnings' in file_info and file_info['warnings']:
                console.print("\n[yellow]Warnings:[/yellow]")
                for warning in file_info['warnings']:
                    console.print(f"  [yellow]• {warning}[/yellow]")
                    
        except Exception as e:
            console.print(f"[red]Error getting file information: {e}[/red]")
            sys.exit(1)
    else:
        # Show system information
        console.print(Panel.fit(
            "[bold blue]AI Model Evaluation System[/bold blue]\n"
            "Version: 0.1.0\n"
            "Python Package for evaluating and comparing AI models\n\n"
            "[bold]Features:[/bold]\n"
            "• Multi-provider support (OpenAI compatible APIs)\n"
            "• Async batch processing\n"
            "• Template-based prompt generation\n"
            "• Comprehensive result analysis\n"
            "• CSV/Excel file support\n\n"
            "[bold]Usage:[/bold]\n"
            "Use 'ai-eval --help' to see all available commands",
            title="System Information"
        ))


@cli.group()
@click.pass_context
def report(ctx):
    """Generate analysis reports from evaluation results."""
    pass

@report.command("unified")
@click.argument('results_file', type=click.Path(exists=True))
@click.option('--output-dir', type=click.Path(),
              help="Output directory (defaults to same directory as results file)")
@click.pass_context
def report_unified(ctx, results_file, output_dir):
    """Generate unified analysis reports (same as auto_evaluate.py)."""
    from ..services.evaluation_report_generator import EvaluationReportGenerator
    from pathlib import Path

    try:
        results_path = Path(results_file)

        if output_dir:
            output_directory = Path(output_dir)
        else:
            output_directory = results_path.parent

        output_directory.mkdir(parents=True, exist_ok=True)

        console.print(f"[blue]Generating unified reports from {results_file}...[/blue]")

        # 使用与auto_evaluate.py相同的报告生成器
        report_generator = EvaluationReportGenerator()

        # 生成分析报告
        analysis_reports = report_generator.generate_analysis_report(results_path, output_directory)

        # 生成分类指标报告
        metrics_reports = report_generator.generate_classification_metrics_report(results_path, output_directory)

        console.print(f"[green]✓ Unified reports generated successfully![/green]")
        console.print(f"[blue]Analysis reports:[/blue]")
        for report_type, file_path in analysis_reports.items():
            console.print(f"  - {report_type.upper()}: {file_path}")

        console.print(f"[blue]Classification metrics reports:[/blue]")
        for report_type, file_path in metrics_reports.items():
            console.print(f"  - {report_type.upper()}: {file_path}")

    except Exception as e:
        console.print(f"[red]Error generating unified reports: {e}[/red]")
        sys.exit(1)

@report.command("generate")
@click.argument("results_file", type=click.Path(exists=True))
@click.option("--format", "output_format", 
              type=click.Choice(['text', 'json', 'csv', 'html']), 
              default='text', 
              help="Output format")
@click.option("--output", "output_path", 
              type=click.Path(), 
              help="Output file path (auto-generated if not provided)")
@click.pass_context
def report_generate(ctx, results_file, output_format, output_path):
    """Generate analysis report from evaluation results."""
    file_handler = ctx.obj['file_handler']
    
    try:
        console.print(f"[blue]Loading results from {results_file}...[/blue]")
        
        # Load results using the analysis engine
        from ..services.analysis import AnalysisEngine
        analysis_engine = AnalysisEngine()
        results = analysis_engine.load_results(results_file)
        
        console.print(f"[green]✓ Loaded {len(results)} result rows[/green]")
        
        # Generate output path if not provided
        if not output_path:
            results_path = Path(results_file)
            output_path = results_path.parent / f"{results_path.stem}_report.{output_format}"
        
        # Generate report
        generator = ReportGenerator()
        
        console.print(f"[blue]Generating {output_format.upper()} report...[/blue]")
        
        if output_format == 'text':
            report_content = generator.generate_text_report(results, str(output_path))
        elif output_format == 'json':
            report_content = generator.generate_json_report(results, str(output_path))
        elif output_format == 'csv':
            report_content = generator.generate_csv_summary(results, str(output_path))
        elif output_format == 'html':
            report_content = generator.generate_html_report(results, str(output_path))
        
        console.print(f"[green]✓ Report generated successfully![/green]")
        console.print(f"[blue]Report saved to: {output_path}[/blue]")
        
        # Show brief summary
        if output_format == 'json' and isinstance(report_content, dict):
            summary = report_content.get('executive_summary', {})
            console.print(f"[blue]Total rows: {summary.get('total_rows', 0)}[/blue]")
            console.print(f"[blue]Models: {summary.get('model_count', 0)}[/blue]")
            console.print(f"[blue]Success rate: {summary.get('overall_success_rate', 0):.1%}[/blue]")
        
    except FileProcessingError as e:
        console.print(f"[red]File processing error: {e}[/red]")
        sys.exit(1)
    except Exception as e:
        console.print(f"[red]Error generating report: {e}[/red]")
        sys.exit(1)

@report.command("analyze")
@click.argument("results_file", type=click.Path(exists=True))
@click.option("--models", help="Comma-separated list of model IDs to analyze")
@click.option("--threshold", default=0.5, type=float, help="Failure rate threshold for problematic cases")
@click.pass_context
def report_analyze(ctx, results_file, models, threshold):
    """Perform detailed analysis of evaluation results."""
    try:
        console.print(f"[blue]Analyzing results from {results_file}...[/blue]")
        
        # Load results
        from ..services.analysis import AnalysisEngine
        analysis_engine = AnalysisEngine()
        results = analysis_engine.load_results(results_file)
        
        console.print(f"[green]✓ Loaded {len(results)} result rows[/green]")
        
        # Parse model filter if provided
        model_filter = None
        if models:
            model_filter = [m.strip() for m in models.split(",")]
            console.print(f"[blue]Filtering analysis to models: {', '.join(model_filter)}[/blue]")
        
        # Perform analysis
        analysis = analysis_engine.analyze_results(results)
        comparison = analysis_engine.compare_models(results, model_filter)
        statistics = analysis_engine.generate_statistics(results)
        
        # Display results
        console.print("\n[bold blue]Analysis Results[/bold blue]")
        
        # Overall metrics
        table = Table(title="Overall Metrics")
        table.add_column("Metric", style="cyan")
        table.add_column("Value", style="green")
        
        overall_success_rate = sum(analysis.success_rates.values()) / len(analysis.success_rates) if analysis.success_rates else 0.0
        
        table.add_row("Total Rows", str(analysis.total_rows))
        table.add_row("Models Evaluated", str(analysis.model_count))
        table.add_row("Total Evaluations", str(statistics.total_evaluations))
        table.add_row("Successful Evaluations", str(statistics.successful_evaluations))
        table.add_row("Failed Evaluations", str(statistics.failed_evaluations))
        table.add_row("Overall Success Rate", f"{overall_success_rate:.1%}")
        
        console.print(table)
        
        # Model performance
        if comparison.models:
            table = Table(title="Model Performance")
            table.add_column("Model", style="cyan")
            table.add_column("Success Rate", style="green")
            table.add_column("Avg Time (s)", style="yellow")
            table.add_column("Requests", style="blue")
            table.add_column("Errors", style="red")
            
            for model_id in comparison.models:
                metrics = comparison.metrics[model_id]
                table.add_row(
                    model_id,
                    f"{metrics['success_rate']:.1%}",
                    f"{metrics['average_execution_time']:.2f}",
                    f"{int(metrics['total_requests'])}",
                    f"{int(metrics['failed_requests'])}"
                )
            
            console.print(table)
        
        # Find best performing model
        best_model = analysis_engine.find_best_performing_model(results, 'success_rate')
        if best_model:
            console.print(f"\n[green]Best performing model: {best_model[0]} ({best_model[1]:.1%} success rate)[/green]")
        
        fastest_model = analysis_engine.find_best_performing_model(results, 'response_time')
        if fastest_model:
            console.print(f"[blue]Fastest model: {fastest_model[0]} ({fastest_model[1]:.2f}s avg)[/blue]")
        
        # Problematic cases
        problematic_rows = analysis_engine.identify_problematic_rows(results, threshold)
        if problematic_rows:
            console.print(f"\n[yellow]Found {len(problematic_rows)} problematic cases (>{threshold:.0%} failure rate)[/yellow]")
            
            # Show top 5 problematic cases
            table = Table(title="Top Problematic Cases")
            table.add_column("Row", style="cyan")
            table.add_column("Prompt", style="green")
            table.add_column("Failure Rate", style="red")
            table.add_column("Failed Models", style="yellow")
            
            for case in problematic_rows[:5]:
                prompt_preview = case['original_prompt'][:50] + "..." if len(case['original_prompt']) > 50 else case['original_prompt']
                table.add_row(
                    str(case['row_index']),
                    prompt_preview,
                    f"{case['failure_rate']:.1%}",
                    f"{case['failed_models']}/{case['total_models']}"
                )
            
            console.print(table)
        
        # Model agreement
        agreement = analysis_engine.calculate_model_agreement(results)
        if agreement:
            console.print(f"\n[bold blue]Model Agreement[/bold blue]")
            for pair, score in agreement.items():
                console.print(f"[blue]{pair}: {score:.1%} agreement[/blue]")
        
    except FileProcessingError as e:
        console.print(f"[red]File processing error: {e}[/red]")
        sys.exit(1)
    except Exception as e:
        console.print(f"[red]Error analyzing results: {e}[/red]")
        sys.exit(1)

@cli.group()
@click.pass_context
def history(ctx):
    """Manage evaluation task history."""
    ctx.ensure_object(dict)
    ctx.obj['history_manager'] = HistoryManager()

@history.command("list")
@click.option("--status", 
              type=click.Choice(['pending', 'running', 'completed', 'failed', 'cancelled']), 
              help="Filter by task status")
@click.option("--limit", default=20, type=int, help="Maximum number of tasks to show")
@click.option("--offset", default=0, type=int, help="Number of tasks to skip")
@click.pass_context
def history_list(ctx, status, limit, offset):
    """List task history."""
    history_manager = ctx.obj['history_manager']
    
    try:
        # Convert status string to enum if provided
        status_filter = None
        if status:
            status_filter = TaskStatus(status)
        
        tasks = history_manager.list_tasks(status=status_filter, limit=limit, offset=offset)
        
        if not tasks:
            console.print("[yellow]No tasks found in history[/yellow]")
            return
        
        # Display tasks in a table
        table = Table(title=f"Task History ({len(tasks)} tasks)")
        table.add_column("ID", style="cyan")
        table.add_column("Name", style="green")
        table.add_column("Status", style="yellow")
        table.add_column("Models", style="blue")
        table.add_column("Created", style="magenta")
        table.add_column("Duration", style="white")
        
        for task in tasks:
            # Calculate duration
            duration = "N/A"
            if task.started_at and task.completed_at:
                duration_seconds = (task.completed_at - task.started_at).total_seconds()
                if duration_seconds < 60:
                    duration = f"{duration_seconds:.1f}s"
                elif duration_seconds < 3600:
                    duration = f"{duration_seconds/60:.1f}m"
                else:
                    duration = f"{duration_seconds/3600:.1f}h"
            elif task.started_at:
                duration = "Running..."
            
            # Format status with color
            status_color = {
                TaskStatus.PENDING: "yellow",
                TaskStatus.RUNNING: "blue",
                TaskStatus.COMPLETED: "green",
                TaskStatus.FAILED: "red",
                TaskStatus.CANCELLED: "gray"
            }.get(task.status, "white")
            
            # Truncate model list if too long
            models_str = ", ".join(task.selected_models[:3])
            if len(task.selected_models) > 3:
                models_str += f" (+{len(task.selected_models) - 3})"
            
            table.add_row(
                task.id[:8] + "...",
                task.name[:30] + "..." if len(task.name) > 30 else task.name,
                f"[{status_color}]{task.status.value}[/{status_color}]",
                models_str,
                task.created_at.strftime("%Y-%m-%d %H:%M"),
                duration
            )
        
        console.print(table)
        
        # Show pagination info
        if limit and len(tasks) == limit:
            console.print(f"\n[blue]Showing {offset + 1}-{offset + len(tasks)} tasks. Use --offset {offset + limit} to see more.[/blue]")
        
    except HistoryError as e:
        console.print(f"[red]History error: {e}[/red]")
        sys.exit(1)
    except Exception as e:
        console.print(f"[red]Error listing history: {e}[/red]")
        sys.exit(1)

@history.command("show")
@click.argument("task_id")
@click.option("--show-results", is_flag=True, help="Show evaluation results")
@click.option("--show-logs", is_flag=True, help="Show task logs")
@click.pass_context
def history_show(ctx, task_id, show_results, show_logs):
    """Show detailed information about a task."""
    history_manager = ctx.obj['history_manager']
    
    try:
        task = history_manager.get_task(task_id)
        if not task:
            console.print(f"[red]Task '{task_id}' not found in history[/red]")
            return
        
        # Show task details
        info_text = f"""
[bold]Task ID:[/bold] {task.id}
[bold]Name:[/bold] {task.name}
[bold]Status:[/bold] {task.status.value}
[bold]Template:[/bold] {task.prompt_template_id}
[bold]Models:[/bold] {', '.join(task.selected_models)}
[bold]Input File:[/bold] {task.input_file_path}
[bold]Output Directory:[/bold] {task.output_directory}
[bold]Created:[/bold] {task.created_at.strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        if task.started_at:
            info_text += f"[bold]Started:[/bold] {task.started_at.strftime('%Y-%m-%d %H:%M:%S')}\n"
        
        if task.completed_at:
            info_text += f"[bold]Completed:[/bold] {task.completed_at.strftime('%Y-%m-%d %H:%M:%S')}\n"
            duration = (task.completed_at - task.started_at).total_seconds() if task.started_at else 0
            info_text += f"[bold]Duration:[/bold] {duration:.1f} seconds\n"
        
        if task.error_message:
            info_text += f"[bold red]Error:[/bold red] {task.error_message}\n"
        
        if task.metadata:
            info_text += f"[bold]Metadata:[/bold] {task.metadata}\n"
        
        console.print(Panel(info_text.strip(), title="Task Details", border_style="blue"))
        
        # Show results if requested
        if show_results:
            console.print("\n[bold blue]Evaluation Results[/bold blue]")
            results = history_manager.get_task_results(task_id)
            
            if not results:
                console.print("[yellow]No results found for this task[/yellow]")
            else:
                # Show summary
                console.print(f"[blue]Total rows: {len(results)}[/blue]")
                
                # Show first few results
                for i, result in enumerate(results[:5]):
                    console.print(f"\n[cyan]Row {result['row_index'] + 1}:[/cyan]")
                    console.print(f"  Prompt: {result['original_prompt'][:100]}...")
                    
                    for model_id, response in result['model_results'].items():
                        if result['error_info'] and result['error_info'].get(model_id):
                            console.print(f"  {model_id}: [red]ERROR - {result['error_info'][model_id]}[/red]")
                        else:
                            response_preview = response[:80] + "..." if len(response) > 80 else response
                            exec_time = result['execution_time'].get(model_id, 0)
                            console.print(f"  {model_id}: {response_preview} [dim]({exec_time:.2f}s)[/dim]")
                
                if len(results) > 5:
                    console.print(f"\n[dim]... and {len(results) - 5} more rows[/dim]")
        
        # Show logs if requested
        if show_logs:
            console.print("\n[bold blue]Task Logs[/bold blue]")
            logs = history_manager.get_task_logs(task_id)
            
            if not logs:
                console.print("[yellow]No logs found for this task[/yellow]")
            else:
                for log in logs:
                    timestamp = datetime.fromisoformat(log['timestamp']).strftime('%H:%M:%S')
                    level_color = {
                        'INFO': 'blue',
                        'WARNING': 'yellow',
                        'ERROR': 'red'
                    }.get(log['level'], 'white')
                    
                    console.print(f"[dim]{timestamp}[/dim] [{level_color}]{log['level']}[/{level_color}] {log['message']}")
                    
                    if log['details']:
                        console.print(f"  [dim]{log['details']}[/dim]")
        
    except HistoryError as e:
        console.print(f"[red]History error: {e}[/red]")
        sys.exit(1)
    except Exception as e:
        console.print(f"[red]Error showing task: {e}[/red]")
        sys.exit(1)

@history.command("search")
@click.argument("query")
@click.option("--limit", default=20, type=int, help="Maximum number of results")
@click.pass_context
def history_search(ctx, query, limit):
    """Search task history."""
    history_manager = ctx.obj['history_manager']
    
    try:
        tasks = history_manager.search_tasks(query, limit=limit)
        
        if not tasks:
            console.print(f"[yellow]No tasks found matching '{query}'[/yellow]")
            return
        
        console.print(f"[blue]Found {len(tasks)} tasks matching '{query}'[/blue]\n")
        
        # Display results
        table = Table(title="Search Results")
        table.add_column("ID", style="cyan")
        table.add_column("Name", style="green")
        table.add_column("Status", style="yellow")
        table.add_column("Created", style="magenta")
        
        for task in tasks:
            status_color = {
                TaskStatus.PENDING: "yellow",
                TaskStatus.RUNNING: "blue",
                TaskStatus.COMPLETED: "green",
                TaskStatus.FAILED: "red",
                TaskStatus.CANCELLED: "gray"
            }.get(task.status, "white")
            
            table.add_row(
                task.id[:8] + "...",
                task.name[:40] + "..." if len(task.name) > 40 else task.name,
                f"[{status_color}]{task.status.value}[/{status_color}]",
                task.created_at.strftime("%Y-%m-%d %H:%M")
            )
        
        console.print(table)
        
    except HistoryError as e:
        console.print(f"[red]History error: {e}[/red]")
        sys.exit(1)
    except Exception as e:
        console.print(f"[red]Error searching history: {e}[/red]")
        sys.exit(1)

@history.command("delete")
@click.argument("task_id")
@click.confirmation_option(prompt="Are you sure you want to delete this task and all its data?")
@click.pass_context
def history_delete(ctx, task_id):
    """Delete a task from history."""
    history_manager = ctx.obj['history_manager']
    
    try:
        deleted = history_manager.delete_task(task_id)
        
        if deleted:
            console.print(f"[green]✓ Task '{task_id}' deleted from history[/green]")
        else:
            console.print(f"[yellow]Task '{task_id}' not found in history[/yellow]")
        
    except HistoryError as e:
        console.print(f"[red]History error: {e}[/red]")
        sys.exit(1)
    except Exception as e:
        console.print(f"[red]Error deleting task: {e}[/red]")
        sys.exit(1)

@history.command("export")
@click.argument("task_id")
@click.option("--output", "output_path", 
              type=click.Path(), 
              help="Output file path (defaults to task_id.json)")
@click.pass_context
def history_export(ctx, task_id, output_path):
    """Export task data to JSON file."""
    history_manager = ctx.obj['history_manager']
    
    try:
        # Generate output path if not provided
        if not output_path:
            output_path = f"{task_id}.json"
        
        console.print(f"[blue]Exporting task '{task_id}' to {output_path}...[/blue]")
        
        history_manager.export_task_data(task_id, output_path)
        
        console.print(f"[green]✓ Task data exported successfully![/green]")
        console.print(f"[blue]Export saved to: {output_path}[/blue]")
        
        # Show file size
        file_size = os.path.getsize(output_path)
        if file_size < 1024:
            size_str = f"{file_size} bytes"
        elif file_size < 1024 * 1024:
            size_str = f"{file_size / 1024:.1f} KB"
        else:
            size_str = f"{file_size / (1024 * 1024):.1f} MB"
        
        console.print(f"[blue]File size: {size_str}[/blue]")
        
    except HistoryError as e:
        console.print(f"[red]History error: {e}[/red]")
        sys.exit(1)
    except Exception as e:
        console.print(f"[red]Error exporting task: {e}[/red]")
        sys.exit(1)

@history.command("stats")
@click.pass_context
def history_stats(ctx):
    """Show history statistics."""
    history_manager = ctx.obj['history_manager']
    
    try:
        stats = history_manager.get_task_statistics()
        
        console.print("[bold blue]Task History Statistics[/bold blue]\n")
        
        # Overall stats
        table = Table(title="Task Counts by Status")
        table.add_column("Status", style="cyan")
        table.add_column("Count", style="green")
        table.add_column("Percentage", style="yellow")
        
        total = stats['total']
        
        for status in TaskStatus:
            count = stats.get(status.value, 0)
            percentage = (count / total * 100) if total > 0 else 0
            
            status_color = {
                TaskStatus.PENDING: "yellow",
                TaskStatus.RUNNING: "blue",
                TaskStatus.COMPLETED: "green",
                TaskStatus.FAILED: "red",
                TaskStatus.CANCELLED: "gray"
            }.get(status, "white")
            
            table.add_row(
                f"[{status_color}]{status.value.title()}[/{status_color}]",
                str(count),
                f"{percentage:.1f}%"
            )
        
        table.add_row("[bold]Total[/bold]", f"[bold]{total}[/bold]", "[bold]100.0%[/bold]")
        
        console.print(table)
        
        # Success rate
        if total > 0:
            completed = stats.get('completed', 0)
            failed = stats.get('failed', 0)
            finished = completed + failed
            
            if finished > 0:
                success_rate = completed / finished * 100
                console.print(f"\n[blue]Success Rate: {success_rate:.1f}% ({completed}/{finished} completed tasks)[/blue]")
        
    except HistoryError as e:
        console.print(f"[red]History error: {e}[/red]")
        sys.exit(1)
    except Exception as e:
        console.print(f"[red]Error getting statistics: {e}[/red]")
        sys.exit(1)

@history.command("cleanup")
@click.option("--days", default=30, type=int, help="Delete tasks older than this many days")
@click.option("--dry-run", is_flag=True, help="Show what would be deleted without actually deleting")
@click.confirmation_option(prompt="This will permanently delete old completed/failed tasks. Continue?")
@click.pass_context
def history_cleanup(ctx, days, dry_run):
    """Clean up old completed/failed tasks."""
    history_manager = ctx.obj['history_manager']
    
    try:
        if dry_run:
            console.print(f"[blue]Dry run: Finding tasks older than {days} days...[/blue]")
            
            # Get tasks that would be deleted
            from datetime import timedelta
            cutoff_date = datetime.now() - timedelta(days=days)
            
            # This is a simplified version - in a real implementation, 
            # we'd add a method to preview cleanup
            console.print(f"[yellow]Would delete completed/failed tasks created before {cutoff_date.strftime('%Y-%m-%d')}[/yellow]")
            console.print("[blue]Use without --dry-run to actually delete[/blue]")
        else:
            console.print(f"[blue]Cleaning up tasks older than {days} days...[/blue]")
            
            deleted_count = history_manager.cleanup_old_tasks(days=days)
            
            if deleted_count > 0:
                console.print(f"[green]✓ Deleted {deleted_count} old tasks[/green]")
            else:
                console.print("[blue]No old tasks found to delete[/blue]")
        
    except HistoryError as e:
        console.print(f"[red]History error: {e}[/red]")
        sys.exit(1)
    except Exception as e:
        console.print(f"[red]Error during cleanup: {e}[/red]")
        sys.exit(1)

if __name__ == '__main__':
    cli()