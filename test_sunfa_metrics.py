#!/usr/bin/env python3
"""
测试脚本：验证根据 sunfa.md 文档修改后的指标计算逻辑

这个脚本创建一个示例数据集，模拟 sunfa.md 文档中的混淆矩阵示例，
验证修改后的指标计算是否符合文档要求。
"""

import pandas as pd
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from ai_model_evaluation.services.evaluation_report_generator import EvaluationReportGenerator
from ai_model_evaluation.services.analysis import AnalysisEngine

def create_test_data():
    """
    创建测试数据，模拟 sunfa.md 文档中的示例：

    混淆矩阵：
    | 真实 \\ 预测 | Booking | Cancellation | none | 真实总计 |
    | :---------- | :------ | :----------- | :--- | :----------- |
    | Booking     | 80 (TP) | 10           | 10 (FN) | 100      |
    | Cancellation| 15      | 60 (TP)      | 25 (FN) | 100      |
    | none        | 5 (FP)  | 3 (FP)       | 92 (TN) | 100      |
    """
    
    data = []
    
    # Booking 类别：80个正确，10个误判为Cancellation，10个误判为none
    for i in range(80):
        data.append({'expected_result': 'Booking', 'model_result': 'Booking'})
    for i in range(10):
        data.append({'expected_result': 'Booking', 'model_result': 'Cancellation'})
    for i in range(10):
        data.append({'expected_result': 'Booking', 'model_result': 'none'})
    
    # Cancellation 类别：60个正确，15个误判为Booking，25个误判为none
    for i in range(60):
        data.append({'expected_result': 'Cancellation', 'model_result': 'Cancellation'})
    for i in range(15):
        data.append({'expected_result': 'Cancellation', 'model_result': 'Booking'})
    for i in range(25):
        data.append({'expected_result': 'Cancellation', 'model_result': 'none'})
    
    # none 类别：92个正确，5个误判为Booking，3个误判为Cancellation
    for i in range(92):
        data.append({'expected_result': 'none', 'model_result': 'none'})
    for i in range(5):
        data.append({'expected_result': 'none', 'model_result': 'Booking'})
    for i in range(3):
        data.append({'expected_result': 'none', 'model_result': 'Cancellation'})
    
    return pd.DataFrame(data)

def print_expected_results():
    """打印期望的计算结果（根据 sunfa.md 文档）"""
    print("=" * 60)
    print("期望结果（根据 sunfa.md 文档）：")
    print("=" * 60)
    print("总 TP = 80 + 60 = 140")
    print("总 FP = (10 + 5) + (15 + 3) = 15 + 18 = 33")
    print("总 FN = 10 + 25 = 35")
    print("总 TN = 92")
    print()
    print("召回率 (Recall) = 140 / (140 + 35) = 140 / 175 = 0.80")
    print("精确率 (Precision) = 140 / (140 + 33) = 140 / 173 ≈ 0.809")
    print("F1 值 = 2 * (0.809 * 0.80) / (0.809 + 0.80) ≈ 0.805")
    print("准确率 (Accuracy) = (140 + 92) / 300 = 232 / 300 ≈ 0.773")
    print()

def test_evaluation_report_generator():
    """测试 EvaluationReportGenerator 的计算结果"""
    print("=" * 60)
    print("测试 EvaluationReportGenerator")
    print("=" * 60)
    
    df = create_test_data()
    generator = EvaluationReportGenerator()
    
    # 测试详细分类指标计算
    result = generator._calculate_detailed_classification_metrics(df, 'model_result')
    
    print(f"准确率: {result['accuracy']:.3f}")
    print(f"精确率: {result['precision']:.3f}")
    print(f"召回率: {result['recall']:.3f}")
    print(f"F1分数: {result['f1_score']:.3f}")
    print()
    
    print("总体指标:")
    print(f"  TP: {result['overall_metrics']['tp']}")
    print(f"  FP: {result['overall_metrics']['fp']}")
    print(f"  FN: {result['overall_metrics']['fn']}")
    print(f"  TN: {result['overall_metrics']['tn']}")
    print()
    
    print("正向类别:", result['positive_classes'])
    print("None类别:", result['none_class'])
    print("总正向样本数:", result['total_positive_samples'])
    print()
    
    print("宏平均:")
    print(f"  精确率: {result['macro_avg']['precision']:.3f}")
    print(f"  召回率: {result['macro_avg']['recall']:.3f}")
    print(f"  F1分数: {result['macro_avg']['f1_score']:.3f}")
    print()
    
    print("加权平均:")
    print(f"  精确率: {result['weighted_avg']['precision']:.3f}")
    print(f"  召回率: {result['weighted_avg']['recall']:.3f}")
    print(f"  F1分数: {result['weighted_avg']['f1_score']:.3f}")
    print()
    
    print("每个类别的详细指标:")
    for class_name, metrics in result['per_class_metrics'].items():
        print(f"  {class_name}:")
        print(f"    精确率: {metrics['precision']:.3f}")
        print(f"    召回率: {metrics['recall']:.3f}")
        print(f"    F1分数: {metrics['f1_score']:.3f}")
        print(f"    支持数: {metrics['support']}")
        print(f"    是否正向类: {metrics['is_positive_class']}")
    print()
    
    if 'accuracy_warning' in result:
        print("警告:", result['accuracy_warning'])
    if 'methodology' in result:
        print("方法说明:", result['methodology'])
    
    return result

def test_analysis_engine():
    """测试 AnalysisEngine 的计算结果"""
    print("=" * 60)
    print("测试 AnalysisEngine")
    print("=" * 60)

    df = create_test_data()
    engine = AnalysisEngine()
    
    y_true = df['expected_result'].tolist()
    y_pred = df['model_result'].tolist()
    
    result = engine._calculate_detailed_classification_metrics(y_true, y_pred, "test_model")
    
    print(f"模型名称: {result['model_name']}")
    print(f"准确率: {result['accuracy']:.3f}")
    print(f"精确率: {result['precision']:.3f}")
    print(f"召回率: {result['recall']:.3f}")
    print(f"F1分数: {result['f1_score']:.3f}")
    print()
    
    print("总体指标:")
    print(f"  TP: {result['overall_metrics']['tp']}")
    print(f"  FP: {result['overall_metrics']['fp']}")
    print(f"  FN: {result['overall_metrics']['fn']}")
    print(f"  TN: {result['overall_metrics']['tn']}")
    print()
    
    if 'accuracy_warning' in result:
        print("警告:", result['accuracy_warning'])
    if 'methodology' in result:
        print("方法说明:", result['methodology'])
    
    return result

def verify_calculations(result1, result2):
    """验证两个服务的计算结果是否一致"""
    print("=" * 60)
    print("验证计算结果一致性")
    print("=" * 60)
    
    # 检查核心指标
    tolerance = 1e-6
    
    metrics_to_check = ['accuracy', 'precision', 'recall', 'f1_score']
    
    for metric in metrics_to_check:
        val1 = result1[metric]
        val2 = result2[metric]
        diff = abs(val1 - val2)
        
        if diff < tolerance:
            print(f"✅ {metric}: {val1:.6f} (一致)")
        else:
            print(f"❌ {metric}: {val1:.6f} vs {val2:.6f} (差异: {diff:.6f})")
    
    # 检查TP/FP/FN/TN
    tp_diff = abs(result1['overall_metrics']['tp'] - result2['overall_metrics']['tp'])
    fp_diff = abs(result1['overall_metrics']['fp'] - result2['overall_metrics']['fp'])
    fn_diff = abs(result1['overall_metrics']['fn'] - result2['overall_metrics']['fn'])
    tn_diff = abs(result1['overall_metrics']['tn'] - result2['overall_metrics']['tn'])
    
    print(f"✅ TP: {result1['overall_metrics']['tp']} (差异: {tp_diff})")
    print(f"✅ FP: {result1['overall_metrics']['fp']} (差异: {fp_diff})")
    print(f"✅ FN: {result1['overall_metrics']['fn']} (差异: {fn_diff})")
    print(f"✅ TN: {result1['overall_metrics']['tn']} (差异: {tn_diff})")

if __name__ == "__main__":
    print("测试根据 sunfa.md 文档修改后的指标计算逻辑")
    print("=" * 60)
    
    # 打印期望结果
    print_expected_results()
    
    # 测试两个服务
    result1 = test_evaluation_report_generator()
    result2 = test_analysis_engine()
    
    # 验证一致性
    verify_calculations(result1, result2)
    
    print("\n测试完成！")
