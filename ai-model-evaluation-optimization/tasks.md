# Implementation Plan

- [ ] 1. 项目结构分析和备份
  - 分析当前项目结构，识别所有文件和依赖关系
  - 创建项目完整备份，确保可以回滚
  - 生成文件清理计划和依赖关系图
  - _Requirements: 1.1, 1.2_

- [ ] 2. 统一CLI和auto_evaluate.py执行引擎
  - [ ] 2.1 创建统一的评测引擎接口
    - 设计UnifiedEvaluationEngine类，整合原版和优化版功能
    - 实现统一的配置加载和引擎选择逻辑
    - 确保CLI和脚本使用相同的核心引擎
    - _Requirements: 1.1, 1.2, 1.3_

  - [ ] 2.2 重构auto_evaluate.py使用统一引擎
    - 修改auto_evaluate.py调用统一的评测引擎
    - 移除直接导入优化服务的代码
    - 确保与CLI产生相同的结果格式和性能
    - _Requirements: 1.2, 1.4_

- [x] 3. 优化配置和依赖管理
  - [ ] 3.1 统一依赖管理文件
    - 移除requirements.txt和requirements-dev.txt
    - 确保pyproject.toml包含所有必要依赖
    - _Requirements: 5.2, 5.3_

  - [x] 3.2 整合配置文件
    - [x] 将performance_config.yaml内容整合到config.yaml
    - [x] 添加performance配置节到主配置文件
    - [x] 更新ConfigManager支持性能配置加载
    - [x] 验证配置文件的完整性和正确性
    - [x] **并发优化完成**：
      - 全局最大并发数从500提升到800
      - CLI默认并发数从5提升到50
      - 各提供商并发数大幅提升（openai: 80→120, tongyi: 70→120等）
      - 批处理大小从100提升到150
      - 连接池大小相应提升
      - 标准评测引擎默认并发从50提升到100
      - 标准API客户端默认并发从20提升到50
      - 优化版API客户端默认并发从100提升到150
    - _Requirements: 5.1, 5.4_

- [ ] 4. 清理分析和工具脚本
  - [ ] 4.1 移除重复的分析脚本
    - 删除analyze_classification_metrics.py
    - 删除performance_comparison.py和performance_analyzer.py
    - 确认分析功能在主系统中可用
    - _Requirements: 2.1, 2.3, 8.1_

  - [ ] 4.2 清理工具脚本
    - 移除convert_excel_to_csv.py、generate_analysis.py等工具脚本
    - 删除show_reports.py等显示脚本
    - 保留scripts/目录中有价值的工具
    - _Requirements: 2.1, 2.3_

  - [ ] 4.3 清理重复的评测脚本
    - 删除optimized_evaluate.py（功能整合到统一引擎）
    - 删除quick_performance_test.py、test_optimized_version.py等临时测试文件
    - 保留auto_evaluate.py作为脚本执行入口
    - _Requirements: 2.1, 2.2_

- [ ] 5. 整理文档结构
  - [ ] 5.1 合并README文件
    - 将README_auto_evaluate.md、README_classification_metrics.md等内容整合到主README.md
    - 删除重复的README文件
    - 确保主README包含所有重要信息
    - _Requirements: 7.1, 7.2, 7.3_

  - [ ] 5.2 清理文档文件
    - 移除UPGRADE_GUIDE.md和PERFORMANCE_OPTIMIZATION_GUIDE.md
    - 将有价值的内容整合到docs/目录
    - 更新文档链接和引用
    - _Requirements: 7.1, 7.2, 7.4_

- [ ] 6. 清理示例和数据文件
  - [ ] 6.1 整理示例数据
    - 保留代表性的示例文件（如yitu.csv）
    - 移除重复的数据文件（yitu_10.csv等）
    - 将示例文件移动到data/目录
    - _Requirements: 5.2, 5.4_


- [ ] 7. 验证CLI和auto_evaluate.py执行一致性
  - [ ] 7.1 创建一致性测试用例
    - 设计相同输入数据的测试用例
    - 分别通过CLI和auto_evaluate.py执行评测
    - 比较两种方式的结果格式、性能指标和分析报告
    - _Requirements: 1.1, 1.2, 1.4_

  - [ ] 7.2 验证性能优化一致性
    - 测试优化版本在两种执行方式中的表现
    - 验证并发控制、内存优化等功能的一致性
    - 确保性能统计和报告格式相同
    - _Requirements: 3.2, 3.3, 3.4_

- [ ] 8. 验证核心功能完整性
  - [ ] 8.1 测试CLI命令功能
    - 验证ai-eval命令的所有子命令正常工作
    - 测试配置管理、评测执行、结果分析等核心功能
    - 确认所有原有功能都可通过CLI访问
    - _Requirements: 8.1, 8.2, 8.3_

  - [ ] 8.2 验证配置和模块加载
    - 测试config.yaml配置文件加载
    - 验证所有服务模块正常导入
    - 检查API客户端连接功能
    - _Requirements: 8.1, 8.4_

- [ ] 9. 优化代码结构
  - [ ] 9.1 整合原版和优化版服务
    - 合并evaluation.py和optimized_evaluation.py的功能
    - 合并api_client.py和optimized_api_client.py的功能
    - 创建统一的服务接口，支持性能优化开关
    - _Requirements: 4.1, 4.3_

  - [ ] 9.2 检查模块依赖关系
    - 分析src/目录下的模块依赖
    - 消除循环依赖和不必要的导入
    - 优化模块接口设计
    - _Requirements: 4.1, 4.2_

  - [ ] 9.3 标准化代码风格
    - 应用black代码格式化
    - 运行mypy类型检查
    - 确保代码符合项目规范
    - _Requirements: 4.4_

- [ ] 10. 更新项目配置
  - [ ] 10.1 完善pyproject.toml配置
    - 确保所有依赖正确声明
    - 更新项目元数据和脚本入口
    - 配置开发工具设置
    - _Requirements: 5.3, 5.4_

  - [ ] 10.2 更新环境配置
    - 检查.env.example文件
    - 更新.gitignore忽略规则
    - 确保开发环境配置正确
    - _Requirements: 5.4_

- [ ] 11. 创建迁移文档
  - [ ] 11.1 记录所有变更
    - 详细记录删除、移动和修改的文件
    - 创建变更日志和迁移指南
    - 说明新的使用方式和最佳实践
    - _Requirements: 10.1, 10.2, 10.3_

  - [ ] 11.2 更新开发文档
    - 创建开发指南文档
    - 更新API文档和用户指南
    - 确保所有文档反映最新结构
    - _Requirements: 10.4, 7.4, 9.4_

- [ ] 12. 最终验证和测试
  - [ ] 12.1 运行完整测试套件
    - 执行所有单元测试和集成测试
    - 验证测试覆盖率达到要求
    - 确保所有测试通过
    - _Requirements: 8.4, 4.4_

  - [ ] 12.2 验证安装和部署
    - 测试pip install -e .安装过程
    - 验证CLI命令在新环境中正常工作
    - 确认项目可以正常分发和部署
    - _Requirements: 5.4, 8.4_

  - [ ] 12.3 端到端一致性验证
    - 使用相同的测试数据分别通过CLI和auto_evaluate.py执行完整评测流程
    - 比较结果文件的内容、格式和性能指标
    - 验证分析报告的一致性
    - 确认两种执行方式产生完全相同的输出
    - _Requirements: 1.1, 1.2, 1.3, 1.4_