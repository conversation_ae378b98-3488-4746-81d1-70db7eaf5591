# Design Document

## Overview

AI模型评测系统代码结构优化设计旨在解决CLI和auto_evaluate.py双执行路径的一致性问题，清理冗余代码、优化项目结构、整理文档，并确保核心功能不受影响。通过系统性的重构和清理，提升代码质量、可维护性和性能一致性。

核心优化目标：
- 统一CLI和auto_evaluate.py的执行引擎，确保结果一致性
- 整合原版和优化版服务，提供统一的性能优化接口
- 清理冗余和重复的脚本文件
- 统一配置和依赖管理
- 优化代码结构和模块组织
- 整理和完善文档体系
- 保持所有核心功能完整性

## Architecture

优化后的项目结构将遵循以下架构原则：

```
ai-model-evaluation/
├── src/                          # 核心源代码
│   └── ai_model_evaluation/
│       ├── models/               # 数据模型
│       ├── services/             # 业务逻辑服务
│       ├── config/               # 配置管理
│       ├── cli/                  # 命令行接口
│       └── utils/                # 工具函数
├── tests/                        # 测试代码
├── docs/                         # 文档
├── examples/                     # 示例和模板
├── scripts/                      # 工具脚本
├── data/                         # 示例数据
└── [配置文件]                    # 项目配置
```

## Components and Interfaces

### 1. 执行路径统一策略

**双执行路径问题分析：**
- CLI通过`ai-eval evaluate`命令执行评测
- auto_evaluate.py直接调用内部服务执行评测
- 两种方式使用不同的引擎和配置，导致结果不一致

**统一解决方案：**
```python
# 创建统一的评测引擎接口
class UnifiedEvaluationEngine:
    def __init__(self, use_optimized: bool = False):
        self.use_optimized = use_optimized
        self._setup_engine()
    
    def _setup_engine(self):
        if self.use_optimized:
            self.engine = OptimizedEvaluationEngine()
        else:
            self.engine = StandardEvaluationEngine()
    
    async def run_evaluation(self, task, providers, models, template, data):
        # 统一的评测执行逻辑
        return await self.engine.execute(task, providers, models, template, data)
```

**文件清理策略：**

需要清理的文件类型：
- 重复的评测脚本（optimized_evaluate.py等，保留auto_evaluate.py作为脚本入口）
- 临时测试文件（test_*.py, quick_*.py等）
- 重复的README文件
- 冗余的配置文件
- 无用的性能测试文件

**保留策略：**
- 保留src/目录下的核心模块
- 保留auto_evaluate.py作为脚本执行入口
- 保留主要的README.md
- 整合performance_config.yaml到主配置文件
- 保留pyproject.toml作为唯一的项目配置

### 2. 代码结构优化

**统一执行引擎设计：**
```python
# 优化前：分离的执行路径
CLI: config_manager -> evaluation_engine -> api_client
Script: direct imports -> optimized_evaluation -> optimized_api_client

# 优化后：统一的执行引擎
Both: config_manager -> unified_evaluation_engine -> adaptive_api_client
```

**服务层重构：**
```python
# 整合原版和优化版服务
src/ai_model_evaluation/services/
├── evaluation.py          # 统一的评测引擎（整合原版和优化版）
├── api_client.py          # 统一的API客户端（支持性能优化）
├── analysis.py            # 统一的分析服务（整合报告生成逻辑）
└── ...
```

**auto_evaluate.py重构：**
```python
# 重构为使用统一引擎的脚本接口
class AutoEvaluator:
    def __init__(self, use_optimized: bool = False):
        # 使用与CLI相同的配置管理器和评测引擎
        self.config_manager = ConfigManager()
        self.evaluation_engine = UnifiedEvaluationEngine(use_optimized)
    
    def run_evaluation(self, ...):
        # 调用与CLI相同的核心逻辑
        return self.evaluation_engine.run_evaluation(...)
```

**依赖管理统一：**
- 移除requirements.txt和requirements-dev.txt
- 统一使用pyproject.toml管理依赖
- 清理poetry相关文件，使用标准pip安装

### 3. 文档整理策略

**文档结构优化：**
```
docs/
├── user_guide.md           # 用户指南
├── api_reference.md        # API参考
├── troubleshooting.md      # 故障排除
└── development.md          # 开发指南（新增）
```

**README整理：**
- 保留主README.md，整合其他README内容
- 移除重复的README文件
- 更新文档链接和示例

### 4. 配置优化

**配置文件统一：**
- 保留config.yaml作为主配置
- 将performance_config.yaml的内容整合到config.yaml中
- 添加性能优化配置节：
```yaml
# config.yaml
providers: [...]
models: [...]
prompt_templates: [...]

# 新增性能配置节
performance:
  global_performance:
    max_concurrent_requests: 500
    request_timeout: 30
  batch_processing:
    batch_size:
      default: 100
      large_dataset: 500
    enable_streaming: true
    memory_optimization:
      enable: true
      max_memory_usage: "2GB"
```

**统一配置加载：**
```python
class ConfigManager:
    def load_config(self):
        # 加载统一配置，包含性能设置
        config = self._load_yaml('config.yaml')
        return ConfigData(
            providers=config['providers'],
            models=config['models'],
            templates=config['prompt_templates'],
            performance=config.get('performance', {})
        )
```

## Data Models

### 文件清理映射

```python
@dataclass
class FileCleanupPlan:
    files_to_remove: List[str]
    files_to_keep: List[str]
    files_to_merge: Dict[str, List[str]]
    directories_to_organize: Dict[str, str]

# 具体清理计划
cleanup_plan = FileCleanupPlan(
    files_to_remove=[
        "optimized_evaluate.py",  # 功能整合到统一引擎
        "performance_comparison.py",
        "performance_analyzer.py",
        "quick_performance_test.py",
        "test_optimized_version.py",
        "test_csv_reading.py",
        "performance_config.yaml",  # 内容整合到config.yaml
        "requirements.txt",
        "requirements-dev.txt",
        "poetry.lock",
        "poetry.toml",
        "README_*.md",
        "UPGRADE_GUIDE.md",
        "analyze_classification_metrics.py",
        "convert_excel_to_csv.py",
        "generate_analysis.py",
        "show_reports.py"
    ],
    files_to_keep=[
        "src/",
        "tests/",
        "docs/",
        "examples/",
        "auto_evaluate.py",  # 保留作为脚本执行入口
        "config.yaml",
        "pyproject.toml",
        "README.md",
        ".gitignore",
        ".env.example"
    ],
    files_to_merge={
        "README.md": ["README_auto_evaluate.md", "README_classification_metrics.md", "README_final_reports.md"]
    }
)
```

## Error Handling

### 清理过程错误处理

1. **文件依赖检查**
   - 在删除文件前检查是否被其他模块引用
   - 确保核心功能模块不被误删

2. **备份策略**
   - 创建清理前的项目备份
   - 记录所有变更操作

3. **验证机制**
   - 清理后运行完整测试套件
   - 验证CLI命令正常工作
   - 确认配置文件可正常加载

## Testing Strategy

### 优化验证测试

1. **功能完整性测试**
   - 验证所有CLI命令正常工作
   - 测试配置加载和模型调用
   - 确认评测流程完整

2. **结构验证测试**
   - 检查模块导入关系
   - 验证文件路径引用
   - 测试安装和部署流程

3. **文档一致性测试**
   - 验证文档中的示例代码
   - 检查配置文件示例
   - 确认API文档准确性

## 实施计划

### Phase 1: 执行引擎统一
1. 创建UnifiedEvaluationEngine类
2. 重构auto_evaluate.py使用统一引擎
3. 验证CLI和脚本执行一致性

### Phase 2: 服务层整合
1. 合并原版和优化版服务
2. 创建统一的API客户端
3. 整合分析和报告生成功能

### Phase 3: 配置统一
1. 整合性能配置到主配置文件
2. 更新ConfigManager支持性能设置
3. 清理依赖管理

### Phase 4: 文件清理
1. 创建项目备份
2. 识别和移除冗余文件
3. 整理目录结构

### Phase 5: 文档整理
1. 合并README文件
2. 更新文档内容
3. 创建统一使用指南

### Phase 6: 验证测试
1. 运行一致性测试
2. 验证性能优化效果
3. 检查文档准确性

## 迁移指南

### 变更记录

**删除的文件：**
- 所有根目录下的独立脚本文件
- 重复的配置和README文件
- 临时测试文件

**保留的功能：**
- 所有核心评测功能
- CLI命令接口
- 配置管理系统
- 文档和示例

**新的使用方式：**
```bash
# CLI方式（保持不变）
ai-eval evaluate --input data.csv --models model1,model2 --template template1
ai-eval analyze results.csv

# 脚本方式（使用统一引擎）
python auto_evaluate.py  # 内部使用与CLI相同的引擎

# 两种方式现在产生完全相同的结果和性能
```

**统一引擎使用示例：**
```python
# auto_evaluate.py中的新实现
from src.ai_model_evaluation.config.manager import ConfigManager
from src.ai_model_evaluation.services.evaluation import UnifiedEvaluationEngine

class AutoEvaluator:
    def __init__(self, use_optimized: bool = False):
        self.config_manager = ConfigManager()
        self.evaluation_engine = UnifiedEvaluationEngine(use_optimized)
    
    def run_evaluation(self, input_file, template, models, output_name=None):
        # 使用与CLI完全相同的逻辑
        config_data = self.config_manager.load_config()
        # ... 其余逻辑与CLI保持一致
```

### 兼容性保证

- 所有CLI命令保持不变
- 配置文件格式保持兼容
- API接口保持稳定
- 核心功能完全保留

## 质量保证

### 代码质量标准
- 遵循PEP 8代码风格
- 保持90%+测试覆盖率
- 通过类型检查
- 文档完整性检查

### 维护流程
- 定期代码审查
- 自动化质量检查
- 持续集成验证
- 文档同步更新